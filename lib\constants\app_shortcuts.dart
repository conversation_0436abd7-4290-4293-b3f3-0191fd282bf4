import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// نظام اختصارات لوحة المفاتيح للتطبيق
class AppShortcuts {
  // اختصارات عامة
  static final LogicalKeySet save = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyS,
  );

  static final LogicalKeySet newDocument = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyN,
  );

  static final LogicalKeySet search = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyF,
  );

  static final LogicalKeySet refresh = LogicalKeySet(LogicalKeyboardKey.f5);

  static final LogicalKeySet print = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyP,
  );

  static final LogicalKeySet copy = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyC,
  );

  static final LogicalKeySet paste = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyV,
  );

  static final LogicalKeySet undo = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyZ,
  );

  static final LogicalKeySet redo = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyY,
  );

  static final LogicalKeySet selectAll = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyA,
  );

  static final LogicalKeySet delete = LogicalKeySet(LogicalKeyboardKey.delete);

  static final LogicalKeySet escape = LogicalKeySet(LogicalKeyboardKey.escape);

  static final LogicalKeySet enter = LogicalKeySet(LogicalKeyboardKey.enter);

  // اختصارات خاصة بالمحاسبة
  static final LogicalKeySet newInvoice = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.keyI,
  );

  static final LogicalKeySet newCustomer = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.keyC,
  );

  static final LogicalKeySet newSupplier = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.keyS,
  );

  static final LogicalKeySet newItem = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.keyP,
  );

  static final LogicalKeySet newAccount = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.keyA,
  );

  static final LogicalKeySet journalEntry = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.shift,
    LogicalKeyboardKey.keyJ,
  );

  static final LogicalKeySet reports = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyR,
  );

  static final LogicalKeySet backup = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyB,
  );

  static final LogicalKeySet settings = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.comma,
  );

  // اختصارات التنقل
  static final LogicalKeySet dashboard = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.keyH,
  );

  static final LogicalKeySet accounts = LogicalKeySet(
    LogicalKeyboardKey.alt,
    LogicalKeyboardKey.keyA,
  );

  static final LogicalKeySet invoices = LogicalKeySet(
    LogicalKeyboardKey.alt,
    LogicalKeyboardKey.keyI,
  );

  static final LogicalKeySet customers = LogicalKeySet(
    LogicalKeyboardKey.alt,
    LogicalKeyboardKey.keyC,
  );

  static final LogicalKeySet suppliers = LogicalKeySet(
    LogicalKeyboardKey.alt,
    LogicalKeyboardKey.keyS,
  );

  static final LogicalKeySet items = LogicalKeySet(
    LogicalKeyboardKey.alt,
    LogicalKeyboardKey.keyP,
  );

  static final LogicalKeySet warehouse = LogicalKeySet(
    LogicalKeyboardKey.alt,
    LogicalKeyboardKey.keyW,
  );

  // اختصارات الأرقام (للحاسبة السريعة)
  static final LogicalKeySet calculator = LogicalKeySet(
    LogicalKeyboardKey.control,
    LogicalKeyboardKey.equal,
  );

  // خريطة الاختصارات مع أوصافها
  static final Map<LogicalKeySet, String> shortcutDescriptions = {
    save: 'حفظ (Ctrl+S)',
    newDocument: 'جديد (Ctrl+N)',
    search: 'بحث (Ctrl+F)',
    refresh: 'تحديث (F5)',
    print: 'طباعة (Ctrl+P)',
    copy: 'نسخ (Ctrl+C)',
    paste: 'لصق (Ctrl+V)',
    undo: 'تراجع (Ctrl+Z)',
    redo: 'إعادة (Ctrl+Y)',
    selectAll: 'تحديد الكل (Ctrl+A)',
    delete: 'حذف (Delete)',
    escape: 'إلغاء (Esc)',
    enter: 'موافق (Enter)',
    newInvoice: 'فاتورة جديدة (Ctrl+Shift+I)',
    newCustomer: 'عميل جديد (Ctrl+Shift+C)',
    newSupplier: 'مورد جديد (Ctrl+Shift+S)',
    newItem: 'صنف جديد (Ctrl+Shift+P)',
    newAccount: 'حساب جديد (Ctrl+Shift+A)',
    journalEntry: 'قيد يومية (Ctrl+Shift+J)',
    reports: 'التقارير (Ctrl+R)',
    backup: 'نسخ احتياطي (Ctrl+B)',
    settings: 'الإعدادات (Ctrl+,)',
    dashboard: 'الرئيسية (Ctrl+H)',
    accounts: 'الحسابات (Alt+A)',
    invoices: 'الفواتير (Alt+I)',
    customers: 'العملاء (Alt+C)',
    suppliers: 'الموردين (Alt+S)',
    items: 'الأصناف (Alt+P)',
    warehouse: 'المخزن (Alt+W)',
    calculator: 'الحاسبة (Ctrl+=)',
  };

  /// إنشاء مكون اختصار لوحة المفاتيح
  static Widget shortcut({
    required LogicalKeySet keySet,
    required VoidCallback onPressed,
    required Widget child,
    String? description,
  }) {
    return Shortcuts(
      shortcuts: {keySet: CallbackIntent(onPressed)},
      child: Actions(
        actions: {
          CallbackIntent: CallbackAction<CallbackIntent>(
            onInvoke: (intent) => intent.callback(),
          ),
        },
        child: Focus(autofocus: false, child: child),
      ),
    );
  }

  /// إنشاء مجموعة اختصارات متعددة
  static Widget multipleShortcuts({
    required Map<LogicalKeySet, VoidCallback> shortcuts,
    required Widget child,
  }) {
    final shortcutMap = <LogicalKeySet, Intent>{};
    final actionMap = <Type, Action<Intent>>{};

    shortcuts.forEach((keySet, callback) {
      final intent = CallbackIntent(callback);
      shortcutMap[keySet] = intent;
      actionMap[CallbackIntent] = CallbackAction<CallbackIntent>(
        onInvoke: (intent) => intent.callback(),
      );
    });

    return Shortcuts(
      shortcuts: shortcutMap,
      child: Actions(
        actions: actionMap,
        child: Focus(autofocus: false, child: child),
      ),
    );
  }

  /// عرض قائمة الاختصارات المتاحة
  static Widget shortcutsHelp(BuildContext context) {
    return AlertDialog(
      title: const Text('اختصارات لوحة المفاتيح'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: ListView.builder(
          itemCount: shortcutDescriptions.length,
          itemBuilder: (context, index) {
            final entry = shortcutDescriptions.entries.elementAt(index);
            return ListTile(title: Text(entry.value), dense: true);
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  /// التحقق من توفر اختصار معين
  static bool isShortcutPressed(KeyEvent event, LogicalKeySet shortcut) {
    return shortcut.accepts(event, HardwareKeyboard.instance);
  }

  /// الحصول على وصف اختصار
  static String? getShortcutDescription(LogicalKeySet shortcut) {
    return shortcutDescriptions[shortcut];
  }

  /// تحويل اختصار إلى نص قابل للقراءة
  static String shortcutToString(LogicalKeySet shortcut) {
    final keys = shortcut.keys.toList();
    final keyNames = <String>[];

    for (final key in keys) {
      if (key == LogicalKeyboardKey.control) {
        keyNames.add('Ctrl');
      } else if (key == LogicalKeyboardKey.shift) {
        keyNames.add('Shift');
      } else if (key == LogicalKeyboardKey.alt) {
        keyNames.add('Alt');
      } else if (key == LogicalKeyboardKey.meta) {
        keyNames.add('Cmd');
      } else {
        keyNames.add(key.keyLabel.toUpperCase());
      }
    }

    return keyNames.join('+');
  }
}

/// Intent مخصص للاستدعاءات
class CallbackIntent extends Intent {
  final VoidCallback callback;

  const CallbackIntent(this.callback);
}
