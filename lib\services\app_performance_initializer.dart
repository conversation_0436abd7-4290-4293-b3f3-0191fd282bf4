import 'package:flutter/foundation.dart';
import '../services/performance_service.dart';
import '../services/cache_service.dart';
import '../services/connection_pool_service.dart';
import '../services/memory_management_service.dart';
import '../services/logging_service.dart';

/// مهيئ تحسينات الأداء للتطبيق
/// يدير تهيئة وإعداد جميع خدمات تحسين الأداء
class AppPerformanceInitializer {
  static bool _isInitialized = false;
  static bool _isShutdown = false;

  /// تهيئة جميع خدمات تحسين الأداء
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info(
        'بدء تهيئة تحسينات الأداء للتطبيق',
        category: 'AppPerformance',
      );

      // تهيئة خدمة الأداء الرئيسية
      await PerformanceService().initialize();

      // إعداد معالجات الأخطاء
      _setupErrorHandlers();

      // إعداد مراقبة دورة حياة التطبيق
      _setupAppLifecycleMonitoring();

      // تحسين الأداء الأولي
      await _performInitialOptimizations();

      _isInitialized = true;

      LoggingService.info(
        'تم تهيئة تحسينات الأداء بنجاح',
        category: 'AppPerformance',
      );
    } catch (e) {
      LoggingService.error(
        'فشل في تهيئة تحسينات الأداء',
        category: 'AppPerformance',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إعداد معالجات الأخطاء
  static void _setupErrorHandlers() {
    // معالج أخطاء Flutter
    FlutterError.onError = (FlutterErrorDetails details) {
      LoggingService.error(
        'خطأ في Flutter',
        category: 'FlutterError',
        data: {
          'exception': details.exception.toString(),
          'stack': details.stack.toString(),
          'library': details.library,
          'context': details.context?.toString(),
        },
      );

      // تسجيل حدث أداء للخطأ
      PerformanceService().recordEvent(PerformanceEvent(
        name: 'flutter_error',
        timestamp: DateTime.now(),
        duration: Duration.zero,
        category: 'error',
        metadata: {
          'exception': details.exception.toString(),
          'library': details.library,
        },
      ));
    };

    // معالج الأخطاء غير المعالجة في المنطقة
    if (!kIsWeb) {
      // يمكن إضافة معالجات إضافية هنا للمنصات المختلفة
    }
  }

  /// إعداد مراقبة دورة حياة التطبيق
  static void _setupAppLifecycleMonitoring() {
    // تسجيل أحداث دورة حياة التطبيق
    PerformanceService().recordEvent(PerformanceEvent(
      name: 'app_initialized',
      timestamp: DateTime.now(),
      duration: Duration.zero,
      category: 'lifecycle',
    ));
  }

  /// تحسينات الأداء الأولية
  static Future<void> _performInitialOptimizations() async {
    // تنظيف التخزين المؤقت من الجلسات السابقة
    CacheService().removeExpired();

    // تحسين إعدادات الذاكرة
    final memoryService = MemoryManagementService();
    if (!memoryService.isMonitoring) {
      memoryService.startMonitoring();
    }

    // تحسين مجموعة اتصالات قاعدة البيانات
    await ConnectionPoolService().initialize();

    LoggingService.debug(
      'تم تنفيذ التحسينات الأولية',
      category: 'AppPerformance',
    );
  }

  /// تحسين الأداء الدوري
  static Future<void> performPeriodicOptimization() async {
    if (!_isInitialized || _isShutdown) return;

    try {
      LoggingService.debug(
        'بدء التحسين الدوري للأداء',
        category: 'AppPerformance',
      );

      // تحسين التخزين المؤقت
      await _optimizeCache();

      // تحسين الذاكرة
      await _optimizeMemory();

      // تحسين قاعدة البيانات
      await _optimizeDatabase();

      // تنظيف السجلات القديمة
      await _cleanupOldLogs();

      LoggingService.debug(
        'تم التحسين الدوري بنجاح',
        category: 'AppPerformance',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في التحسين الدوري',
        category: 'AppPerformance',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحسين التخزين المؤقت
  static Future<void> _optimizeCache() async {
    final cacheService = CacheService();
    final stats = cacheService.getStatistics();

    // تنظيف القيم المنتهية الصلاحية
    cacheService.removeExpired();

    // إذا كان استخدام الذاكرة مرتفع، قم بتنظيف إضافي
    if (stats.memoryUsageKB > 10240) { // 10 MB
      // تنظيف القيم الأقل استخداماً
      // هذا سيتم تلقائياً بواسطة CacheService عند الحاجة
    }

    LoggingService.debug(
      'تحسين التخزين المؤقت',
      category: 'AppPerformance',
      data: {
        'totalEntries': stats.totalEntries,
        'memoryUsageKB': stats.memoryUsageKB,
      },
    );
  }

  /// تحسين الذاكرة
  static Future<void> _optimizeMemory() async {
    final memoryService = MemoryManagementService();
    final stats = memoryService.getStatistics();

    // إذا كان مستوى ضغط الذاكرة مرتفع، قم بتحسين إضافي
    if (stats.pressureLevel != MemoryPressureLevel.normal) {
      // سيتم التحسين تلقائياً بواسطة MemoryManagementService
      LoggingService.warning(
        'مستوى ضغط الذاكرة مرتفع',
        category: 'AppPerformance',
        data: {
          'pressureLevel': stats.pressureLevel.name,
          'currentUsageMB': stats.currentUsageMB,
        },
      );
    }
  }

  /// تحسين قاعدة البيانات
  static Future<void> _optimizeDatabase() async {
    final poolService = ConnectionPoolService();
    final stats = poolService.getStatistics();

    // مراقبة استخدام مجموعة الاتصالات
    if (stats.utilizationRatio > 0.8) {
      LoggingService.warning(
        'استخدام مرتفع لمجموعة اتصالات قاعدة البيانات',
        category: 'AppPerformance',
        data: {
          'utilizationRatio': stats.utilizationRatio,
          'activeConnections': stats.activeConnections,
          'totalConnections': stats.totalConnections,
        },
      );
    }
  }

  /// تنظيف السجلات القديمة
  static Future<void> _cleanupOldLogs() async {
    // يمكن إضافة منطق تنظيف السجلات القديمة هنا
    // مثل حذف سجلات الأداء الأقدم من أسبوع
  }

  /// الحصول على تقرير أداء شامل
  static Map<String, dynamic> getPerformanceReport() {
    if (!_isInitialized) {
      return {'error': 'خدمات الأداء غير مهيأة'};
    }

    try {
      final performanceStats = PerformanceService().getStatistics();
      final memoryStats = MemoryManagementService().getStatistics();
      final cacheStats = CacheService().getStatistics();
      final dbStats = ConnectionPoolService().getStatistics();

      return {
        'timestamp': DateTime.now().toIso8601String(),
        'performance': {
          'totalEvents': performanceStats.totalEvents,
          'totalMetrics': performanceStats.totalMetrics,
          'averageEventDuration': performanceStats.averageEventDuration.inMilliseconds,
          'isOptimizationEnabled': performanceStats.isOptimizationEnabled,
        },
        'memory': {
          'currentUsageMB': memoryStats.currentUsageMB,
          'peakUsageMB': memoryStats.peakUsageMB,
          'pressureLevel': memoryStats.pressureLevel.name,
          'isMonitoring': memoryStats.isMonitoring,
        },
        'cache': {
          'totalEntries': cacheStats.totalEntries,
          'memoryUsageKB': cacheStats.memoryUsageKB,
          'averageAccessCount': cacheStats.averageAccessCount,
        },
        'database': {
          'totalConnections': dbStats.totalConnections,
          'activeConnections': dbStats.activeConnections,
          'utilizationRatio': dbStats.utilizationRatio,
          'isInitialized': dbStats.isInitialized,
        },
      };
    } catch (e) {
      return {
        'error': 'خطأ في إنشاء تقرير الأداء: ${e.toString()}',
      };
    }
  }

  /// تشخيص مشاكل الأداء
  static List<String> diagnosePerformanceIssues() {
    final issues = <String>[];

    if (!_isInitialized) {
      issues.add('خدمات الأداء غير مهيأة');
      return issues;
    }

    try {
      // فحص الذاكرة
      final memoryStats = MemoryManagementService().getStatistics();
      if (memoryStats.pressureLevel == MemoryPressureLevel.critical) {
        issues.add('استخدام الذاكرة في المستوى الحرج (${memoryStats.currentUsageMB.toStringAsFixed(1)} MB)');
      } else if (memoryStats.pressureLevel == MemoryPressureLevel.warning) {
        issues.add('استخدام الذاكرة مرتفع (${memoryStats.currentUsageMB.toStringAsFixed(1)} MB)');
      }

      // فحص التخزين المؤقت
      final cacheStats = CacheService().getStatistics();
      if (cacheStats.memoryUsageKB > 20480) { // 20 MB
        issues.add('استخدام التخزين المؤقت مرتفع (${cacheStats.memoryUsageKB.toStringAsFixed(1)} KB)');
      }

      // فحص قاعدة البيانات
      final dbStats = ConnectionPoolService().getStatistics();
      if (dbStats.utilizationRatio > 0.9) {
        issues.add('استخدام مرتفع جداً لمجموعة اتصالات قاعدة البيانات (${(dbStats.utilizationRatio * 100).toStringAsFixed(1)}%)');
      }

      if (issues.isEmpty) {
        issues.add('لا توجد مشاكل أداء واضحة');
      }
    } catch (e) {
      issues.add('خطأ في تشخيص الأداء: ${e.toString()}');
    }

    return issues;
  }

  /// إيقاف جميع خدمات الأداء
  static Future<void> shutdown() async {
    if (!_isInitialized || _isShutdown) return;

    try {
      LoggingService.info(
        'بدء إيقاف خدمات الأداء',
        category: 'AppPerformance',
      );

      // إيقاف خدمة الأداء الرئيسية
      await PerformanceService().shutdown();

      _isShutdown = true;
      _isInitialized = false;

      LoggingService.info(
        'تم إيقاف خدمات الأداء بنجاح',
        category: 'AppPerformance',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إيقاف خدمات الأداء',
        category: 'AppPerformance',
        data: {'error': e.toString()},
      );
    }
  }

  /// هل الخدمات مهيأة؟
  static bool get isInitialized => _isInitialized;

  /// هل تم إيقاف الخدمات؟
  static bool get isShutdown => _isShutdown;
}
