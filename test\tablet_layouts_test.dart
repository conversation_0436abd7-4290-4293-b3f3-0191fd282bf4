import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/responsive/responsive.dart';

void main() {
  group('اختبارات التخطيطات المحسنة للأجهزة اللوحية', () {
    testWidgets('اختبار TabletMasterDetailLayout', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(768, 1024));

      await tester.pumpWidget(
        MaterialApp(
          home: ScreenInfoProvider(
            child: TabletMasterDetailLayout(
              title: 'اختبار التخطيط',
              sidebar: Container(
                key: const Key('sidebar'),
                child: const Text('الشريط الجانبي'),
              ),
              content: Container(
                key: const Key('content'),
                child: const Text('المحتوى الرئيسي'),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود العناصر
      expect(find.byKey(const Key('sidebar')), findsOneWidget);
      expect(find.byKey(const Key('content')), findsOneWidget);
      expect(find.text('اختبار التخطيط'), findsOneWidget);

      // اختبار زر إخفاء/إظهار الشريط الجانبي
      final menuButton = find.byIcon(Icons.menu);
      expect(menuButton, findsOneWidget);

      await tester.tap(menuButton);
      await tester.pumpAndSettle();

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار TabletGridLayout', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(768, 1024));

      final testItems = List.generate(
        6,
        (index) =>
            Container(key: Key('grid_item_$index'), child: Text('Item $index')),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: ScreenInfoProvider(
            child: Scaffold(
              body: TabletGridLayout(
                title: 'شبكة الاختبار',
                items: testItems,
                columns: 3,
                actions: [
                  IconButton(
                    key: const Key('action_button'),
                    icon: const Icon(Icons.add),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود العناصر
      expect(find.text('شبكة الاختبار'), findsOneWidget);
      expect(find.byKey(const Key('action_button')), findsOneWidget);

      for (int i = 0; i < 6; i++) {
        expect(find.byKey(Key('grid_item_$i')), findsOneWidget);
      }

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار TabletListDetailLayout', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(768, 1024));

      final listItems = List.generate(
        5,
        (index) => Container(
          key: Key('list_item_$index'),
          child: Text('List Item $index'),
        ),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: ScreenInfoProvider(
            child: Scaffold(
              body: TabletListDetailLayout(
                title: 'قائمة الاختبار',
                listItems: listItems,
                detailBuilder: (index) => Container(
                  key: Key('detail_$index'),
                  child: Text('Detail for item $index'),
                ),
                emptyDetail: Container(
                  key: const Key('empty_detail'),
                  child: const Text('اختر عنصراً'),
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود العناصر
      expect(find.text('قائمة الاختبار'), findsOneWidget);
      expect(find.byKey(const Key('empty_detail')), findsOneWidget);

      for (int i = 0; i < 5; i++) {
        expect(find.byKey(Key('list_item_$i')), findsOneWidget);
      }

      // اختبار النقر على عنصر من القائمة
      await tester.tap(find.byKey(const Key('list_item_2')));
      await tester.pumpAndSettle();

      // التحقق من ظهور التفاصيل
      expect(find.byKey(const Key('detail_2')), findsOneWidget);
      expect(find.byKey(const Key('empty_detail')), findsNothing);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار TabletFormLayout', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(768, 1024));

      final formFields = [
        TextFormField(
          key: const Key('field_1'),
          decoration: const InputDecoration(labelText: 'الحقل الأول'),
        ),
        TextFormField(
          key: const Key('field_2'),
          decoration: const InputDecoration(labelText: 'الحقل الثاني'),
        ),
        TextFormField(
          key: const Key('field_3'),
          decoration: const InputDecoration(labelText: 'الحقل الثالث'),
        ),
        TextFormField(
          key: const Key('field_4'),
          decoration: const InputDecoration(labelText: 'الحقل الرابع'),
        ),
      ];

      final actions = [
        ElevatedButton(
          key: const Key('save_button'),
          onPressed: () {},
          child: const Text('حفظ'),
        ),
        OutlinedButton(
          key: const Key('cancel_button'),
          onPressed: () {},
          child: const Text('إلغاء'),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: ScreenInfoProvider(
            child: Scaffold(
              body: TabletFormLayout(
                title: 'نموذج الاختبار',
                subtitle: 'وصف النموذج',
                formFields: formFields,
                actions: actions,
                columns: 2,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود العناصر
      expect(find.text('نموذج الاختبار'), findsOneWidget);
      expect(find.text('وصف النموذج'), findsOneWidget);
      expect(find.byKey(const Key('save_button')), findsOneWidget);
      expect(find.byKey(const Key('cancel_button')), findsOneWidget);

      for (int i = 1; i <= 4; i++) {
        expect(find.byKey(Key('field_$i')), findsOneWidget);
      }

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار التكيف مع أحجام الشاشات المختلفة', (
      WidgetTester tester,
    ) async {
      final widget = MaterialApp(
        home: ScreenInfoProvider(
          child: ResponsiveLayout(
            mobile: Container(
              key: const Key('mobile_layout'),
              child: const Text('Mobile'),
            ),
            tablet: TabletMasterDetailLayout(
              sidebar: Container(
                key: const Key('tablet_sidebar'),
                child: const Text('Tablet Sidebar'),
              ),
              content: Container(
                key: const Key('tablet_content'),
                child: const Text('Tablet Content'),
              ),
            ),
          ),
        ),
      );

      // اختبار على الهاتف المحمول
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(widget);
      await tester.pumpAndSettle();

      expect(find.byKey(const Key('mobile_layout')), findsOneWidget);
      expect(find.byKey(const Key('tablet_sidebar')), findsNothing);

      // اختبار على الجهاز اللوحي
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(widget);
      await tester.pumpAndSettle();

      expect(find.byKey(const Key('mobile_layout')), findsNothing);
      expect(find.byKey(const Key('tablet_sidebar')), findsOneWidget);
      expect(find.byKey(const Key('tablet_content')), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار الأداء مع التخطيطات المعقدة', (
      WidgetTester tester,
    ) async {
      await tester.binding.setSurfaceSize(const Size(768, 1024));

      final stopwatch = Stopwatch()..start();

      // إنشاء تخطيط معقد مع عدد كبير من العناصر
      final largeItemList = List.generate(
        100,
        (index) => SizedBox(
          key: Key('large_item_$index'),
          height: 50,
          child: Text('Item $index'),
        ),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: ScreenInfoProvider(
            child: Scaffold(
              body: TabletListDetailLayout(
                listItems: largeItemList,
                detailBuilder: (index) => Text('Detail $index'),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      stopwatch.stop();

      // التحقق من أن الأداء مقبول (أقل من ثانيتين)
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));

      // التحقق من وجود العناصر
      expect(find.byKey(const Key('large_item_0')), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });
  });

  group('اختبارات التفاعل مع التخطيطات', () {
    testWidgets('اختبار التفاعل مع TabletMasterDetailLayout', (
      WidgetTester tester,
    ) async {
      await tester.binding.setSurfaceSize(const Size(768, 1024));

      bool sidebarToggled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: ScreenInfoProvider(
            child: StatefulBuilder(
              builder: (context, setState) {
                return TabletMasterDetailLayout(
                  title: 'تخطيط تفاعلي',
                  sidebar: GestureDetector(
                    key: const Key('interactive_sidebar'),
                    onTap: () {
                      setState(() {
                        sidebarToggled = true;
                      });
                    },
                    child: Text(sidebarToggled ? 'تم النقر' : 'انقر هنا'),
                  ),
                  content: const Text('المحتوى'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // اختبار النقر على الشريط الجانبي
      await tester.tap(find.byKey(const Key('interactive_sidebar')));
      await tester.pumpAndSettle();

      expect(find.text('تم النقر'), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });
  });
}
