import '../database/database_helper.dart';
import '../models/supplier.dart';
import '../constants/app_constants.dart';
import 'validation_service.dart';
import '../exceptions/validation_exception.dart';

class SupplierService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<Supplier>> getAllSuppliers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  Future<List<Supplier>> getActiveSuppliers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  Future<Supplier?> getSupplierById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Supplier.fromMap(maps.first);
    }
    return null;
  }

  Future<Supplier?> getSupplierByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Supplier.fromMap(maps.first);
    }
    return null;
  }

  Future<int> insertSupplier(Supplier supplier) async {
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات باستخدام ValidationService
    final validation = ValidationService.validateSupplier(
      code: supplier.code,
      name: supplier.name,
      phone: supplier.phone,
      email: supplier.email,
      address: supplier.address,
    );

    if (!validation.isValid) {
      throw ValidationException(validation.errorMessage!);
    }

    // التحقق من عدم تكرار الكود
    final existingSupplier = await getSupplierByCode(supplier.code);
    if (existingSupplier != null) {
      throw ValidationException.duplicate('كود المورد', supplier.code);
    }

    // تنظيف البيانات
    final cleanSupplier = supplier.copyWith(
      name: ValidationService.sanitizeText(supplier.name),
      phone: supplier.phone != null
          ? ValidationService.sanitizeText(supplier.phone!)
          : null,
      email: supplier.email != null
          ? ValidationService.sanitizeText(supplier.email!)
          : null,
      address: supplier.address != null
          ? ValidationService.sanitizeText(supplier.address!)
          : null,
    );

    final supplierData = cleanSupplier.toMap();
    supplierData.remove('id'); // إزالة المعرف للإدراج التلقائي

    return await db.insert(AppConstants.suppliersTable, supplierData);
  }

  Future<int> updateSupplier(Supplier supplier) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم تكرار الكود مع موردين آخرين
    final existingSupplier = await getSupplierByCode(supplier.code);
    if (existingSupplier != null && existingSupplier.id != supplier.id) {
      throw Exception('كود المورد موجود مسبقاً');
    }

    final supplierData = supplier.copyWith(updatedAt: DateTime.now()).toMap();

    return await db.update(
      AppConstants.suppliersTable,
      supplierData,
      where: 'id = ?',
      whereArgs: [supplier.id],
    );
  }

  Future<int> deleteSupplier(int id) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم وجود فواتير للمورد
    final invoices = await db.query(
      AppConstants.invoicesTable,
      where: 'supplier_id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (invoices.isNotEmpty) {
      throw Exception('لا يمكن حذف المورد لوجود فواتير مرتبطة به');
    }

    return await db.delete(
      AppConstants.suppliersTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateSupplierBalance(
    int supplierId,
    double amount,
    bool isDebit,
  ) async {
    final db = await _databaseHelper.database;
    final supplier = await getSupplierById(supplierId);

    if (supplier == null) {
      throw Exception('المورد غير موجود');
    }

    double newBalance = supplier.balance;

    // الموردين: الدائن يزيد الرصيد (دين علينا)، المدين يقلل الرصيد (دفع للمورد)
    newBalance += isDebit ? -amount : amount;

    await db.update(
      AppConstants.suppliersTable,
      {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [supplierId],
    );
  }

  Future<List<Supplier>> searchSuppliers(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      where: '(name LIKE ? OR code LIKE ? OR phone LIKE ?) AND is_active = 1',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  Future<String> generateSupplierCode() async {
    final db = await _databaseHelper.database;

    // استعلام محسن للحصول على أكبر رقم كود
    final result = await db.rawQuery('''
      SELECT MAX(CAST(SUBSTR(code, 2) AS INTEGER)) as max_number
      FROM ${AppConstants.suppliersTable}
      WHERE code LIKE 'S%' AND code GLOB 'S[0-9]*'
    ''');

    final maxNumber = result.first['max_number'] as int? ?? 0;
    return 'S${(maxNumber + 1).toString().padLeft(4, '0')}';
  }

  Future<List<Supplier>> getSuppliersWithDebt() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      where: 'balance < 0 AND is_active = 1',
      orderBy: 'balance ASC',
    );

    return List.generate(maps.length, (i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  Future<List<Supplier>> getSuppliersWithCredit() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.suppliersTable,
      where: 'balance > 0 AND is_active = 1',
      orderBy: 'balance DESC',
    );

    return List.generate(maps.length, (i) {
      return Supplier.fromMap(maps[i]);
    });
  }

  Future<Map<String, double>> getSuppliersSummary() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_suppliers,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_suppliers,
        COUNT(CASE WHEN balance < 0 THEN 1 END) as suppliers_with_debt,
        COUNT(CASE WHEN balance > 0 THEN 1 END) as suppliers_with_credit,
        SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END) as total_debt,
        SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END) as total_credit
      FROM ${AppConstants.suppliersTable}
    ''');

    final row = result.first;
    return {
      'total_suppliers': (row['total_suppliers'] as num?)?.toDouble() ?? 0.0,
      'active_suppliers': (row['active_suppliers'] as num?)?.toDouble() ?? 0.0,
      'suppliers_with_debt':
          (row['suppliers_with_debt'] as num?)?.toDouble() ?? 0.0,
      'suppliers_with_credit':
          (row['suppliers_with_credit'] as num?)?.toDouble() ?? 0.0,
      'total_debt': (row['total_debt'] as num?)?.toDouble() ?? 0.0,
      'total_credit': (row['total_credit'] as num?)?.toDouble() ?? 0.0,
    };
  }
}
