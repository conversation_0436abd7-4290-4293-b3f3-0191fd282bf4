
import 'data_sanitization_service.dart';
import 'logging_service.dart';

// ===============================
// مستويات الحماية
// ===============================

enum ProtectionLevel {
  basic, // تنظيف أساسي
  standard, // تنظيف متوسط مع فحص أمني
  strict, // تنظيف صارم مع تسجيل المحاولات المشبوهة
}

/// خدمة حماية البيانات الشاملة
/// تجمع بين التنظيف والتحقق والحماية من التهديدات
class DataProtectionService {
  // ===============================
  // تنظيف محمي للبيانات
  // ===============================

  /// تنظيف النص مع مستوى حماية محدد
  static String protectedSanitizeText(
    String? input,
    String context, {
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    if (input == null) return '';

    switch (level) {
      case ProtectionLevel.basic:
        return DataSanitizationService.sanitizeText(input);

      case ProtectionLevel.standard:
        if (DataSanitizationService.containsSuspiciousContent(input)) {
          _logSecurityEvent('محتوى مشبوه في $context', input);
        }
        return DataSanitizationService.sanitizeForXSS(
          DataSanitizationService.sanitizeForSQL(input),
        );

      case ProtectionLevel.strict:
        return DataSanitizationService.sanitizeWithLogging(input, context);
    }
  }

  /// تنظيف محمي للأكواد
  static String protectedSanitizeCode(
    String? input,
    String codeType, {
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    if (input == null) return '';

    String cleaned = DataSanitizationService.sanitizeCode(input);

    if (level == ProtectionLevel.strict) {
      // فحص إضافي للأكواد
      if (cleaned.length > 20) {
        _logSecurityEvent('كود طويل جداً في $codeType', input);
        cleaned = cleaned.substring(0, 20);
      }

      if (cleaned.contains(RegExp(r'[^A-Z0-9]'))) {
        _logSecurityEvent('كود يحتوي على أحرف غير مسموحة في $codeType', input);
        cleaned = cleaned.replaceAll(RegExp(r'[^A-Z0-9]'), '');
      }
    }

    return cleaned;
  }

  // ===============================
  // حماية البيانات المالية
  // ===============================

  /// تنظيف محمي للمبالغ المالية
  static String protectedSanitizeAmount(
    String? input, {
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    if (input == null) return '';

    String cleaned = DataSanitizationService.sanitizeAmount(input);

    if (level == ProtectionLevel.strict) {
      // فحص المبالغ الكبيرة جداً
      final amount = double.tryParse(cleaned);
      if (amount != null && amount > 1000000000) {
        _logSecurityEvent('محاولة إدخال مبلغ كبير جداً', input);
        cleaned = '999999999.99';
      }
    }

    return cleaned;
  }

  // ===============================
  // حماية بيانات الاتصال
  // ===============================

  /// تنظيف محمي لرقم الهاتف
  static String protectedSanitizePhone(
    String? input, {
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    if (input == null) return '';

    String cleaned = DataSanitizationService.sanitizePhoneNumber(input);

    if (level == ProtectionLevel.strict) {
      // فحص أرقام الهاتف المشبوهة
      if (cleaned.length > 15) {
        _logSecurityEvent('رقم هاتف طويل جداً', input);
        cleaned = cleaned.substring(0, 15);
      }

      if (cleaned.startsWith('00000') || cleaned == '1234567890') {
        _logSecurityEvent('رقم هاتف وهمي محتمل', input);
      }
    }

    return cleaned;
  }

  /// تنظيف محمي للبريد الإلكتروني
  static String protectedSanitizeEmail(
    String? input, {
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    if (input == null) return '';

    String cleaned = DataSanitizationService.sanitizeEmail(input);

    if (level == ProtectionLevel.strict) {
      // فحص البريد الإلكتروني المشبوه
      if (cleaned.length > 100) {
        _logSecurityEvent('بريد إلكتروني طويل جداً', input);
        cleaned = cleaned.substring(0, 100);
      }

      if (cleaned.contains('..') ||
          cleaned.startsWith('.') ||
          cleaned.endsWith('.')) {
        _logSecurityEvent('بريد إلكتروني بتنسيق مشبوه', input);
      }
    }

    return cleaned;
  }

  // ===============================
  // حماية شاملة للكيانات
  // ===============================

  /// حماية شاملة لبيانات الحساب
  static Map<String, String> protectedSanitizeAccount({
    required String? code,
    required String? name,
    String? description,
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    return {
      'code': protectedSanitizeCode(code, 'كود الحساب', level: level),
      'name': protectedSanitizeText(name, 'اسم الحساب', level: level),
      'description': protectedSanitizeText(
        description,
        'وصف الحساب',
        level: level,
      ),
    };
  }

  /// حماية شاملة لبيانات العميل
  static Map<String, String> protectedSanitizeCustomer({
    required String? code,
    required String? name,
    String? phone,
    String? email,
    String? address,
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    return {
      'code': protectedSanitizeCode(code, 'كود العميل', level: level),
      'name': protectedSanitizeText(name, 'اسم العميل', level: level),
      'phone': protectedSanitizePhone(phone, level: level),
      'email': protectedSanitizeEmail(email, level: level),
      'address': protectedSanitizeText(address, 'عنوان العميل', level: level),
    };
  }

  // ===============================
  // مراقبة الأمان
  // ===============================

  static final List<SecurityEvent> _securityEvents = [];

  /// تسجيل حدث أمني
  static void _logSecurityEvent(String event, String data) {
    final securityEvent = SecurityEvent(
      timestamp: DateTime.now(),
      event: event,
      data: data,
      severity: SecuritySeverity.medium,
    );

    _securityEvents.add(securityEvent);

    // تسجيل الحدث الأمني باستخدام خدمة التسجيل
    final truncatedData = data.length > 50
        ? '${data.substring(0, 50)}...'
        : data;
    LoggingService.security(
      'حدث أمني: $event',
      category: 'DataProtection',
      data: {
        'event': event,
        'data_preview': truncatedData,
        'data_length': data.length,
        'severity': securityEvent.severity.toString(),
      },
    );

    // يمكن إضافة تسجيل في ملف أو إرسال تنبيه
    _handleSecurityEvent(securityEvent);
  }

  /// معالجة الأحداث الأمنية
  static void _handleSecurityEvent(SecurityEvent event) {
    // يمكن إضافة منطق معالجة الأحداث الأمنية هنا
    // مثل: إرسال تنبيه، حفظ في قاعدة البيانات، إلخ

    if (event.severity == SecuritySeverity.high) {
      // معالجة خاصة للأحداث عالية الخطورة
      LoggingService.error(
        'تحذير أمني عالي: ${event.event}',
        category: 'SecurityAlert',
        data: {
          'event': event.event,
          'severity': event.severity.toString(),
          'timestamp': event.timestamp.toIso8601String(),
        },
      );
    }
  }

  /// الحصول على الأحداث الأمنية الأخيرة
  static List<SecurityEvent> getRecentSecurityEvents({int limit = 100}) {
    return _securityEvents.take(limit).toList();
  }

  /// مسح الأحداث الأمنية القديمة
  static void clearOldSecurityEvents({Duration? olderThan}) {
    final cutoffTime = DateTime.now().subtract(
      olderThan ?? const Duration(days: 30),
    );
    _securityEvents.removeWhere(
      (event) => event.timestamp.isBefore(cutoffTime),
    );
  }

  // ===============================
  // إحصائيات الأمان
  // ===============================

  /// الحصول على إحصائيات الأمان
  static SecurityStats getSecurityStats() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    final lastWeek = now.subtract(const Duration(days: 7));

    final events24h = _securityEvents
        .where((e) => e.timestamp.isAfter(last24Hours))
        .length;
    final eventsWeek = _securityEvents
        .where((e) => e.timestamp.isAfter(lastWeek))
        .length;

    return SecurityStats(
      totalEvents: _securityEvents.length,
      eventsLast24Hours: events24h,
      eventsLastWeek: eventsWeek,
      lastEventTime: _securityEvents.isNotEmpty
          ? _securityEvents.last.timestamp
          : null,
    );
  }
}

// ===============================
// نماذج البيانات الأمنية
// ===============================

/// حدث أمني
class SecurityEvent {
  final DateTime timestamp;
  final String event;
  final String data;
  final SecuritySeverity severity;

  SecurityEvent({
    required this.timestamp,
    required this.event,
    required this.data,
    required this.severity,
  });
}

/// مستوى خطورة الحدث الأمني
enum SecuritySeverity { low, medium, high, critical }

/// إحصائيات الأمان
class SecurityStats {
  final int totalEvents;
  final int eventsLast24Hours;
  final int eventsLastWeek;
  final DateTime? lastEventTime;

  SecurityStats({
    required this.totalEvents,
    required this.eventsLast24Hours,
    required this.eventsLastWeek,
    this.lastEventTime,
  });
}
