import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'package:smart_ledger/services/audit_service.dart';

import 'package:smart_ledger/constants/app_constants.dart';
import 'package:smart_ledger/models/audit_log.dart';

void main() {
  group('AuditService Tests', () {
    late Database database;

    setUpAll(() {
      // تهيئة sqflite للاختبار
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      // إنشاء قاعدة بيانات في الذاكرة للاختبار
      database = await openDatabase(
        inMemoryDatabasePath,
        version: AppConstants.databaseVersion,
        onCreate: (db, version) async {
          // إنشاء جدول سجل المراجعة فقط للاختبار
          await db.execute('''
            CREATE TABLE ${AppConstants.auditLogTable} (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              action TEXT NOT NULL,
              entity_type TEXT NOT NULL,
              entity_id INTEGER,
              entity_name TEXT,
              old_values TEXT,
              new_values TEXT,
              user_id TEXT,
              user_name TEXT,
              ip_address TEXT,
              user_agent TEXT,
              session_id TEXT,
              timestamp TEXT NOT NULL,
              description TEXT,
              severity TEXT NOT NULL DEFAULT 'INFO',
              category TEXT,
              reference_type TEXT,
              reference_id INTEGER,
              created_at TEXT NOT NULL
            )
          ''');
        },
      );

      // تعيين مستخدم تجريبي
      AuditService.setCurrentUser(
        userId: 'test_user_1',
        userName: 'مستخدم تجريبي',
        sessionId: 'test_session_123',
      );
    });

    tearDown(() async {
      await database.close();
      AuditService.clearCurrentUser();
    });

    group('User Management Tests', () {
      test('should set and clear current user', () {
        AuditService.setCurrentUser(
          userId: 'user123',
          userName: 'أحمد محمد',
          sessionId: 'session456',
        );

        // لا يمكن اختبار القيم الخاصة مباشرة، لكن يمكن اختبار السلوك
        expect(() => AuditService.clearCurrentUser(), returnsNormally);
      });
    });

    group('Audit Logging Tests', () {
      test('should log create operation successfully', () async {
        final result = await AuditService.logCreate(
          entityType: AppConstants.auditEntityAccount,
          entityId: 1,
          entityName: 'حساب الصندوق',
          newValues: {
            'code': '1001',
            'name': 'حساب الصندوق',
            'type': 'asset',
            'balance': 0.0,
          },
          description: 'تم إنشاء حساب جديد',
          category: 'Accounting',
        );

        expect(result, isTrue);
      });

      test('should log update operation successfully', () async {
        final result = await AuditService.logUpdate(
          entityType: AppConstants.auditEntityAccount,
          entityId: 1,
          entityName: 'حساب الصندوق',
          oldValues: {'name': 'حساب الصندوق القديم', 'balance': 0.0},
          newValues: {'name': 'حساب الصندوق الجديد', 'balance': 1000.0},
          description: 'تم تحديث الحساب',
          category: 'Accounting',
        );

        expect(result, isTrue);
      });

      test('should log delete operation successfully', () async {
        final result = await AuditService.logDelete(
          entityType: AppConstants.auditEntityAccount,
          entityId: 1,
          entityName: 'حساب الصندوق',
          oldValues: {
            'code': '1001',
            'name': 'حساب الصندوق',
            'type': 'asset',
            'balance': 1000.0,
          },
          description: 'تم حذف الحساب',
          category: 'Accounting',
        );

        expect(result, isTrue);
      });

      test('should log login operation successfully', () async {
        final result = await AuditService.logLogin(
          userId: 'user123',
          userName: 'أحمد محمد',
          description: 'تسجيل دخول ناجح',
        );

        expect(result, isTrue);
      });

      test('should log logout operation successfully', () async {
        final result = await AuditService.logLogout(description: 'تسجيل خروج');

        expect(result, isTrue);
      });

      test('should log password change operation successfully', () async {
        final result = await AuditService.logPasswordChange(
          description: 'تم تغيير كلمة المرور',
        );

        expect(result, isTrue);
      });
    });

    group('Audit Query Tests', () {
      setUp(() async {
        // إدراج بيانات تجريبية للاختبار
        await AuditService.logCreate(
          entityType: AppConstants.auditEntityAccount,
          entityId: 1,
          entityName: 'حساب تجريبي 1',
          newValues: {'name': 'حساب تجريبي 1'},
          category: 'Accounting',
        );

        await AuditService.logUpdate(
          entityType: AppConstants.auditEntityCustomer,
          entityId: 2,
          entityName: 'عميل تجريبي',
          oldValues: {'name': 'عميل قديم'},
          newValues: {'name': 'عميل جديد'},
          category: 'CRM',
        );

        await AuditService.logDelete(
          entityType: AppConstants.auditEntityItem,
          entityId: 3,
          entityName: 'صنف تجريبي',
          oldValues: {'name': 'صنف تجريبي'},
          category: 'Inventory',
        );
      });

      test('should get all audit logs', () async {
        final logs = await AuditService.getAuditLogs();

        expect(logs, isNotEmpty);
        expect(logs.length, greaterThanOrEqualTo(3));
      });

      test('should filter audit logs by action', () async {
        final createLogs = await AuditService.getAuditLogs(
          action: AppConstants.auditActionCreate,
        );

        expect(createLogs, isNotEmpty);
        for (final log in createLogs) {
          expect(log.action, equals(AppConstants.auditActionCreate));
        }
      });

      test('should filter audit logs by entity type', () async {
        final accountLogs = await AuditService.getAuditLogs(
          entityType: AppConstants.auditEntityAccount,
        );

        expect(accountLogs, isNotEmpty);
        for (final log in accountLogs) {
          expect(log.entityType, equals(AppConstants.auditEntityAccount));
        }
      });

      test('should filter audit logs by severity', () async {
        final successLogs = await AuditService.getAuditLogs(
          severity: 'SUCCESS',
        );

        expect(successLogs, isNotEmpty);
        for (final log in successLogs) {
          expect(log.severity, equals('SUCCESS'));
        }
      });

      test('should search audit logs', () async {
        final searchResults = await AuditService.searchAuditLogs(
          searchTerm: 'تجريبي',
        );

        expect(searchResults, isNotEmpty);
      });

      test('should get audit log count', () async {
        final count = await AuditService.getAuditLogCount();

        expect(count, greaterThanOrEqualTo(3));
      });

      test('should get audit statistics', () async {
        final stats = await AuditService.getAuditStatistics();

        expect(stats, isNotEmpty);
        expect(stats.containsKey('action_CREATE'), isTrue);
        expect(stats.containsKey('severity_SUCCESS'), isTrue);
      });
    });

    group('Audit Log Model Tests', () {
      test('should create audit log from map', () {
        final map = {
          'id': 1,
          'action': 'CREATE',
          'entity_type': 'ACCOUNT',
          'entity_id': 123,
          'entity_name': 'حساب تجريبي',
          'user_id': 'user123',
          'user_name': 'أحمد محمد',
          'timestamp': DateTime.now().toIso8601String(),
          'severity': 'SUCCESS',
          'created_at': DateTime.now().toIso8601String(),
        };

        final auditLog = AuditLog.fromMap(map);

        expect(auditLog.id, equals(1));
        expect(auditLog.action, equals('CREATE'));
        expect(auditLog.entityType, equals('ACCOUNT'));
        expect(auditLog.entityId, equals(123));
        expect(auditLog.entityName, equals('حساب تجريبي'));
        expect(auditLog.severity, equals('SUCCESS'));
      });

      test('should convert audit log to map', () {
        final auditLog = AuditLog(
          action: 'UPDATE',
          entityType: 'CUSTOMER',
          entityId: 456,
          entityName: 'عميل تجريبي',
          timestamp: DateTime.now(),
          severity: 'INFO',
          createdAt: DateTime.now(),
        );

        final map = auditLog.toMap();

        expect(map['action'], equals('UPDATE'));
        expect(map['entity_type'], equals('CUSTOMER'));
        expect(map['entity_id'], equals(456));
        expect(map['entity_name'], equals('عميل تجريبي'));
        expect(map['severity'], equals('INFO'));
      });

      test('should get readable description', () {
        final auditLog = AuditLog(
          action: 'CREATE',
          entityType: 'ACCOUNT',
          entityName: 'حساب الصندوق',
          timestamp: DateTime.now(),
          createdAt: DateTime.now(),
        );

        expect(auditLog.readableDescription, contains('إنشاء'));
        expect(auditLog.readableDescription, contains('حساب'));
        expect(auditLog.readableDescription, contains('حساب الصندوق'));
      });

      test('should get action icon', () {
        final createLog = AuditLog(
          action: 'CREATE',
          entityType: 'ACCOUNT',
          timestamp: DateTime.now(),
          createdAt: DateTime.now(),
        );

        final updateLog = AuditLog(
          action: 'UPDATE',
          entityType: 'ACCOUNT',
          timestamp: DateTime.now(),
          createdAt: DateTime.now(),
        );

        final deleteLog = AuditLog(
          action: 'DELETE',
          entityType: 'ACCOUNT',
          timestamp: DateTime.now(),
          createdAt: DateTime.now(),
        );

        expect(createLog.actionIcon, equals('➕'));
        expect(updateLog.actionIcon, equals('✏️'));
        expect(deleteLog.actionIcon, equals('🗑️'));
      });

      test('should get severity color', () {
        final errorLog = AuditLog(
          action: 'DELETE',
          entityType: 'ACCOUNT',
          severity: 'ERROR',
          timestamp: DateTime.now(),
          createdAt: DateTime.now(),
        );

        final successLog = AuditLog(
          action: 'CREATE',
          entityType: 'ACCOUNT',
          severity: 'SUCCESS',
          timestamp: DateTime.now(),
          createdAt: DateTime.now(),
        );

        expect(errorLog.severityColor, equals('#F44336'));
        expect(successLog.severityColor, equals('#4CAF50'));
      });
    });
  });
}
