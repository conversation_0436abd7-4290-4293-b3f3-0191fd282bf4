import 'dart:async';
import '../database/database_helper.dart';
import '../models/dashboard_models.dart';
import '../constants/app_constants.dart';
import 'logging_service.dart';
import 'performance_service.dart';
import 'progressive_loading_service.dart';

/// خدمة لوحة التحكم المتقدمة
/// توفر مؤشرات الأداء الرئيسية والبيانات المالية والرسوم البيانية
class AdvancedDashboardService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final PerformanceService _performanceService = PerformanceService();
  final ProgressiveLoadingService _progressiveLoadingService =
      ProgressiveLoadingService();
  static final AdvancedDashboardService _instance =
      AdvancedDashboardService._internal();

  AdvancedDashboardService._internal();

  factory AdvancedDashboardService() => _instance;

  /// الحصول على بيانات لوحة التحكم الكاملة
  Future<DashboardData> getDashboardData({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      startDate ??= DateTime.now().subtract(const Duration(days: 30));
      endDate ??= DateTime.now();

      final financialSummary = await getFinancialSummary(startDate, endDate);
      final kpis = await getKPIs(financialSummary);
      final revenueChart = await getRevenueChartData(startDate, endDate);
      final expenseChart = await getExpenseChartData(startDate, endDate);
      final profitChart = await getProfitChartData(startDate, endDate);
      final cashFlowChart = await getCashFlowChartData(startDate, endDate);

      return DashboardData(
        kpis: kpis,
        financialSummary: financialSummary,
        revenueChart: revenueChart,
        expenseChart: expenseChart,
        profitChart: profitChart,
        cashFlowChart: cashFlowChart,
        lastRefresh: DateTime.now(),
      );
    } catch (e) {
      LoggingService.error(
        'فشل في الحصول على بيانات لوحة التحكم',
        category: 'AdvancedDashboard',
        data: {'error': e.toString()},
      );
      return DashboardData.empty();
    }
  }

  /// الحصول على إحصائيات الأداء المتقدمة
  Future<Map<String, dynamic>> getPerformanceStatistics() async {
    try {
      final metrics = _performanceService.currentMetrics;
      final statistics = _performanceService.getStatistics();
      final recentEvents = _performanceService.recentEvents;

      return {
        'total_operations': recentEvents.length,
        'average_response_time': statistics.averageEventDuration.inMilliseconds,
        'total_database_operations':
            metrics['db_total_connections']?.value ?? 0,
        'cache_hit_rate': _calculateCacheHitRate(statistics),
        'memory_usage': statistics.memoryStats.currentUsageMB,
        'active_operations_count': recentEvents
            .where(
              (e) =>
                  DateTime.now().difference(e.timestamp) < Duration(minutes: 5),
            )
            .length,
        'performance_score': _calculatePerformanceScore(statistics),
        'last_updated': DateTime.now().toIso8601String(),
        'is_optimization_enabled': statistics.isOptimizationEnabled,
        'total_metrics': statistics.totalMetrics,
        'cache_size': statistics.cacheStats.totalEntries,
        'db_connections': statistics.dbStats.totalConnections,
      };
    } catch (e) {
      LoggingService.error(
        'فشل في الحصول على إحصائيات الأداء',
        category: 'AdvancedDashboard',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// حساب معدل نجاح التخزين المؤقت
  double _calculateCacheHitRate(PerformanceStatistics statistics) {
    final cacheStats = statistics.cacheStats;

    // استخدام معدل الوصول المتوسط كمؤشر على كفاءة التخزين المؤقت
    final averageAccess = cacheStats.averageAccessCount;
    final expiredRatio = cacheStats.expiredRatio;

    // حساب معدل النجاح بناءً على معدل الوصول ونسبة القيم المنتهية الصلاحية
    double hitRate = averageAccess * (1 - expiredRatio) * 10;

    return hitRate.clamp(0, 100);
  }

  /// حساب نقاط الأداء العامة
  double _calculatePerformanceScore(PerformanceStatistics statistics) {
    final avgResponseTime = statistics.averageEventDuration.inMilliseconds;
    final memoryPressure = statistics.memoryStats.pressureLevel.index;

    // نقاط الأداء بناءً على وقت الاستجابة وضغط الذاكرة
    double score = 100;

    // خصم نقاط بناءً على وقت الاستجابة (كلما زاد الوقت، قلت النقاط)
    if (avgResponseTime > 100) {
      score -= (avgResponseTime - 100) / 10;
    }

    // خصم نقاط بناءً على ضغط الذاكرة
    score -= memoryPressure * 15;

    // إضافة نقاط إذا كان التحسين مفعلاً
    if (statistics.isOptimizationEnabled) {
      score += 5;
    }

    return score.clamp(0, 100);
  }

  /// الحصول على إحصائيات التحميل التدريجي
  Future<Map<String, dynamic>> getProgressiveLoadingStatistics() async {
    try {
      final statistics = _progressiveLoadingService.getStatistics();

      return {
        'active_loads': statistics.activeLoads,
        'total_loads_started': statistics.totalLoadsStarted,
        'load_completion_rate': statistics.totalLoadsStarted > 0
            ? ((statistics.totalLoadsStarted - statistics.activeLoads) /
                      statistics.totalLoadsStarted *
                      100)
                  .toStringAsFixed(1)
            : '0.0',
        'has_active_loads': statistics.activeLoads > 0,
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggingService.error(
        'فشل في الحصول على إحصائيات التحميل التدريجي',
        category: 'AdvancedDashboard',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// الحصول على الملخص المالي
  Future<FinancialSummary> getFinancialSummary(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;

    // الحصول على الإيرادات والمصروفات
    final profitLossResult = await db.rawQuery(
      '''
      SELECT 
        a.type,
        SUM(jed.debit_amount) as total_debit,
        SUM(jed.credit_amount) as total_credit
      FROM ${AppConstants.accountsTable} a
      JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.type IN ('${AppConstants.accountTypeRevenue}', '${AppConstants.accountTypeExpense}')
        AND je.is_posted = 1
        AND je.entry_date BETWEEN ? AND ?
      GROUP BY a.type
    ''',
      [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    double totalRevenue = 0;
    double totalExpenses = 0;

    for (final row in profitLossResult) {
      final type = row['type'] as String;
      final credit = (row['total_credit'] as num?)?.toDouble() ?? 0;
      final debit = (row['total_debit'] as num?)?.toDouble() ?? 0;

      if (type == AppConstants.accountTypeRevenue) {
        totalRevenue = credit - debit;
      } else if (type == AppConstants.accountTypeExpense) {
        totalExpenses = debit - credit;
      }
    }

    // الحصول على الأصول والخصوم وحقوق الملكية
    final balanceSheetResult = await db.rawQuery(
      '''
      SELECT 
        a.type,
        SUM(
          a.balance + 
          COALESCE(
            CASE 
              WHEN a.type IN ('${AppConstants.accountTypeAsset}', '${AppConstants.accountTypeExpense}') 
              THEN (
                SELECT SUM(jed.debit_amount - jed.credit_amount)
                FROM ${AppConstants.journalEntryDetailsTable} jed
                JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = a.id 
                  AND je.is_posted = 1 
                  AND je.entry_date <= ?
              )
              ELSE (
                SELECT SUM(jed.credit_amount - jed.debit_amount)
                FROM ${AppConstants.journalEntryDetailsTable} jed
                JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = a.id 
                  AND je.is_posted = 1 
                  AND je.entry_date <= ?
              )
            END, 0
          )
        ) as total_balance
      FROM ${AppConstants.accountsTable} a
      WHERE a.is_active = 1 
        AND a.type IN ('${AppConstants.accountTypeAsset}', '${AppConstants.accountTypeLiability}', '${AppConstants.accountTypeEquity}')
      GROUP BY a.type
    ''',
      [
        endDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    double totalAssets = 0;
    double totalLiabilities = 0;
    double totalEquity = 0;

    for (final row in balanceSheetResult) {
      final type = row['type'] as String;
      final balance = (row['total_balance'] as num).toDouble();

      switch (type) {
        case AppConstants.accountTypeAsset:
          totalAssets = balance;
          break;
        case AppConstants.accountTypeLiability:
          totalLiabilities = balance;
          break;
        case AppConstants.accountTypeEquity:
          totalEquity = balance;
          break;
      }
    }

    // الحصول على عدد العملاء والموردين والفواتير
    final countsResult = await db.rawQuery(
      '''
      SELECT 
        (SELECT COUNT(*) FROM ${AppConstants.customersTable} WHERE is_active = 1) as total_customers,
        (SELECT COUNT(*) FROM ${AppConstants.suppliersTable} WHERE is_active = 1) as total_suppliers,
        (SELECT COUNT(*) FROM ${AppConstants.invoicesTable} WHERE invoice_date BETWEEN ? AND ?) as total_invoices
    ''',
      [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    final counts = countsResult.first;

    return FinancialSummary(
      totalRevenue: totalRevenue,
      totalExpenses: totalExpenses,
      netProfit: totalRevenue - totalExpenses,
      totalAssets: totalAssets,
      totalLiabilities: totalLiabilities,
      totalEquity: totalEquity,
      cashFlow: totalRevenue - totalExpenses, // تبسيط للتدفق النقدي
      totalCustomers: counts['total_customers'] as int,
      totalSuppliers: counts['total_suppliers'] as int,
      totalInvoices: counts['total_invoices'] as int,
      periodStart: startDate,
      periodEnd: endDate,
    );
  }

  /// الحصول على مؤشرات الأداء الرئيسية
  Future<List<KPIModel>> getKPIs(FinancialSummary summary) async {
    final kpis = <KPIModel>[];

    // مؤشر إجمالي الإيرادات
    kpis.add(
      KPIModel(
        id: 'total_revenue',
        title: 'إجمالي الإيرادات',
        value: summary.totalRevenue.toStringAsFixed(2),
        unit: 'ل.س',
        percentage: _calculateRevenueGrowth(summary.totalRevenue),
        trend: _getTrend(_calculateRevenueGrowth(summary.totalRevenue)),
        description: 'إجمالي الإيرادات للفترة المحددة',
        lastUpdated: DateTime.now(),
      ),
    );

    // مؤشر صافي الربح
    kpis.add(
      KPIModel(
        id: 'net_profit',
        title: 'صافي الربح',
        value: summary.netProfit.toStringAsFixed(2),
        unit: 'ل.س',
        percentage: summary.profitMargin,
        trend: summary.netProfit >= 0 ? KPITrend.up : KPITrend.down,
        description: 'صافي الربح بعد خصم جميع المصروفات',
        lastUpdated: DateTime.now(),
      ),
    );

    // مؤشر نسبة الربح
    kpis.add(
      KPIModel(
        id: 'profit_margin',
        title: 'نسبة الربح',
        value: summary.profitMargin.toStringAsFixed(1),
        unit: '%',
        percentage: summary.profitMargin,
        trend: _getTrend(summary.profitMargin),
        description: 'نسبة الربح إلى الإيرادات',
        lastUpdated: DateTime.now(),
      ),
    );

    // مؤشر إجمالي الأصول
    kpis.add(
      KPIModel(
        id: 'total_assets',
        title: 'إجمالي الأصول',
        value: summary.totalAssets.toStringAsFixed(2),
        unit: 'ل.س',
        percentage: 0,
        trend: KPITrend.stable,
        description: 'إجمالي قيمة الأصول',
        lastUpdated: DateTime.now(),
      ),
    );

    // مؤشر عدد العملاء
    kpis.add(
      KPIModel(
        id: 'total_customers',
        title: 'عدد العملاء',
        value: summary.totalCustomers.toString(),
        unit: 'عميل',
        percentage: 0,
        trend: KPITrend.stable,
        description: 'إجمالي عدد العملاء النشطين',
        lastUpdated: DateTime.now(),
      ),
    );

    // مؤشر عدد الفواتير
    kpis.add(
      KPIModel(
        id: 'total_invoices',
        title: 'عدد الفواتير',
        value: summary.totalInvoices.toString(),
        unit: 'فاتورة',
        percentage: 0,
        trend: KPITrend.stable,
        description: 'إجمالي عدد الفواتير للفترة',
        lastUpdated: DateTime.now(),
      ),
    );

    return kpis;
  }

  /// حساب نمو الإيرادات (مبسط)
  double _calculateRevenueGrowth(double currentRevenue) {
    // هذا مبسط - في التطبيق الحقيقي نحتاج لمقارنة مع الفترة السابقة
    return currentRevenue > 0 ? 5.0 : 0.0;
  }

  /// تحديد اتجاه المؤشر
  KPITrend _getTrend(double percentage) {
    if (percentage > 0) return KPITrend.up;
    if (percentage < 0) return KPITrend.down;
    return KPITrend.stable;
  }

  /// الحصول على بيانات رسم الإيرادات
  Future<List<ChartData>> getRevenueChartData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT 
        DATE(je.entry_date) as date,
        SUM(jed.credit_amount - jed.debit_amount) as revenue
      FROM ${AppConstants.journalEntriesTable} je
      JOIN ${AppConstants.journalEntryDetailsTable} jed ON je.id = jed.journal_entry_id
      JOIN ${AppConstants.accountsTable} a ON jed.account_id = a.id
      WHERE a.type = '${AppConstants.accountTypeRevenue}'
        AND je.is_posted = 1
        AND je.entry_date BETWEEN ? AND ?
      GROUP BY DATE(je.entry_date)
      ORDER BY je.entry_date
    ''',
      [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    return result
        .map(
          (row) => ChartData(
            label: row['date'] as String,
            value: (row['revenue'] as num).toDouble(),
            date: DateTime.parse(row['date'] as String),
            category: 'revenue',
          ),
        )
        .toList();
  }

  /// الحصول على بيانات رسم المصروفات
  Future<List<ChartData>> getExpenseChartData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT 
        DATE(je.entry_date) as date,
        SUM(jed.debit_amount - jed.credit_amount) as expense
      FROM ${AppConstants.journalEntriesTable} je
      JOIN ${AppConstants.journalEntryDetailsTable} jed ON je.id = jed.journal_entry_id
      JOIN ${AppConstants.accountsTable} a ON jed.account_id = a.id
      WHERE a.type = '${AppConstants.accountTypeExpense}'
        AND je.is_posted = 1
        AND je.entry_date BETWEEN ? AND ?
      GROUP BY DATE(je.entry_date)
      ORDER BY je.entry_date
    ''',
      [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    return result
        .map(
          (row) => ChartData(
            label: row['date'] as String,
            value: (row['expense'] as num).toDouble(),
            date: DateTime.parse(row['date'] as String),
            category: 'expense',
          ),
        )
        .toList();
  }

  /// الحصول على بيانات رسم الأرباح
  Future<List<ChartData>> getProfitChartData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final revenueData = await getRevenueChartData(startDate, endDate);
    final expenseData = await getExpenseChartData(startDate, endDate);

    final profitData = <ChartData>[];

    for (final revenue in revenueData) {
      final expense = expenseData.firstWhere(
        (e) => e.date.day == revenue.date.day,
        orElse: () => ChartData(
          label: revenue.label,
          value: 0,
          date: revenue.date,
          category: 'expense',
        ),
      );

      profitData.add(
        ChartData(
          label: revenue.label,
          value: revenue.value - expense.value,
          date: revenue.date,
          category: 'profit',
        ),
      );
    }

    return profitData;
  }

  /// الحصول على بيانات رسم التدفق النقدي
  Future<List<ChartData>> getCashFlowChartData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // مبسط - نفس بيانات الأرباح
    return await getProfitChartData(startDate, endDate);
  }
}
