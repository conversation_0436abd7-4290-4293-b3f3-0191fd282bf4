import 'package:flutter/material.dart';
import '../services/progressive_loading_service.dart';
import '../constants/app_colors.dart';

/// Widget للتحميل التدريجي للقوائم
/// يوفر واجهة موحدة للتحميل التدريجي مع مؤشر التقدم
class ProgressiveListView<T> extends StatefulWidget {
  final Stream<ProgressiveLoadResult<T>> loadStream;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final Widget Function(BuildContext context, String error)? errorBuilder;
  final String? emptyMessage;
  final String? loadingMessage;
  final EdgeInsetsGeometry? padding;
  final ScrollController? scrollController;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final VoidCallback? onRefresh;

  const ProgressiveListView({
    super.key,
    required this.loadStream,
    required this.itemBuilder,
    this.emptyBuilder,
    this.errorBuilder,
    this.emptyMessage,
    this.loadingMessage,
    this.padding,
    this.scrollController,
    this.shrinkWrap = false,
    this.physics,
    this.onRefresh,
  });

  @override
  State<ProgressiveListView<T>> createState() => _ProgressiveListViewState<T>();
}

class _ProgressiveListViewState<T> extends State<ProgressiveListView<T>> {
  final List<T> _allItems = [];
  bool _isLoading = true;
  bool _isComplete = false;
  String? _error;
  double _progress = 0.0;
  int _totalItems = 0;
  int _loadedItems = 0;

  @override
  void initState() {
    super.initState();
    _startLoading();
  }

  void _startLoading() {
    setState(() {
      _allItems.clear();
      _isLoading = true;
      _isComplete = false;
      _error = null;
      _progress = 0.0;
      _totalItems = 0;
      _loadedItems = 0;
    });

    widget.loadStream.listen(
      (result) {
        if (mounted) {
          setState(() {
            if (result.error != null) {
              _error = result.error;
              _isLoading = false;
            } else {
              _allItems.addAll(result.data);
              _progress = result.progressPercentage;
              _totalItems = result.totalItems;
              _loadedItems = result.loadedItems;
              _isComplete = result.isComplete;
              
              if (result.isComplete) {
                _isLoading = false;
              }
            }
          });
        }
      },
      onError: (error) {
        if (mounted) {
          setState(() {
            _error = error.toString();
            _isLoading = false;
          });
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_allItems.isEmpty && _isLoading) {
      return _buildLoadingWidget();
    }

    if (_allItems.isEmpty && !_isLoading) {
      return _buildEmptyWidget();
    }

    return Column(
      children: [
        if (_isLoading && !_isComplete) _buildProgressIndicator(),
        Expanded(
          child: RefreshIndicator(
            onRefresh: widget.onRefresh != null 
                ? () async {
                    widget.onRefresh!();
                    _startLoading();
                  }
                : () async => _startLoading(),
            child: ListView.builder(
              controller: widget.scrollController,
              padding: widget.padding,
              shrinkWrap: widget.shrinkWrap,
              physics: widget.physics,
              itemCount: _allItems.length,
              itemBuilder: (context, index) {
                return widget.itemBuilder(context, _allItems[index], index);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            widget.loadingMessage ?? 'جاري تحميل البيانات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: _progress / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${_progress.toStringAsFixed(0)}%',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تم تحميل $_loadedItems من $_totalItems',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              if (_allItems.isNotEmpty)
                Text(
                  'عرض ${_allItems.length} عنصر',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    if (widget.emptyBuilder != null) {
      return widget.emptyBuilder!(context);
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            widget.emptyMessage ?? 'لا توجد بيانات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorBuilder != null) {
      return widget.errorBuilder!(context, _error!);
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _startLoading,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget مبسط للتحميل التدريجي مع دعم البحث
class SearchableProgressiveListView<T> extends StatefulWidget {
  final Stream<ProgressiveLoadResult<T>> Function(String searchTerm) loadStreamBuilder;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final String searchHint;
  final Widget Function(BuildContext context)? emptyBuilder;
  final String? emptyMessage;
  final EdgeInsetsGeometry? padding;

  const SearchableProgressiveListView({
    super.key,
    required this.loadStreamBuilder,
    required this.itemBuilder,
    required this.searchHint,
    this.emptyBuilder,
    this.emptyMessage,
    this.padding,
  });

  @override
  State<SearchableProgressiveListView<T>> createState() => 
      _SearchableProgressiveListViewState<T>();
}

class _SearchableProgressiveListViewState<T> 
    extends State<SearchableProgressiveListView<T>> {
  final TextEditingController _searchController = TextEditingController();
  String _currentSearchTerm = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    setState(() {
      _currentSearchTerm = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: widget.searchHint,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _onSearchChanged('');
                      },
                    )
                  : null,
              border: const OutlineInputBorder(),
            ),
            onChanged: _onSearchChanged,
          ),
        ),
        Expanded(
          child: ProgressiveListView<T>(
            key: ValueKey(_currentSearchTerm),
            loadStream: widget.loadStreamBuilder(_currentSearchTerm),
            itemBuilder: widget.itemBuilder,
            emptyBuilder: widget.emptyBuilder,
            emptyMessage: widget.emptyMessage,
            padding: widget.padding,
          ),
        ),
      ],
    );
  }
}
