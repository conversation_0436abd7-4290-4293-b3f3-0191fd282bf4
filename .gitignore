# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Smart Ledger specific
# Database files (if any local test databases)
*.db
*.sqlite
*.sqlite3

# Log files
logs/
*.log

# Backup files
*.backup
*.bak

# Temporary files
temp/
tmp/
*.tmp

# IDE specific
.vscode/settings.json
.vscode/launch.json

# OS specific
Thumbs.db
ehthumbs.db
Desktop.ini

# Coverage reports
coverage/
*.lcov

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Local configuration
.env
.env.local
.env.production
.env.test
