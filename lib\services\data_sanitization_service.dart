import 'logging_service.dart';

/// خدمة تنظيف البيانات المتقدمة
/// تحمي التطبيق من البيانات الضارة وتحافظ على جودة البيانات
class DataSanitizationService {
  // ===============================
  // تنظيف النصوص الأساسي
  // ===============================

  /// تنظيف النص العام
  static String sanitizeText(String? input) {
    if (input == null) return '';

    return input
        .trim() // إزالة المسافات من البداية والنهاية
        .replaceAll(
          RegExp(r'\s+'),
          ' ',
        ) // تحويل المسافات المتعددة إلى مسافة واحدة
        .replaceAll(
          RegExp(r'[<>"]'),
          '',
        ) // إزالة HTML tags والاقتباسات المزدوجة
        .replaceAll("'", '') // إزالة الاقتباسات المفردة
        .replaceAll(RegExp(r'[;]'), '') // إزالة الفاصلة المنقوطة
        .replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '') // إزالة control characters
        .replaceAll(
          RegExp(
            r'[^\u0000-\u007F\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]',
          ),
          '',
        ); // الاحتفاظ بالأحرف الإنجليزية والعربية فقط
  }

  /// تنظيف النص مع الاحتفاظ بالأرقام والرموز الأساسية
  static String sanitizeTextWithSymbols(String? input) {
    if (input == null) return '';

    return input
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'[<>"\x27]'), '') // إزالة HTML tags والاقتباسات
        .replaceAll(RegExp(r'[;]'), '') // إزالة الفاصلة المنقوطة
        .replaceAll(RegExp(r'[\x00-\x1F\x7F]'), ''); // إزالة control characters
  }

  /// تنظيف الأسماء (أشخاص، شركات، إلخ)
  static String sanitizeName(String? input) {
    if (input == null) return '';

    String cleaned = sanitizeText(input);

    // إزالة الأرقام من الأسماء (اختياري)
    // cleaned = cleaned.replaceAll(RegExp(r'[0-9]'), '');

    // تحويل أول حرف إلى كبير
    if (cleaned.isNotEmpty) {
      cleaned = cleaned[0].toUpperCase() + cleaned.substring(1).toLowerCase();
    }

    return cleaned;
  }

  // ===============================
  // تنظيف الأرقام والمبالغ
  // ===============================

  /// تنظيف الأرقام
  static String sanitizeNumber(String? input) {
    if (input == null) return '';

    return input
        .replaceAll(
          RegExp(r'[^\d\.\-\+]'),
          '',
        ) // الاحتفاظ بالأرقام والعلامات فقط
        .trim();
  }

  /// تنظيف المبالغ المالية
  static String sanitizeAmount(String? input) {
    if (input == null) return '';

    String cleaned = input
        .replaceAll(
          RegExp(r'[^\d\.]'),
          '',
        ) // الاحتفاظ بالأرقام والنقطة العشرية فقط
        .trim();

    // التأكد من وجود نقطة عشرية واحدة فقط
    final parts = cleaned.split('.');
    if (parts.length > 2) {
      cleaned = '${parts[0]}.${parts.sublist(1).join('')}';
    }

    return cleaned;
  }

  /// تنظيف الكميات
  static String sanitizeQuantity(String? input) {
    return sanitizeAmount(input); // نفس منطق المبالغ
  }

  // ===============================
  // تنظيف الأكواد
  // ===============================

  /// تنظيف الأكواد العامة
  static String sanitizeCode(String? input) {
    if (input == null) return '';

    return input
        .trim()
        .toUpperCase()
        .replaceAll(RegExp(r'[^A-Z0-9]'), '') // الاحتفاظ بالأحرف والأرقام فقط
        .replaceAll(RegExp(r'\s+'), ''); // إزالة جميع المسافات
  }

  /// تنظيف كود الحساب
  static String sanitizeAccountCode(String? input) {
    return sanitizeCode(input);
  }

  /// تنظيف كود العميل
  static String sanitizeCustomerCode(String? input) {
    return sanitizeCode(input);
  }

  /// تنظيف كود المورد
  static String sanitizeSupplierCode(String? input) {
    return sanitizeCode(input);
  }

  /// تنظيف كود الصنف
  static String sanitizeItemCode(String? input) {
    return sanitizeCode(input);
  }

  // ===============================
  // تنظيف بيانات الاتصال
  // ===============================

  /// تنظيف رقم الهاتف
  static String sanitizePhoneNumber(String? input) {
    if (input == null) return '';

    return input
        .trim()
        .replaceAll(RegExp(r'[^\d\+]'), '') // الاحتفاظ بالأرقام وعلامة + فقط
        .replaceAll(RegExp(r'\s+'), ''); // إزالة المسافات
  }

  /// تنظيف البريد الإلكتروني
  static String sanitizeEmail(String? input) {
    if (input == null) return '';

    return input.trim().toLowerCase().replaceAll(
      RegExp(r'[^\w\.\@\-]'),
      '',
    ); // الاحتفاظ بالأحرف والأرقام والرموز المسموحة
  }

  // ===============================
  // تنظيف العناوين والوصف
  // ===============================

  /// تنظيف العنوان
  static String sanitizeAddress(String? input) {
    if (input == null) return '';

    return sanitizeTextWithSymbols(input).replaceAll(
      RegExp(r'[^\w\s\u0600-\u06FF\.\,\-\/]'),
      '',
    ); // السماح بالرموز الأساسية للعناوين
  }

  /// تنظيف الوصف
  static String sanitizeDescription(String? input) {
    if (input == null) return '';

    return sanitizeTextWithSymbols(input).replaceAll(
      RegExp(r'[^\w\s\u0600-\u06FF\.\,\-\(\)\:]'),
      '',
    ); // السماح بالرموز الأساسية للوصف
  }

  // ===============================
  // تنظيف متقدم للأمان
  // ===============================

  /// إزالة محاولات SQL Injection
  static String sanitizeForSQL(String? input) {
    if (input == null) return '';

    return input
        .replaceAll(
          RegExp(r'[\x27\x22\\]'),
          '',
        ) // إزالة الاقتباسات والشرطة المائلة
        .replaceAll(
          RegExp(
            r'\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b',
            caseSensitive: false,
          ),
          '',
        ) // إزالة كلمات SQL الخطيرة
        .replaceAll(RegExp(r'[;]'), '') // إزالة الفاصلة المنقوطة
        .replaceAll(RegExp(r'--'), '') // إزالة تعليقات SQL
        .replaceAll(
          RegExp(r'/\*.*?\*/'),
          '',
        ); // إزالة تعليقات SQL متعددة الأسطر
  }

  /// إزالة محاولات XSS
  static String sanitizeForXSS(String? input) {
    if (input == null) return '';

    return input
        .replaceAll(RegExp(r'<[^>]*>'), '') // إزالة جميع HTML tags
        .replaceAll(
          RegExp(r'javascript:', caseSensitive: false),
          '',
        ) // إزالة JavaScript
        .replaceAll(
          RegExp(r'on\w+\s*=', caseSensitive: false),
          '',
        ) // إزالة event handlers
        .replaceAll(RegExp(r'[\x27\x22\\]'), ''); // إزالة الاقتباسات
  }

  // ===============================
  // تنظيف شامل للكيانات
  // ===============================

  /// تنظيف بيانات الحساب
  static Map<String, String> sanitizeAccountData({
    required String? code,
    required String? name,
    String? description,
  }) {
    return {
      'code': sanitizeAccountCode(code),
      'name': sanitizeName(name),
      'description': sanitizeDescription(description),
    };
  }

  /// تنظيف بيانات العميل
  static Map<String, String> sanitizeCustomerData({
    required String? code,
    required String? name,
    String? phone,
    String? email,
    String? address,
  }) {
    return {
      'code': sanitizeCustomerCode(code),
      'name': sanitizeName(name),
      'phone': sanitizePhoneNumber(phone),
      'email': sanitizeEmail(email),
      'address': sanitizeAddress(address),
    };
  }

  /// تنظيف بيانات المورد
  static Map<String, String> sanitizeSupplierData({
    required String? code,
    required String? name,
    String? phone,
    String? email,
    String? address,
  }) {
    return {
      'code': sanitizeSupplierCode(code),
      'name': sanitizeName(name),
      'phone': sanitizePhoneNumber(phone),
      'email': sanitizeEmail(email),
      'address': sanitizeAddress(address),
    };
  }

  /// تنظيف بيانات الصنف
  static Map<String, String> sanitizeItemData({
    required String? code,
    required String? name,
    required String? unit,
    String? description,
  }) {
    return {
      'code': sanitizeItemCode(code),
      'name': sanitizeName(name),
      'unit': sanitizeText(unit),
      'description': sanitizeDescription(description),
    };
  }

  // ===============================
  // دوال مساعدة
  // ===============================

  /// التحقق من وجود محتوى ضار محتمل
  static bool containsSuspiciousContent(String? input) {
    if (input == null) return false;

    final suspiciousPatterns = [
      RegExp(r'<script', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),
      RegExp(
        r'\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b',
        caseSensitive: false,
      ),
      RegExp(r'[\x27\x22\\].*[\x27\x22\\]'), // اقتباسات متعددة
      RegExp(r'--'), // تعليقات SQL
    ];

    return suspiciousPatterns.any((pattern) => pattern.hasMatch(input));
  }

  /// تسجيل محاولة إدخال بيانات ضارة
  static void logSuspiciousActivity(String input, String context) {
    // تسجيل البيانات المشبوهة باستخدام خدمة التسجيل
    LoggingService.warning(
      'محاولة إدخال بيانات مشبوهة في $context',
      category: 'DataSanitization',
      data: {
        'context': context,
        'input_preview': input.length > 100
            ? '${input.substring(0, 100)}...'
            : input,
        'input_length': input.length,
      },
    );
  }

  /// تنظيف شامل مع تسجيل المحاولات المشبوهة
  static String sanitizeWithLogging(String? input, String context) {
    if (input == null) return '';

    if (containsSuspiciousContent(input)) {
      logSuspiciousActivity(input, context);
    }

    return sanitizeForXSS(sanitizeForSQL(input));
  }
}
