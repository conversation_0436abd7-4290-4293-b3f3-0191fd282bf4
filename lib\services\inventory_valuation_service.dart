/// خدمة تقييم المخزون المتقدمة
/// تدعم طرق تقييم المخزون المختلفة: FIFO, LIFO, المتوسط المرجح
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/settings_service.dart';
import '../constants/app_constants.dart';

/// طرق تقييم المخزون
enum InventoryValuationMethod {
  fifo('FIFO', 'الوارد أولاً صادر أولاً'),
  lifo('LIFO', 'الوارد أخيراً صادر أولاً'),
  weightedAverage('WEIGHTED_AVERAGE', 'المتوسط المرجح');

  const InventoryValuationMethod(this.code, this.displayName);
  final String code;
  final String displayName;

  static InventoryValuationMethod fromCode(String code) {
    return InventoryValuationMethod.values.firstWhere(
      (method) => method.code == code,
      orElse: () => InventoryValuationMethod.weightedAverage,
    );
  }
}

/// نموذج طبقة المخزون (للـ FIFO/LIFO)
class InventoryLayer {
  final int? id;
  final int itemId;
  final int locationId;
  final double quantity;
  final double remainingQuantity;
  final double unitCost;
  final DateTime receivedDate;
  final String? batchNumber;
  final DateTime? expiryDate;
  final int? movementId;

  InventoryLayer({
    this.id,
    required this.itemId,
    required this.locationId,
    required this.quantity,
    double? remainingQuantity,
    required this.unitCost,
    required this.receivedDate,
    this.batchNumber,
    this.expiryDate,
    this.movementId,
  }) : remainingQuantity = remainingQuantity ?? quantity;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'location_id': locationId,
      'quantity': quantity,
      'remaining_quantity': remainingQuantity,
      'unit_cost': unitCost,
      'received_date': receivedDate.toIso8601String(),
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'movement_id': movementId,
    };
  }

  factory InventoryLayer.fromMap(Map<String, dynamic> map) {
    return InventoryLayer(
      id: map['id']?.toInt(),
      itemId: map['item_id']?.toInt() ?? 0,
      locationId: map['location_id']?.toInt() ?? 0,
      quantity: map['quantity']?.toDouble() ?? 0.0,
      remainingQuantity: map['remaining_quantity']?.toDouble() ?? 0.0,
      unitCost: map['unit_cost']?.toDouble() ?? 0.0,
      receivedDate: DateTime.parse(
        map['received_date'] ?? DateTime.now().toIso8601String(),
      ),
      batchNumber: map['batch_number'],
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'])
          : null,
      movementId: map['movement_id']?.toInt(),
    );
  }

  InventoryLayer copyWith({
    int? id,
    int? itemId,
    int? locationId,
    double? quantity,
    double? remainingQuantity,
    double? unitCost,
    DateTime? receivedDate,
    String? batchNumber,
    DateTime? expiryDate,
    int? movementId,
  }) {
    return InventoryLayer(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      locationId: locationId ?? this.locationId,
      quantity: quantity ?? this.quantity,
      remainingQuantity: remainingQuantity ?? this.remainingQuantity,
      unitCost: unitCost ?? this.unitCost,
      receivedDate: receivedDate ?? this.receivedDate,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      movementId: movementId ?? this.movementId,
    );
  }

  bool get isFullyConsumed => remainingQuantity <= 0;
  bool get hasRemainingQuantity => remainingQuantity > 0;
}

/// نتيجة تقييم المخزون
class InventoryValuationResult {
  final double totalCost;
  final double averageUnitCost;
  final List<InventoryLayer> consumedLayers;
  final Map<String, dynamic> details;

  InventoryValuationResult({
    required this.totalCost,
    required this.averageUnitCost,
    required this.consumedLayers,
    required this.details,
  });
}

class InventoryValuationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final SettingsService _settingsService = SettingsService();

  /// إنشاء جدول طبقات المخزون
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول طبقات المخزون
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS inventory_layers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          location_id INTEGER NOT NULL,
          quantity REAL NOT NULL,
          remaining_quantity REAL NOT NULL,
          unit_cost REAL NOT NULL,
          received_date TEXT NOT NULL,
          batch_number TEXT,
          expiry_date TEXT,
          movement_id INTEGER,
          created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id),
          FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
          FOREIGN KEY (movement_id) REFERENCES inventory_movements (id)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_layers_item_location ON inventory_layers(item_id, location_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_layers_received_date ON inventory_layers(received_date)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_layers_remaining ON inventory_layers(remaining_quantity)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_layers_expiry ON inventory_layers(expiry_date)',
      );
    });

    LoggingService.info(
      'تم إنشاء جدول طبقات المخزون',
      category: 'InventoryValuationService',
    );
  }

  /// إضافة طبقة مخزون جديدة (عند الاستلام)
  Future<void> addInventoryLayer({
    required int itemId,
    required int locationId,
    required double quantity,
    required double unitCost,
    required DateTime receivedDate,
    String? batchNumber,
    DateTime? expiryDate,
    int? movementId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final layer = InventoryLayer(
        itemId: itemId,
        locationId: locationId,
        quantity: quantity,
        unitCost: unitCost,
        receivedDate: receivedDate,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        movementId: movementId,
      );

      await db.insert('inventory_layers', layer.toMap());

      LoggingService.info(
        'تم إضافة طبقة مخزون جديدة',
        category: 'InventoryValuationService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantity,
          'unit_cost': unitCost,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة طبقة المخزون',
        category: 'InventoryValuationService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حساب تكلفة البضاعة المباعة
  Future<InventoryValuationResult> calculateCostOfGoodsSold({
    required int itemId,
    required int locationId,
    required double quantityToConsume,
    InventoryValuationMethod? method,
  }) async {
    try {
      method ??= InventoryValuationMethod.fromCode(
        await _settingsService.getInventoryMethod(),
      );

      switch (method) {
        case InventoryValuationMethod.fifo:
          return await _calculateFIFOCost(
            itemId,
            locationId,
            quantityToConsume,
          );
        case InventoryValuationMethod.lifo:
          return await _calculateLIFOCost(
            itemId,
            locationId,
            quantityToConsume,
          );
        case InventoryValuationMethod.weightedAverage:
          return await _calculateWeightedAverageCost(
            itemId,
            locationId,
            quantityToConsume,
          );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب تكلفة البضاعة المباعة',
        category: 'InventoryValuationService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantityToConsume,
          'method': method?.code,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حساب التكلفة بطريقة FIFO
  Future<InventoryValuationResult> _calculateFIFOCost(
    int itemId,
    int locationId,
    double quantityToConsume,
  ) async {
    final db = await _databaseHelper.database;

    // الحصول على الطبقات مرتبة حسب تاريخ الاستلام (الأقدم أولاً)
    final result = await db.query(
      'inventory_layers',
      where: 'item_id = ? AND location_id = ? AND remaining_quantity > 0',
      whereArgs: [itemId, locationId],
      orderBy: 'received_date ASC, id ASC',
    );

    final layers = result.map((map) => InventoryLayer.fromMap(map)).toList();

    return _consumeLayers(layers, quantityToConsume, 'FIFO');
  }

  /// حساب التكلفة بطريقة LIFO
  Future<InventoryValuationResult> _calculateLIFOCost(
    int itemId,
    int locationId,
    double quantityToConsume,
  ) async {
    final db = await _databaseHelper.database;

    // الحصول على الطبقات مرتبة حسب تاريخ الاستلام (الأحدث أولاً)
    final result = await db.query(
      'inventory_layers',
      where: 'item_id = ? AND location_id = ? AND remaining_quantity > 0',
      whereArgs: [itemId, locationId],
      orderBy: 'received_date DESC, id DESC',
    );

    final layers = result.map((map) => InventoryLayer.fromMap(map)).toList();

    return _consumeLayers(layers, quantityToConsume, 'LIFO');
  }

  /// حساب التكلفة بطريقة المتوسط المرجح
  Future<InventoryValuationResult> _calculateWeightedAverageCost(
    int itemId,
    int locationId,
    double quantityToConsume,
  ) async {
    final db = await _databaseHelper.database;

    // حساب المتوسط المرجح للتكلفة
    final result = await db.rawQuery(
      '''
      SELECT 
        SUM(remaining_quantity * unit_cost) as total_value,
        SUM(remaining_quantity) as total_quantity
      FROM inventory_layers
      WHERE item_id = ? AND location_id = ? AND remaining_quantity > 0
    ''',
      [itemId, locationId],
    );

    if (result.isEmpty || result.first['total_quantity'] == null) {
      return InventoryValuationResult(
        totalCost: 0.0,
        averageUnitCost: 0.0,
        consumedLayers: [],
        details: {'method': 'WEIGHTED_AVERAGE', 'error': 'لا توجد طبقات متاحة'},
      );
    }

    final totalValue = (result.first['total_value'] as num?)?.toDouble() ?? 0.0;
    final totalQuantity =
        (result.first['total_quantity'] as num?)?.toDouble() ?? 0.0;

    if (totalQuantity == 0) {
      return InventoryValuationResult(
        totalCost: 0.0,
        averageUnitCost: 0.0,
        consumedLayers: [],
        details: {
          'method': 'WEIGHTED_AVERAGE',
          'error': 'الكمية الإجمالية صفر',
        },
      );
    }

    final averageUnitCost = totalValue / totalQuantity;
    final totalCost = quantityToConsume * averageUnitCost;

    return InventoryValuationResult(
      totalCost: totalCost,
      averageUnitCost: averageUnitCost,
      consumedLayers: [],
      details: {
        'method': 'WEIGHTED_AVERAGE',
        'total_value': totalValue,
        'total_quantity': totalQuantity,
        'average_unit_cost': averageUnitCost,
        'quantity_consumed': quantityToConsume,
      },
    );
  }

  /// استهلاك الطبقات حسب الترتيب المحدد
  Future<InventoryValuationResult> _consumeLayers(
    List<InventoryLayer> layers,
    double quantityToConsume,
    String method,
  ) async {
    double remainingToConsume = quantityToConsume;
    double totalCost = 0.0;
    final List<InventoryLayer> consumedLayers = [];
    final db = await _databaseHelper.database;

    for (final layer in layers) {
      if (remainingToConsume <= 0) break;

      final quantityFromThisLayer =
          remainingToConsume >= layer.remainingQuantity
          ? layer.remainingQuantity
          : remainingToConsume;

      final costFromThisLayer = quantityFromThisLayer * layer.unitCost;
      totalCost += costFromThisLayer;

      // تحديث الطبقة
      final updatedLayer = layer.copyWith(
        remainingQuantity: layer.remainingQuantity - quantityFromThisLayer,
      );

      await db.update(
        'inventory_layers',
        updatedLayer.toMap(),
        where: 'id = ?',
        whereArgs: [layer.id],
      );

      consumedLayers.add(
        layer.copyWith(remainingQuantity: quantityFromThisLayer),
      );

      remainingToConsume -= quantityFromThisLayer;
    }

    final averageUnitCost = quantityToConsume > 0
        ? totalCost / quantityToConsume
        : 0.0;

    return InventoryValuationResult(
      totalCost: totalCost,
      averageUnitCost: averageUnitCost,
      consumedLayers: consumedLayers,
      details: {
        'method': method,
        'quantity_consumed': quantityToConsume,
        'remaining_to_consume': remainingToConsume,
        'layers_used': consumedLayers.length,
      },
    );
  }

  /// الحصول على طبقات المخزون لصنف في موقع
  Future<List<InventoryLayer>> getInventoryLayers({
    required int itemId,
    required int locationId,
    bool activeOnly = true,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'item_id = ? AND location_id = ?';
      List<dynamic> whereArgs = [itemId, locationId];

      if (activeOnly) {
        whereClause += ' AND remaining_quantity > 0';
      }

      final result = await db.query(
        'inventory_layers',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'received_date ASC',
      );

      return result.map((map) => InventoryLayer.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على طبقات المخزون',
        category: 'InventoryValuationService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      return [];
    }
  }

  /// حساب القيمة الإجمالية للمخزون
  Future<Map<String, dynamic>> calculateInventoryValue({
    int? itemId,
    int? locationId,
    InventoryValuationMethod? method,
  }) async {
    try {
      method ??= InventoryValuationMethod.fromCode(
        await _settingsService.getInventoryMethod(),
      );

      final db = await _databaseHelper.database;

      String whereClause = 'remaining_quantity > 0';
      List<dynamic> whereArgs = [];

      if (itemId != null) {
        whereClause += ' AND item_id = ?';
        whereArgs.add(itemId);
      }

      if (locationId != null) {
        whereClause += ' AND location_id = ?';
        whereArgs.add(locationId);
      }

      switch (method) {
        case InventoryValuationMethod.fifo:
        case InventoryValuationMethod.lifo:
          // للـ FIFO/LIFO نحسب القيمة من الطبقات
          final result = await db.rawQuery('''
            SELECT
              SUM(remaining_quantity * unit_cost) as total_value,
              SUM(remaining_quantity) as total_quantity,
              COUNT(*) as layer_count
            FROM inventory_layers
            WHERE $whereClause
          ''', whereArgs);

          if (result.isNotEmpty) {
            final row = result.first;
            return {
              'method': method.code,
              'total_value': (row['total_value'] as num?)?.toDouble() ?? 0.0,
              'total_quantity':
                  (row['total_quantity'] as num?)?.toDouble() ?? 0.0,
              'layer_count': row['layer_count'] ?? 0,
              'average_unit_cost': _calculateAverageFromLayers(row),
            };
          }
          break;

        case InventoryValuationMethod.weightedAverage:
          // للمتوسط المرجح نحسب من جدول الرصيد
          String stockWhereClause = 'quantity > 0';
          List<dynamic> stockWhereArgs = [];

          if (itemId != null) {
            stockWhereClause += ' AND item_id = ?';
            stockWhereArgs.add(itemId);
          }

          if (locationId != null) {
            stockWhereClause += ' AND location_id = ?';
            stockWhereArgs.add(locationId);
          }

          final stockResult = await db.rawQuery('''
            SELECT
              SUM(quantity * average_cost) as total_value,
              SUM(quantity) as total_quantity,
              COUNT(*) as location_count
            FROM item_location_stock
            WHERE $stockWhereClause
          ''', stockWhereArgs);

          if (stockResult.isNotEmpty) {
            final row = stockResult.first;
            final totalValue = (row['total_value'] as num?)?.toDouble() ?? 0.0;
            final totalQuantity =
                (row['total_quantity'] as num?)?.toDouble() ?? 0.0;

            return {
              'method': method.code,
              'total_value': totalValue,
              'total_quantity': totalQuantity,
              'location_count': row['location_count'] ?? 0,
              'average_unit_cost': totalQuantity > 0
                  ? totalValue / totalQuantity
                  : 0.0,
            };
          }
          break;
      }

      return {
        'method': method.code,
        'total_value': 0.0,
        'total_quantity': 0.0,
        'layer_count': 0,
        'average_unit_cost': 0.0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب قيمة المخزون',
        category: 'InventoryValuationService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'method': method?.code,
          'error': e.toString(),
        },
      );
      return {};
    }
  }

  /// حساب المتوسط من الطبقات
  double _calculateAverageFromLayers(Map<String, dynamic> row) {
    final totalValue = (row['total_value'] as num?)?.toDouble() ?? 0.0;
    final totalQuantity = (row['total_quantity'] as num?)?.toDouble() ?? 0.0;
    return totalQuantity > 0 ? totalValue / totalQuantity : 0.0;
  }

  /// تنظيف الطبقات المستهلكة بالكامل
  Future<int> cleanupConsumedLayers({
    int? itemId,
    int? locationId,
    DateTime? olderThan,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'remaining_quantity <= 0';
      List<dynamic> whereArgs = [];

      if (itemId != null) {
        whereClause += ' AND item_id = ?';
        whereArgs.add(itemId);
      }

      if (locationId != null) {
        whereClause += ' AND location_id = ?';
        whereArgs.add(locationId);
      }

      if (olderThan != null) {
        whereClause += ' AND received_date < ?';
        whereArgs.add(olderThan.toIso8601String());
      }

      final deletedCount = await db.delete(
        'inventory_layers',
        where: whereClause,
        whereArgs: whereArgs,
      );

      LoggingService.info(
        'تم تنظيف الطبقات المستهلكة',
        category: 'InventoryValuationService',
        data: {
          'deleted_count': deletedCount,
          'item_id': itemId,
          'location_id': locationId,
        },
      );

      return deletedCount;
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف الطبقات المستهلكة',
        category: 'InventoryValuationService',
        data: {'error': e.toString()},
      );
      return 0;
    }
  }

  /// الحصول على الطبقات المنتهية الصلاحية أو القريبة من الانتهاء
  Future<List<InventoryLayer>> getExpiringLayers({
    int? itemId,
    int? locationId,
    int daysToExpiry = 30,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final expiryDate = DateTime.now().add(Duration(days: daysToExpiry));

      String whereClause =
          'remaining_quantity > 0 AND expiry_date IS NOT NULL AND expiry_date <= ?';
      List<dynamic> whereArgs = [expiryDate.toIso8601String()];

      if (itemId != null) {
        whereClause += ' AND item_id = ?';
        whereArgs.add(itemId);
      }

      if (locationId != null) {
        whereClause += ' AND location_id = ?';
        whereArgs.add(locationId);
      }

      final result = await db.query(
        'inventory_layers',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'expiry_date ASC',
      );

      return result.map((map) => InventoryLayer.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الطبقات المنتهية الصلاحية',
        category: 'InventoryValuationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }
}
