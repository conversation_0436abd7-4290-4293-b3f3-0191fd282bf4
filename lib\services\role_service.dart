import '../database/database_helper.dart';
import '../models/role.dart';
import '../models/permission.dart';
import '../constants/app_constants.dart';
import '../services/auth_service.dart';
import '../services/audit_service.dart';
import '../services/logging_service.dart';

/// خدمة إدارة الأدوار والصلاحيات
class RoleService {
  static final RoleService _instance = RoleService._internal();
  factory RoleService() => _instance;
  RoleService._internal();

  /// الحصول على جميع الأدوار
  Future<List<Role>> getAllRoles() async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.query(
        AppConstants.rolesTable,
        orderBy: 'name',
      );

      return results.map((map) => Role.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأدوار',
        category: 'RoleService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الأدوار النشطة
  Future<List<Role>> getActiveRoles() async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.query(
        AppConstants.rolesTable,
        where: 'is_active = 1',
        orderBy: 'name',
      );

      return results.map((map) => Role.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأدوار النشطة',
        category: 'RoleService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على دور بالمعرف
  Future<Role?> getRoleById(int id) async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.query(
        AppConstants.rolesTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return Role.fromMap(results.first);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الدور',
        category: 'RoleService',
        data: {'id': id, 'error': e.toString()},
      );
    }
    return null;
  }

  /// الحصول على دور بالاسم
  Future<Role?> getRoleByName(String name) async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.query(
        AppConstants.rolesTable,
        where: 'name = ?',
        whereArgs: [name],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return Role.fromMap(results.first);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الدور بالاسم',
        category: 'RoleService',
        data: {'name': name, 'error': e.toString()},
      );
    }
    return null;
  }

  /// إضافة دور جديد
  Future<int> insertRole(Role role) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageRoles);

      final db = await DatabaseHelper().database;

      // التحقق من عدم تكرار اسم الدور
      final existingRole = await getRoleByName(role.name);
      if (existingRole != null) {
        throw Exception('اسم الدور موجود مسبقاً');
      }

      // التحقق من صحة البيانات
      if (!role.isValid()) {
        throw Exception('بيانات الدور غير صحيحة');
      }

      final roleMap = role.toMap();
      roleMap.remove('id'); // إزالة المعرف للإدراج

      final id = await db.insert(AppConstants.rolesTable, roleMap);

      // تسجيل العملية
      await AuditService.log(
        action: 'INSERT',
        entityType: 'roles',
        entityId: id,
        entityName: role.name,
        description: 'إضافة دور جديد: ${role.name}',
      );

      LoggingService.info(
        'تم إضافة دور جديد: ${role.name}',
        category: 'RoleService',
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة الدور',
        category: 'RoleService',
        data: {'name': role.name, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث دور
  Future<int> updateRole(Role role) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageRoles);

      if (role.id == null) {
        throw Exception('معرف الدور مطلوب للتحديث');
      }

      final db = await DatabaseHelper().database;

      // الحصول على البيانات القديمة
      final oldRole = await getRoleById(role.id!);
      if (oldRole == null) {
        throw Exception('الدور غير موجود');
      }

      // منع تعديل الأدوار الافتراضية
      if (oldRole.isDefault) {
        throw Exception('لا يمكن تعديل الأدوار الافتراضية');
      }

      // التحقق من عدم تكرار اسم الدور (إذا تم تغييره)
      if (role.name != oldRole.name) {
        final existingRole = await getRoleByName(role.name);
        if (existingRole != null) {
          throw Exception('اسم الدور موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      if (!role.isValid()) {
        throw Exception('بيانات الدور غير صحيحة');
      }

      final roleMap = role.toMap();
      roleMap['updated_at'] = DateTime.now().toIso8601String();

      final result = await db.update(
        AppConstants.rolesTable,
        roleMap,
        where: 'id = ?',
        whereArgs: [role.id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'roles',
        entityId: role.id,
        entityName: role.name,
        description: 'تحديث الدور: ${role.name}',
      );

      LoggingService.info(
        'تم تحديث الدور: ${role.name}',
        category: 'RoleService',
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الدور',
        category: 'RoleService',
        data: {'id': role.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف دور
  Future<int> deleteRole(int id) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageRoles);

      final db = await DatabaseHelper().database;

      // الحصول على بيانات الدور قبل الحذف
      final role = await getRoleById(id);
      if (role == null) {
        throw Exception('الدور غير موجود');
      }

      // منع حذف الأدوار الافتراضية
      if (role.isDefault) {
        throw Exception('لا يمكن حذف الأدوار الافتراضية');
      }

      // التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
      final usersWithRole = await db.query(
        AppConstants.usersTable,
        where: 'role_id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (usersWithRole.isNotEmpty) {
        throw Exception('لا يمكن حذف الدور لوجود مستخدمين مرتبطين به');
      }

      final result = await db.delete(
        AppConstants.rolesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'DELETE',
        entityType: 'roles',
        entityId: id,
        entityName: role.name,
        description: 'حذف الدور: ${role.name}',
      );

      LoggingService.info(
        'تم حذف الدور: ${role.name}',
        category: 'RoleService',
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الدور',
        category: 'RoleService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل دور
  Future<bool> toggleRoleStatus(int id) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageRoles);

      final role = await getRoleById(id);
      if (role == null) {
        throw Exception('الدور غير موجود');
      }

      // منع إلغاء تفعيل الأدوار الافتراضية
      if (role.isDefault && role.isActive) {
        throw Exception('لا يمكن إلغاء تفعيل الأدوار الافتراضية');
      }

      final updatedRole = role.copyWith(
        isActive: !role.isActive,
        updatedAt: DateTime.now(),
      );

      await updateRole(updatedRole);
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة الدور',
        category: 'RoleService',
        data: {'id': id, 'error': e.toString()},
      );
      return false;
    }
  }

  /// إضافة صلاحية لدور
  Future<bool> addPermissionToRole(int roleId, PermissionType permission) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageRoles);

      final role = await getRoleById(roleId);
      if (role == null) {
        throw Exception('الدور غير موجود');
      }

      if (role.hasPermission(permission)) {
        return true; // الصلاحية موجودة مسبقاً
      }

      final updatedRole = role.addPermission(permission);
      await updateRole(updatedRole);

      LoggingService.info(
        'تم إضافة صلاحية ${permission.toString()} للدور ${role.name}',
        category: 'RoleService',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة صلاحية للدور',
        category: 'RoleService',
        data: {'roleId': roleId, 'permission': permission.toString(), 'error': e.toString()},
      );
      return false;
    }
  }

  /// إزالة صلاحية من دور
  Future<bool> removePermissionFromRole(int roleId, PermissionType permission) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageRoles);

      final role = await getRoleById(roleId);
      if (role == null) {
        throw Exception('الدور غير موجود');
      }

      if (!role.hasPermission(permission)) {
        return true; // الصلاحية غير موجودة
      }

      final updatedRole = role.removePermission(permission);
      await updateRole(updatedRole);

      LoggingService.info(
        'تم إزالة صلاحية ${permission.toString()} من الدور ${role.name}',
        category: 'RoleService',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في إزالة صلاحية من الدور',
        category: 'RoleService',
        data: {'roleId': roleId, 'permission': permission.toString(), 'error': e.toString()},
      );
      return false;
    }
  }

  /// البحث في الأدوار
  Future<List<Role>> searchRoles(String query) async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.query(
        AppConstants.rolesTable,
        where: 'name LIKE ? OR description LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'name',
      );

      return results.map((map) => Role.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في الأدوار',
        category: 'RoleService',
        data: {'query': query, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات الأدوار
  Future<Map<String, int>> getRoleStatistics() async {
    try {
      final db = await DatabaseHelper().database;
      
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.rolesTable}',
      );
      
      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.rolesTable} WHERE is_active = 1',
      );
      
      final defaultResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.rolesTable} WHERE is_default = 1',
      );

      return {
        'total': totalResult.first['count'] as int,
        'active': activeResult.first['count'] as int,
        'default': defaultResult.first['count'] as int,
        'custom': (totalResult.first['count'] as int) - (defaultResult.first['count'] as int),
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الأدوار',
        category: 'RoleService',
        data: {'error': e.toString()},
      );
      return {'total': 0, 'active': 0, 'default': 0, 'custom': 0};
    }
  }
}
