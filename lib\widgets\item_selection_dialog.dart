import 'package:flutter/material.dart';
import '../models/item.dart';
import '../services/item_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';

class ItemSelectionDialog extends StatefulWidget {
  final String title;
  final List<int> excludeItemIds; // الأصناف المستبعدة (المضافة مسبقاً)
  final bool showQuantityInput; // عرض إدخال الكمية
  final bool showPriceInput; // عرض إدخال السعر
  final String invoiceType; // نوع الفاتورة لتحديد السعر المناسب

  const ItemSelectionDialog({
    super.key,
    this.title = 'اختيار صنف',
    this.excludeItemIds = const [],
    this.showQuantityInput = true,
    this.showPriceInput = true,
    required this.invoiceType,
  });

  @override
  State<ItemSelectionDialog> createState() => _ItemSelectionDialogState();
}

class _ItemSelectionDialogState extends State<ItemSelectionDialog> {
  final ItemService _itemService = ItemService();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController(text: '1');
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _discountController = TextEditingController(text: '0');
  final TextEditingController _taxController = TextEditingController(text: '0');

  List<Item> _allItems = [];
  List<Item> _filteredItems = [];
  Item? _selectedItem;
  bool _isLoading = true;
  String _selectedFilter = 'available'; // available, all, low_stock

  @override
  void initState() {
    super.initState();
    _loadItems();
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _discountController.dispose();
    _taxController.dispose();
    super.dispose();
  }

  Future<void> _loadItems() async {
    try {
      final items = await _itemService.getActiveItems();
      setState(() {
        _allItems = items.where((item) => !widget.excludeItemIds.contains(item.id)).toList();
        _isLoading = false;
      });
      _filterItems();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأصناف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterItems() {
    final searchTerm = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = _allItems.where((item) {
        final matchesSearch = item.name.toLowerCase().contains(searchTerm) ||
            item.code.toLowerCase().contains(searchTerm);
        
        switch (_selectedFilter) {
          case 'available':
            return matchesSearch && item.quantity > 0;
          case 'low_stock':
            return matchesSearch && item.quantity <= item.minQuantity && item.quantity > 0;
          case 'all':
          default:
            return matchesSearch;
        }
      }).toList();
    });
  }

  void _selectItem(Item item) {
    setState(() {
      _selectedItem = item;
      // تحديد السعر المناسب حسب نوع الفاتورة
      if (widget.invoiceType == 'sale' || widget.invoiceType == 'sale_return') {
        _priceController.text = item.sellingPrice.toString();
      } else {
        _priceController.text = item.costPrice.toString();
      }
    });
  }

  bool _validateInputs() {
    if (_selectedItem == null) {
      _showError('يرجى اختيار صنف');
      return false;
    }

    final quantity = double.tryParse(_quantityController.text);
    if (quantity == null || quantity <= 0) {
      _showError('يرجى إدخال كمية صحيحة');
      return false;
    }

    final price = double.tryParse(_priceController.text);
    if (price == null || price < 0) {
      _showError('يرجى إدخال سعر صحيح');
      return false;
    }

    // التحقق من الكمية المتاحة للمبيعات
    if ((widget.invoiceType == 'sale' || widget.invoiceType == 'purchase_return') &&
        quantity > _selectedItem!.quantity) {
      _showError('الكمية المطلوبة (${quantity.toStringAsFixed(2)}) أكبر من المتاح (${_selectedItem!.quantity.toStringAsFixed(2)})');
      return false;
    }

    return true;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _confirmSelection() {
    if (!_validateInputs()) return;

    final quantity = double.parse(_quantityController.text);
    final unitPrice = double.parse(_priceController.text);
    final discountPercentage = double.tryParse(_discountController.text) ?? 0.0;
    final taxPercentage = double.tryParse(_taxController.text) ?? 0.0;

    // حساب المبالغ
    final totalPrice = quantity * unitPrice;
    final discountAmount = totalPrice * (discountPercentage / 100);
    final subtotal = totalPrice - discountAmount;
    final taxAmount = subtotal * (taxPercentage / 100);
    final netAmount = subtotal + taxAmount;

    Navigator.of(context).pop({
      'item': _selectedItem,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'discountPercentage': discountPercentage,
      'discountAmount': discountAmount,
      'taxPercentage': taxPercentage,
      'taxAmount': taxAmount,
      'netAmount': netAmount,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildSearchAndFilters(),
            const SizedBox(height: 16),
            Expanded(
              flex: 2,
              child: _isLoading ? const LoadingWidget() : _buildItemsList(),
            ),
            if (_selectedItem != null) ...[
              const Divider(),
              Expanded(
                flex: 1,
                child: _buildItemDetails(),
              ),
            ],
            const SizedBox(height: 16),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.inventory_2, color: AppColors.primary),
        const SizedBox(width: 8),
        Text(
          widget.title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Column(
      children: [
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'البحث بالاسم أو الكود...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: SegmentedButton<String>(
                segments: const [
                  ButtonSegment(value: 'available', label: Text('متاح')),
                  ButtonSegment(value: 'low_stock', label: Text('مخزون منخفض')),
                  ButtonSegment(value: 'all', label: Text('الكل')),
                ],
                selected: {_selectedFilter},
                onSelectionChanged: (Set<String> selection) {
                  setState(() {
                    _selectedFilter = selection.first;
                  });
                  _filterItems();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildItemsList() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 48, color: Colors.grey),
            const SizedBox(height: 8),
            Text(
              'لا توجد أصناف متاحة',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        final isSelected = _selectedItem?.id == item.id;
        
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 2),
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : null,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getQuantityColor(item),
              child: Text(
                item.code.substring(0, 2),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            title: Text(
              item.name,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('الكود: ${item.code}'),
                Text('الكمية: ${item.quantity.toStringAsFixed(2)} ${item.unit}'),
                Text('السعر: ${_getDisplayPrice(item).toStringAsFixed(2)} ل.س'),
              ],
            ),
            trailing: isSelected ? Icon(Icons.check_circle, color: AppColors.primary) : null,
            onTap: () => _selectItem(item),
          ),
        );
      },
    );
  }

  Color _getQuantityColor(Item item) {
    if (item.quantity <= 0) return Colors.red;
    if (item.quantity <= item.minQuantity) return Colors.orange;
    return AppColors.primary;
  }

  double _getDisplayPrice(Item item) {
    if (widget.invoiceType == 'sale' || widget.invoiceType == 'sale_return') {
      return item.sellingPrice;
    } else {
      return item.costPrice;
    }
  }

  Widget _buildItemDetails() {
    if (_selectedItem == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الصنف المختار',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildSelectedItemInfo(),
          const SizedBox(height: 16),
          if (widget.showQuantityInput || widget.showPriceInput)
            _buildInputFields(),
        ],
      ),
    );
  }

  Widget _buildSelectedItemInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _selectedItem!.name,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 4),
            Text('الكود: ${_selectedItem!.code}'),
            Text('الوحدة: ${_selectedItem!.unit}'),
            Text('الكمية المتاحة: ${_selectedItem!.quantity.toStringAsFixed(2)}'),
            Text('سعر التكلفة: ${_selectedItem!.costPrice.toStringAsFixed(2)} ل.س'),
            Text('سعر البيع: ${_selectedItem!.sellingPrice.toStringAsFixed(2)} ل.س'),
          ],
        ),
      ),
    );
  }

  Widget _buildInputFields() {
    return Column(
      children: [
        if (widget.showQuantityInput)
          TextField(
            controller: _quantityController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'الكمية',
              suffixText: _selectedItem!.unit,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        if (widget.showQuantityInput && widget.showPriceInput)
          const SizedBox(height: 8),
        if (widget.showPriceInput)
          TextField(
            controller: _priceController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'سعر الوحدة',
              suffixText: 'ل.س',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _discountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الخصم %',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _taxController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الضريبة %',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _selectedItem != null ? _confirmSelection : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}
