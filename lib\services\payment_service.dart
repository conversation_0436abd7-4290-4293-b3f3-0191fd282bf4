/// خدمة إدارة الدفعات والمدفوعات الجزئية
/// تتعامل مع جميع عمليات الدفع وتتبع المدفوعات وربطها بالفواتير
library;

import '../database/database_helper.dart';
import '../models/payment.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../models/journal_entry.dart';
import '../services/invoice_service.dart';
import '../services/journal_entry_service.dart';

class PaymentService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();
  final JournalEntryService _journalEntryService = JournalEntryService();

  /// إضافة دفعة جديدة
  Future<int> addPayment(Payment payment) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // التحقق من صحة الدفعة
      if (!payment.isValid) {
        throw Exception('بيانات الدفعة غير صحيحة');
      }

      // التحقق من وجود الفاتورة
      final invoice = await _invoiceService.getInvoiceById(payment.invoiceId);
      if (invoice == null) {
        throw Exception('الفاتورة غير موجودة');
      }

      // التحقق من إمكانية إضافة دفعات للفاتورة
      if (!invoice.status.canAddPayments) {
        throw Exception('لا يمكن إضافة دفعات لهذه الفاتورة في حالتها الحالية');
      }

      // التحقق من عدم تجاوز المبلغ المتبقي
      final currentPaidAmount = await getTotalPaidAmount(payment.invoiceId);
      final newTotalPaid = currentPaidAmount + payment.amount;

      if (newTotalPaid > invoice.totalAmount + 0.01) {
        // هامش خطأ صغير
        throw Exception('مبلغ الدفعة يتجاوز المبلغ المتبقي للفاتورة');
      }

      // إدراج الدفعة
      final paymentId = await txn.insert('payments', payment.toMap());

      // تحديث حالة الفاتورة
      await _updateInvoiceStatus(payment.invoiceId, txn);

      // إنشاء قيد محاسبي للدفعة
      await _createPaymentJournalEntry(
        payment.copyWith(id: paymentId),
        invoice,
        txn,
      );

      return paymentId;
    });
  }

  /// تحديث دفعة موجودة
  Future<void> updatePayment(Payment payment) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // التحقق من وجود الدفعة
      final existingPayment = await getPaymentById(payment.id!);
      if (existingPayment == null) {
        throw Exception('الدفعة غير موجودة');
      }

      // التحقق من إمكانية التعديل
      if (!existingPayment.canEdit) {
        throw Exception('لا يمكن تعديل هذه الدفعة');
      }

      // تحديث الدفعة
      await txn.update(
        'payments',
        payment.copyWith(updatedAt: DateTime.now()).toMap(),
        where: 'id = ?',
        whereArgs: [payment.id],
      );

      // تحديث حالة الفاتورة
      await _updateInvoiceStatus(payment.invoiceId, txn);
    });
  }

  /// حذف دفعة
  Future<void> deletePayment(int paymentId) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // الحصول على الدفعة
      final payment = await getPaymentById(paymentId);
      if (payment == null) {
        throw Exception('الدفعة غير موجودة');
      }

      // التحقق من إمكانية الحذف
      if (!payment.canDelete) {
        throw Exception('لا يمكن حذف هذه الدفعة');
      }

      // حذف الدفعة
      await txn.delete('payments', where: 'id = ?', whereArgs: [paymentId]);

      // تحديث حالة الفاتورة
      await _updateInvoiceStatus(payment.invoiceId, txn);
    });
  }

  /// الحصول على دفعة بالمعرف
  Future<Payment?> getPaymentById(int id) async {
    final db = await _databaseHelper.database;
    final maps = await db.query('payments', where: 'id = ?', whereArgs: [id]);

    if (maps.isNotEmpty) {
      return Payment.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع دفعات فاتورة
  Future<List<Payment>> getInvoicePayments(int invoiceId) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'payments',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'payment_date DESC, created_at DESC',
    );

    return maps.map((map) => Payment.fromMap(map)).toList();
  }

  /// الحصول على إجمالي المبلغ المدفوع لفاتورة
  Future<double> getTotalPaidAmount(int invoiceId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM payments WHERE invoice_id = ? AND status = ?',
      [invoiceId, PaymentStatus.confirmed.code],
    );

    return result.first['total'] as double? ?? 0.0;
  }

  /// الحصول على ملخص الدفعات لفاتورة
  Future<PaymentSummary> getPaymentSummary(int invoiceId) async {
    final invoice = await _invoiceService.getInvoiceById(invoiceId);
    if (invoice == null) {
      throw Exception('الفاتورة غير موجودة');
    }

    final payments = await getInvoicePayments(invoiceId);
    final paidAmount = await getTotalPaidAmount(invoiceId);

    return PaymentSummary(
      invoiceId: invoiceId,
      totalAmount: invoice.totalAmount,
      paidAmount: paidAmount,
      payments: payments,
    );
  }

  /// تأكيد دفعة معلقة
  Future<void> confirmPayment(int paymentId) async {
    final payment = await getPaymentById(paymentId);
    if (payment == null) {
      throw Exception('الدفعة غير موجودة');
    }

    if (payment.status != PaymentStatus.pending) {
      throw Exception('يمكن تأكيد الدفعات المعلقة فقط');
    }

    await updatePayment(
      payment.copyWith(
        status: PaymentStatus.confirmed,
        updatedAt: DateTime.now(),
      ),
    );
  }

  /// رفض دفعة معلقة
  Future<void> rejectPayment(int paymentId, String reason) async {
    final payment = await getPaymentById(paymentId);
    if (payment == null) {
      throw Exception('الدفعة غير موجودة');
    }

    if (payment.status != PaymentStatus.pending) {
      throw Exception('يمكن رفض الدفعات المعلقة فقط');
    }

    await updatePayment(
      payment.copyWith(
        status: PaymentStatus.rejected,
        notes: '${payment.notes ?? ''}\nسبب الرفض: $reason',
        updatedAt: DateTime.now(),
      ),
    );
  }

  /// البحث في الدفعات
  Future<List<Payment>> searchPayments({
    String? searchTerm,
    PaymentMethod? method,
    PaymentStatus? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (searchTerm != null && searchTerm.isNotEmpty) {
      whereClause += ' AND (reference LIKE ? OR notes LIKE ?)';
      whereArgs.addAll(['%$searchTerm%', '%$searchTerm%']);
    }

    if (method != null) {
      whereClause += ' AND method = ?';
      whereArgs.add(method.code);
    }

    if (status != null) {
      whereClause += ' AND status = ?';
      whereArgs.add(status.code);
    }

    if (fromDate != null) {
      whereClause += ' AND payment_date >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      whereClause += ' AND payment_date <= ?';
      whereArgs.add(toDate.toIso8601String());
    }

    final maps = await db.query(
      'payments',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'payment_date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => Payment.fromMap(map)).toList();
  }

  /// تحديث حالة الفاتورة بناءً على الدفعات
  Future<void> _updateInvoiceStatus(int invoiceId, dynamic txn) async {
    final invoice = await _invoiceService.getInvoiceById(invoiceId);
    if (invoice == null) return;

    final paidAmount = await getTotalPaidAmount(invoiceId);

    final newStatus = InvoiceStatusManager.calculateStatusFromPayments(
      totalAmount: invoice.totalAmount,
      paidAmount: paidAmount,
      dueDate: invoice.dueDate,
      currentStatus: invoice.status,
    );

    if (newStatus != invoice.status) {
      // تحديث حالة الفاتورة
      await txn.update(
        'invoices',
        {
          'status': newStatus.code,
          'paid_amount': paidAmount,
          'remaining_amount': invoice.totalAmount - paidAmount,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [invoiceId],
      );

      // إضافة سجل تغيير الحالة
      await txn.insert('invoice_status_history', {
        'invoice_id': invoiceId,
        'from_status': invoice.status.code,
        'to_status': newStatus.code,
        'reason': StatusChangeReason.paymentReceived.code,
        'notes': 'تحديث تلقائي بناءً على الدفعات',
        'changed_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إنشاء قيد محاسبي للدفعة
  Future<void> _createPaymentJournalEntry(
    Payment payment,
    Invoice invoice,
    dynamic txn,
  ) async {
    // تحديد الحسابات المحاسبية
    int cashAccountId = 1; // حساب الصندوق
    int customerAccountId = invoice.customerId ?? 0;
    int supplierAccountId = invoice.supplierId ?? 0;

    // تحديد نوع القيد بناءً على نوع الفاتورة
    List<JournalEntryDetail> details = [];

    if (invoice.type == 'sale' || invoice.type == 'sale_return') {
      // دفعة من عميل
      details = [
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: cashAccountId,
          debitAmount: payment.amount,
          creditAmount: 0.0,
          description: 'استلام دفعة من العميل - ${payment.method.displayName}',
        ),
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: customerAccountId,
          debitAmount: 0.0,
          creditAmount: payment.amount,
          description: 'تسديد جزئي للفاتورة رقم ${invoice.invoiceNumber}',
        ),
      ];
    } else {
      // دفعة لمورد
      details = [
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: supplierAccountId,
          debitAmount: payment.amount,
          creditAmount: 0.0,
          description: 'تسديد للمورد - ${payment.method.displayName}',
        ),
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: cashAccountId,
          debitAmount: 0.0,
          creditAmount: payment.amount,
          description: 'دفع للفاتورة رقم ${invoice.invoiceNumber}',
        ),
      ];
    }

    // إنشاء رقم القيد
    final entryNumber = await _journalEntryService.generateEntryNumber();

    // إنشاء كائن القيد المحاسبي
    final journalEntry = JournalEntry(
      entryNumber: entryNumber,
      entryDate: payment.paymentDate,
      description: 'دفعة للفاتورة رقم ${invoice.invoiceNumber}',
      type: 'payment',
      totalDebit: payment.amount,
      totalCredit: payment.amount,
      currencyId: invoice.currencyId,
      referenceType: 'payment',
      referenceId: payment.id,
      details: details,
    );

    // إنشاء القيد المحاسبي باستخدام transaction موجود
    await _journalEntryService.insertJournalEntryWithTransaction(
      journalEntry,
      txn,
    );
  }

  /// الحصول على إحصائيات الدفعات
  Future<Map<String, dynamic>> getPaymentStatistics({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'status = ?';
    List<dynamic> whereArgs = [PaymentStatus.confirmed.code];

    if (fromDate != null) {
      whereClause += ' AND payment_date >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      whereClause += ' AND payment_date <= ?';
      whereArgs.add(toDate.toIso8601String());
    }

    // إجمالي المبلغ المحصل
    final totalResult = await db.rawQuery(
      'SELECT SUM(amount) as total, COUNT(*) as count FROM payments WHERE $whereClause',
      whereArgs,
    );

    // التوزيع حسب طريقة الدفع
    final methodResult = await db.rawQuery(
      'SELECT method, SUM(amount) as total, COUNT(*) as count FROM payments WHERE $whereClause GROUP BY method',
      whereArgs,
    );

    return {
      'totalAmount': totalResult.first['total'] as double? ?? 0.0,
      'totalCount': totalResult.first['count'] as int? ?? 0,
      'byMethod': methodResult
          .map(
            (row) => {
              'method': PaymentMethod.fromCode(row['method'] as String),
              'total': row['total'] as double,
              'count': row['count'] as int,
            },
          )
          .toList(),
    };
  }
}
