/// خدمة إدارة المستودعات
/// توفر عمليات CRUD للمستودعات مع دعم المواقع المتعددة
library;

import '../database/database_helper.dart';
import '../models/warehouse.dart';
import '../models/warehouse_location.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class WarehouseService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول المستودعات والمواقع
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول المستودعات
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS warehouses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          description TEXT,
          address TEXT,
          phone TEXT,
          email TEXT,
          manager_name TEXT,
          warehouse_type TEXT NOT NULL DEFAULT 'main',
          is_active INTEGER NOT NULL DEFAULT 1,
          is_default INTEGER NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول مواقع المستودعات
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS warehouse_locations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          warehouse_id INTEGER NOT NULL,
          zone TEXT,
          aisle TEXT,
          shelf TEXT,
          bin TEXT,
          max_capacity REAL,
          location_barcode TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
          UNIQUE(warehouse_id, code)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_warehouses_code ON warehouses(code)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_warehouses_active ON warehouses(is_active)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_warehouse_locations_warehouse_id ON warehouse_locations(warehouse_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_warehouse_locations_code ON warehouse_locations(code)',
      );
    });

    LoggingService.info(
      'تم إنشاء جداول المستودعات والمواقع',
      category: 'WarehouseService',
    );
  }

  /// إضافة مستودع جديد
  Future<int> insertWarehouse(Warehouse warehouse) async {
    try {
      await _validateWarehouse(warehouse);

      final db = await _databaseHelper.database;

      // التأكد من وجود الجداول
      await createTables();

      final id = await db.insert('warehouses', warehouse.toMap());

      // إنشاء موقع افتراضي للمستودع
      final defaultLocation = WarehouseLocation(
        code: 'DEFAULT',
        name: 'الموقع الافتراضي',
        description: 'الموقع الافتراضي للمستودع ${warehouse.name}',
        warehouseId: id,
      );

      await insertLocation(defaultLocation);

      await AuditService.logCreate(
        entityType: 'warehouse',
        entityId: id,
        entityName: warehouse.name,
        newValues: warehouse.toMap(),
        description: 'تم إنشاء مستودع جديد: ${warehouse.name}',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم إضافة مستودع جديد',
        category: 'WarehouseService',
        data: {
          'warehouse_id': id,
          'warehouse_code': warehouse.code,
          'warehouse_name': warehouse.name,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة المستودع',
        category: 'WarehouseService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث مستودع
  Future<void> updateWarehouse(Warehouse warehouse) async {
    try {
      if (warehouse.id == null) {
        throw ValidationException('معرف المستودع مطلوب للتحديث');
      }

      await _validateWarehouse(warehouse);

      final db = await _databaseHelper.database;

      final updatedWarehouse = warehouse.copyWith(updatedAt: DateTime.now());

      final rowsAffected = await db.update(
        'warehouses',
        updatedWarehouse.toMap(),
        where: 'id = ?',
        whereArgs: [warehouse.id],
      );

      if (rowsAffected == 0) {
        throw Exception('المستودع غير موجود');
      }

      await AuditService.logUpdate(
        entityType: 'warehouse',
        entityId: warehouse.id!,
        entityName: warehouse.name,
        oldValues: {},
        newValues: warehouse.toMap(),
        description: 'تم تحديث المستودع: ${warehouse.name}',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم تحديث المستودع',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouse.id, 'warehouse_code': warehouse.code},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المستودع',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouse.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف مستودع
  Future<void> deleteWarehouse(int warehouseId) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود أصناف في المستودع
      final stockCount = await db.rawQuery(
        '''
        SELECT COUNT(*) as count 
        FROM item_location_stock 
        WHERE warehouse_id = ? AND quantity > 0
      ''',
        [warehouseId],
      );

      if ((stockCount.first['count'] as int) > 0) {
        throw ValidationException('لا يمكن حذف المستودع لوجود أصناف به');
      }

      await db.transaction((txn) async {
        // حذف المواقع أولاً
        await txn.delete(
          'warehouse_locations',
          where: 'warehouse_id = ?',
          whereArgs: [warehouseId],
        );

        // حذف المستودع
        final rowsAffected = await txn.delete(
          'warehouses',
          where: 'id = ?',
          whereArgs: [warehouseId],
        );

        if (rowsAffected == 0) {
          throw Exception('المستودع غير موجود');
        }
      });

      await AuditService.logDelete(
        entityType: 'warehouse',
        entityId: warehouseId,
        entityName: 'مستودع',
        oldValues: {'warehouse_id': warehouseId},
        description: 'تم حذف المستودع',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم حذف المستودع',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouseId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف المستودع',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouseId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على مستودع بالمعرف
  Future<Warehouse?> getWarehouseById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'warehouses',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) return null;

      return Warehouse.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على المستودع',
        category: 'WarehouseService',
        data: {'warehouse_id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على جميع المستودعات
  Future<List<Warehouse>> getAllWarehouses({bool activeOnly = false}) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause = 'WHERE is_active = 1';
      }

      final result = await db.rawQuery('''
        SELECT * FROM warehouses 
        $whereClause
        ORDER BY is_default DESC, name ASC
      ''', whereArgs);

      return result.map((map) => Warehouse.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على المستودعات',
        category: 'WarehouseService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على المستودع الافتراضي
  Future<Warehouse?> getDefaultWarehouse() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'warehouses',
        where: 'is_default = 1 AND is_active = 1',
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Warehouse.fromMap(result.first);
      }

      // إذا لم يوجد مستودع افتراضي، إرجاع أول مستودع نشط
      final firstActive = await db.query(
        'warehouses',
        where: 'is_active = 1',
        orderBy: 'created_at ASC',
        limit: 1,
      );

      if (firstActive.isNotEmpty) {
        return Warehouse.fromMap(firstActive.first);
      }

      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على المستودع الافتراضي',
        category: 'WarehouseService',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// تعيين مستودع كافتراضي
  Future<void> setDefaultWarehouse(int warehouseId) async {
    try {
      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // إلغاء الافتراضي من جميع المستودعات
        await txn.update('warehouses', {
          'is_default': 0,
          'updated_at': DateTime.now().toIso8601String(),
        });

        // تعيين المستودع الجديد كافتراضي
        final rowsAffected = await txn.update(
          'warehouses',
          {'is_default': 1, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ?',
          whereArgs: [warehouseId],
        );

        if (rowsAffected == 0) {
          throw Exception('المستودع غير موجود');
        }
      });

      await AuditService.log(
        action: 'default_warehouse_changed',
        entityType: 'warehouse',
        entityId: warehouseId,
        description: 'تم تغيير المستودع الافتراضي',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم تعيين المستودع الافتراضي',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouseId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تعيين المستودع الافتراضي',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouseId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من صحة بيانات المستودع
  Future<void> _validateWarehouse(Warehouse warehouse) async {
    if (warehouse.code.trim().isEmpty) {
      throw ValidationException('كود المستودع مطلوب');
    }

    if (warehouse.name.trim().isEmpty) {
      throw ValidationException('اسم المستودع مطلوب');
    }

    // التحقق من عدم تكرار الكود
    final db = await _databaseHelper.database;
    final existing = await db.query(
      'warehouses',
      where: 'code = ? AND id != ?',
      whereArgs: [warehouse.code, warehouse.id ?? 0],
    );

    if (existing.isNotEmpty) {
      throw ValidationException('كود المستودع موجود مسبقاً');
    }
  }

  // ==================== إدارة المواقع ====================

  /// إضافة موقع جديد
  Future<int> insertLocation(WarehouseLocation location) async {
    try {
      await _validateLocation(location);

      final db = await _databaseHelper.database;

      final id = await db.insert('warehouse_locations', location.toMap());

      await AuditService.logCreate(
        entityType: 'warehouse_location',
        entityId: id,
        entityName: location.name,
        newValues: location.toMap(),
        description: 'تم إنشاء موقع جديد: ${location.name}',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم إضافة موقع جديد',
        category: 'WarehouseService',
        data: {
          'location_id': id,
          'location_code': location.code,
          'warehouse_id': location.warehouseId,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة الموقع',
        category: 'WarehouseService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث موقع
  Future<void> updateLocation(WarehouseLocation location) async {
    try {
      if (location.id == null) {
        throw ValidationException('معرف الموقع مطلوب للتحديث');
      }

      await _validateLocation(location);

      final db = await _databaseHelper.database;

      final updatedLocation = location.copyWith(updatedAt: DateTime.now());

      final rowsAffected = await db.update(
        'warehouse_locations',
        updatedLocation.toMap(),
        where: 'id = ?',
        whereArgs: [location.id],
      );

      if (rowsAffected == 0) {
        throw Exception('الموقع غير موجود');
      }

      await AuditService.logUpdate(
        entityType: 'warehouse_location',
        entityId: location.id!,
        entityName: location.name,
        oldValues: {},
        newValues: location.toMap(),
        description: 'تم تحديث الموقع: ${location.name}',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم تحديث الموقع',
        category: 'WarehouseService',
        data: {'location_id': location.id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الموقع',
        category: 'WarehouseService',
        data: {'location_id': location.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف موقع
  Future<void> deleteLocation(int locationId) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود أصناف في الموقع
      final stockCount = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM item_location_stock
        WHERE location_id = ? AND quantity > 0
      ''',
        [locationId],
      );

      if ((stockCount.first['count'] as int) > 0) {
        throw ValidationException('لا يمكن حذف الموقع لوجود أصناف به');
      }

      final rowsAffected = await db.delete(
        'warehouse_locations',
        where: 'id = ?',
        whereArgs: [locationId],
      );

      if (rowsAffected == 0) {
        throw Exception('الموقع غير موجود');
      }

      await AuditService.logDelete(
        entityType: 'warehouse_location',
        entityId: locationId,
        entityName: 'موقع',
        oldValues: {'location_id': locationId},
        description: 'تم حذف الموقع',
        category: 'Warehouse',
      );

      LoggingService.info(
        'تم حذف الموقع',
        category: 'WarehouseService',
        data: {'location_id': locationId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الموقع',
        category: 'WarehouseService',
        data: {'location_id': locationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على موقع بالمعرف
  Future<WarehouseLocation?> getLocationById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'warehouse_locations',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) return null;

      return WarehouseLocation.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الموقع',
        category: 'WarehouseService',
        data: {'location_id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على مواقع مستودع معين
  Future<List<WarehouseLocation>> getWarehouseLocations(
    int warehouseId, {
    bool activeOnly = false,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'warehouse_id = ?';
      List<dynamic> whereArgs = [warehouseId];

      if (activeOnly) {
        whereClause += ' AND is_active = 1';
      }

      final result = await db.query(
        'warehouse_locations',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'name ASC',
      );

      return result.map((map) => WarehouseLocation.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على مواقع المستودع',
        category: 'WarehouseService',
        data: {'warehouse_id': warehouseId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على جميع المواقع
  Future<List<WarehouseLocation>> getAllLocations({
    bool activeOnly = false,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause = 'WHERE is_active = 1';
      }

      final result = await db.rawQuery('''
        SELECT wl.*, w.name as warehouse_name
        FROM warehouse_locations wl
        JOIN warehouses w ON wl.warehouse_id = w.id
        $whereClause
        ORDER BY w.name ASC, wl.name ASC
      ''', whereArgs);

      return result.map((map) => WarehouseLocation.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على جميع المواقع',
        category: 'WarehouseService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// التحقق من صحة بيانات الموقع
  Future<void> _validateLocation(WarehouseLocation location) async {
    if (location.code.trim().isEmpty) {
      throw ValidationException('كود الموقع مطلوب');
    }

    if (location.name.trim().isEmpty) {
      throw ValidationException('اسم الموقع مطلوب');
    }

    if (location.warehouseId <= 0) {
      throw ValidationException('معرف المستودع مطلوب');
    }

    // التحقق من عدم تكرار الكود في نفس المستودع
    final db = await _databaseHelper.database;
    final existing = await db.query(
      'warehouse_locations',
      where: 'warehouse_id = ? AND code = ? AND id != ?',
      whereArgs: [location.warehouseId, location.code, location.id ?? 0],
    );

    if (existing.isNotEmpty) {
      throw ValidationException('كود الموقع موجود مسبقاً في هذا المستودع');
    }
  }
}
