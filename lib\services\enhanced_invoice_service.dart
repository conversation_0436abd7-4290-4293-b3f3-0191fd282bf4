/// خدمة الفواتير المحسنة مع التكامل الكامل
/// تتكامل مع القيود المحاسبية والمخزون والضرائب تلقائياً
library;

import '../database/database_helper.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../services/invoice_service.dart';
import '../services/invoice_accounting_integration_service.dart';
import '../services/inventory_invoice_integration_service.dart';
import '../services/tax_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';
import '../exceptions/business_rule_exception.dart';
import '../exceptions/validation_exception.dart' as validation;

class EnhancedInvoiceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();
  final InvoiceAccountingIntegrationService _accountingIntegration =
      InvoiceAccountingIntegrationService();
  final InventoryInvoiceIntegrationService _inventoryIntegration =
      InventoryInvoiceIntegrationService();
  final TaxService _taxService = TaxService();

  /// إدراج فاتورة جديدة مع التكامل الكامل
  Future<int> insertInvoiceWithIntegration(Invoice invoice) async {
    try {
      LoggingService.info(
        'بدء إدراج فاتورة جديدة مع التكامل',
        category: 'EnhancedInvoiceService',
        data: {
          'invoiceNumber': invoice.invoiceNumber,
          'type': invoice.type,
          'totalAmount': invoice.totalAmount,
        },
      );

      // التحقق من صحة الفاتورة
      await _validateInvoiceForIntegration(invoice);

      // حساب الضرائب تلقائياً
      final invoiceWithTax = await _calculateAndApplyTax(invoice);

      return await _databaseHelper.database.then((db) async {
        return await db.transaction((txn) async {
          // 1. إدراج الفاتورة الأساسية
          final invoiceId = await _invoiceService.insertInvoice(invoiceWithTax);
          final savedInvoice = invoiceWithTax.copyWith(id: invoiceId);

          try {
            // 2. تحديث المخزون
            await _inventoryIntegration.updateInventoryFromInvoice(
              savedInvoice,
            );

            // 3. إنشاء القيد المحاسبي
            await _accountingIntegration.createJournalEntryFromInvoice(
              savedInvoice,
            );

            // 4. تسجيل العملية في سجل المراجعة
            await AuditService.logCreate(
              entityType: 'invoice_integrated',
              entityId: invoiceId,
              entityName: savedInvoice.invoiceNumber,
              newValues: savedInvoice.toMap(),
              description:
                  'إنشاء فاتورة متكاملة: ${savedInvoice.invoiceNumber}',
              category: 'Invoice Integration',
            );

            LoggingService.info(
              'تم إدراج فاتورة جديدة مع التكامل بنجاح',
              category: 'EnhancedInvoiceService',
              data: {
                'invoiceId': invoiceId,
                'invoiceNumber': savedInvoice.invoiceNumber,
                'accountingIntegrated': true,
                'inventoryIntegrated': true,
                'taxCalculated': true,
              },
            );

            return invoiceId;
          } catch (integrationError) {
            // في حالة فشل التكامل، حذف الفاتورة المدرجة
            await _invoiceService.deleteInvoice(invoiceId);

            LoggingService.error(
              'فشل في التكامل - تم حذف الفاتورة',
              category: 'EnhancedInvoiceService',
              data: {
                'invoiceId': invoiceId,
                'invoiceNumber': savedInvoice.invoiceNumber,
                'error': integrationError.toString(),
              },
            );

            throw BusinessRuleException(
              'فشل في حفظ الفاتورة مع التكامل: ${integrationError.toString()}',
            );
          }
        });
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في إدراج فاتورة مع التكامل',
        category: 'EnhancedInvoiceService',
        data: {'invoiceNumber': invoice.invoiceNumber, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث فاتورة مع التكامل الكامل
  Future<int> updateInvoiceWithIntegration(Invoice invoice) async {
    try {
      if (invoice.id == null) {
        throw validation.ValidationException('معرف الفاتورة مطلوب للتحديث');
      }

      LoggingService.info(
        'بدء تحديث فاتورة مع التكامل',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoice.id, 'invoiceNumber': invoice.invoiceNumber},
      );

      // الحصول على الفاتورة القديمة
      final oldInvoice = await _invoiceService.getInvoiceById(invoice.id!);
      if (oldInvoice == null) {
        throw BusinessRuleException('الفاتورة غير موجودة');
      }

      // التحقق من إمكانية التحديث
      await _validateInvoiceForUpdate(invoice, oldInvoice);

      // حساب الضرائب تلقائياً
      final invoiceWithTax = await _calculateAndApplyTax(invoice);

      return await _databaseHelper.database.then((db) async {
        return await db.transaction((txn) async {
          // 1. إلغاء التأثيرات السابقة
          await _reverseInvoiceEffects(oldInvoice);

          // 2. تحديث الفاتورة الأساسية
          final result = await _invoiceService.updateInvoice(invoiceWithTax);

          try {
            // 3. تطبيق التأثيرات الجديدة
            await _inventoryIntegration.updateInventoryFromInvoice(
              invoiceWithTax,
            );
            await _accountingIntegration.createJournalEntryFromInvoice(
              invoiceWithTax,
            );

            // 4. تسجيل العملية في سجل المراجعة
            await AuditService.logUpdate(
              entityType: 'invoice_integrated',
              entityId: invoice.id!,
              entityName: invoiceWithTax.invoiceNumber,
              oldValues: oldInvoice.toMap(),
              newValues: invoiceWithTax.toMap(),
              description:
                  'تحديث فاتورة متكاملة: ${invoiceWithTax.invoiceNumber}',
              category: 'Invoice Integration',
            );

            LoggingService.info(
              'تم تحديث فاتورة مع التكامل بنجاح',
              category: 'EnhancedInvoiceService',
              data: {
                'invoiceId': invoice.id,
                'invoiceNumber': invoiceWithTax.invoiceNumber,
              },
            );

            return result;
          } catch (integrationError) {
            // في حالة فشل التكامل، استعادة الحالة السابقة
            await _invoiceService.updateInvoice(oldInvoice);
            await _inventoryIntegration.updateInventoryFromInvoice(oldInvoice);
            await _accountingIntegration.createJournalEntryFromInvoice(
              oldInvoice,
            );

            throw BusinessRuleException(
              'فشل في تحديث الفاتورة مع التكامل: ${integrationError.toString()}',
            );
          }
        });
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث فاتورة مع التكامل',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoice.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف فاتورة مع إلغاء جميع التأثيرات
  Future<int> deleteInvoiceWithIntegration(int invoiceId) async {
    try {
      LoggingService.info(
        'بدء حذف فاتورة مع إلغاء التكامل',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoiceId},
      );

      // الحصول على الفاتورة
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice == null) {
        throw BusinessRuleException('الفاتورة غير موجودة');
      }

      // التحقق من إمكانية الحذف
      await _validateInvoiceForDeletion(invoice);

      return await _databaseHelper.database.then((db) async {
        return await db.transaction((txn) async {
          // 1. إلغاء التأثيرات
          await _reverseInvoiceEffects(invoice);

          // 2. حذف الفاتورة
          final result = await _invoiceService.deleteInvoice(invoiceId);

          // 3. تسجيل العملية في سجل المراجعة
          await AuditService.logDelete(
            entityType: 'invoice_integrated',
            entityId: invoiceId,
            entityName: invoice.invoiceNumber,
            oldValues: invoice.toMap(),
            description: 'حذف فاتورة متكاملة: ${invoice.invoiceNumber}',
            category: 'Invoice Integration',
          );

          LoggingService.info(
            'تم حذف فاتورة مع إلغاء التكامل بنجاح',
            category: 'EnhancedInvoiceService',
            data: {
              'invoiceId': invoiceId,
              'invoiceNumber': invoice.invoiceNumber,
            },
          );

          return result;
        });
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف فاتورة مع التكامل',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoiceId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حساب وتطبيق الضريبة على الفاتورة
  Future<Invoice> _calculateAndApplyTax(Invoice invoice) async {
    try {
      final taxCalculation = await _taxService.calculateInvoiceTax(invoice);

      return invoice.copyWith(
        subtotal: taxCalculation['subtotalBeforeTax']!,
        taxAmount: taxCalculation['taxAmount']!,
        totalAmount:
            taxCalculation['subtotalAfterTax']! - invoice.discountAmount,
      );
    } catch (e) {
      LoggingService.warning(
        'فشل في حساب الضريبة - استخدام القيم الأصلية',
        category: 'EnhancedInvoiceService',
        data: {'invoiceNumber': invoice.invoiceNumber, 'error': e.toString()},
      );
      return invoice;
    }
  }

  /// إلغاء تأثيرات الفاتورة
  Future<void> _reverseInvoiceEffects(Invoice invoice) async {
    try {
      // إلغاء تأثير المخزون
      await _inventoryIntegration.reverseInventoryUpdate(invoice);

      // حذف القيود المحاسبية
      await _accountingIntegration.deleteJournalEntryForInvoice(invoice.id!);
    } catch (e) {
      LoggingService.warning(
        'تحذير: فشل في إلغاء بعض تأثيرات الفاتورة',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoice.id, 'error': e.toString()},
      );
    }
  }

  // ===============================
  // طرق التحقق والتحليل
  // ===============================

  /// التحقق من صحة الفاتورة للتكامل
  Future<void> _validateInvoiceForIntegration(Invoice invoice) async {
    // التحقق الأساسي
    if (invoice.invoiceNumber.trim().isEmpty) {
      throw validation.ValidationException('رقم الفاتورة مطلوب');
    }

    if (invoice.items.isEmpty) {
      throw validation.ValidationException(
        'الفاتورة يجب أن تحتوي على عنصر واحد على الأقل',
      );
    }

    if (invoice.totalAmount <= 0) {
      throw validation.ValidationException(
        'إجمالي الفاتورة يجب أن يكون أكبر من الصفر',
      );
    }

    // التحقق من نوع الفاتورة
    if (!_isValidInvoiceType(invoice.type)) {
      throw validation.ValidationException(
        'نوع فاتورة غير صحيح: ${invoice.type}',
      );
    }

    // التحقق من العميل/المورد
    if (invoice.type == AppConstants.invoiceTypeSale ||
        invoice.type == AppConstants.invoiceTypeSaleReturn) {
      if (invoice.customerId == null) {
        throw validation.ValidationException(
          'معرف العميل مطلوب لفواتير المبيعات',
        );
      }
    }

    if (invoice.type == AppConstants.invoiceTypePurchase ||
        invoice.type == AppConstants.invoiceTypePurchaseReturn) {
      if (invoice.supplierId == null) {
        throw validation.ValidationException(
          'معرف المورد مطلوب لفواتير المشتريات',
        );
      }
    }

    // التحقق من عناصر الفاتورة
    for (final item in invoice.items) {
      if (item.quantity <= 0) {
        throw validation.ValidationException(
          'كمية الصنف يجب أن تكون أكبر من الصفر',
        );
      }
      if (item.unitPrice < 0) {
        throw validation.ValidationException(
          'سعر الوحدة لا يمكن أن يكون سالباً',
        );
      }
    }
  }

  /// التحقق من إمكانية تحديث الفاتورة
  Future<void> _validateInvoiceForUpdate(
    Invoice newInvoice,
    Invoice oldInvoice,
  ) async {
    // التحقق من حالة الفاتورة
    if (oldInvoice.status == InvoiceStatus.fullyPaid ||
        oldInvoice.status == InvoiceStatus.partiallyPaid) {
      throw BusinessRuleException('لا يمكن تعديل فاتورة مدفوعة');
    }

    if (oldInvoice.status == InvoiceStatus.cancelled) {
      throw BusinessRuleException('لا يمكن تعديل فاتورة ملغاة');
    }

    // التحقق من وجود قيود مرحلة
    final hasPostedEntries = await _accountingIntegration
        .hasJournalEntryForInvoice(oldInvoice.id!);
    if (hasPostedEntries) {
      final entries = await _accountingIntegration.getJournalEntriesForInvoice(
        oldInvoice.id!,
      );
      final hasPosted = entries.any((entry) => entry.isPosted);
      if (hasPosted) {
        throw BusinessRuleException('لا يمكن تعديل فاتورة لها قيود مرحلة');
      }
    }

    // التحقق الأساسي للفاتورة الجديدة
    await _validateInvoiceForIntegration(newInvoice);
  }

  /// التحقق من إمكانية حذف الفاتورة
  Future<void> _validateInvoiceForDeletion(Invoice invoice) async {
    // التحقق من حالة الفاتورة
    if (invoice.status == InvoiceStatus.fullyPaid ||
        invoice.status == InvoiceStatus.partiallyPaid) {
      throw BusinessRuleException('لا يمكن حذف فاتورة مدفوعة');
    }

    // التحقق من وجود قيود مرحلة
    final hasPostedEntries = await _accountingIntegration
        .hasJournalEntryForInvoice(invoice.id!);
    if (hasPostedEntries) {
      final entries = await _accountingIntegration.getJournalEntriesForInvoice(
        invoice.id!,
      );
      final hasPosted = entries.any((entry) => entry.isPosted);
      if (hasPosted) {
        throw BusinessRuleException('لا يمكن حذف فاتورة لها قيود مرحلة');
      }
    }
  }

  /// التحقق من صحة نوع الفاتورة
  bool _isValidInvoiceType(String type) {
    return [
      AppConstants.invoiceTypeSale,
      AppConstants.invoiceTypePurchase,
      AppConstants.invoiceTypeSaleReturn,
      AppConstants.invoiceTypePurchaseReturn,
    ].contains(type);
  }

  // ===============================
  // طرق الاستعلام والتحليل
  // ===============================

  /// الحصول على تفاصيل التكامل للفاتورة
  Future<Map<String, dynamic>> getInvoiceIntegrationDetails(
    int invoiceId,
  ) async {
    try {
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice == null) {
        throw BusinessRuleException('الفاتورة غير موجودة');
      }

      // الحصول على القيود المحاسبية
      final journalEntries = await _accountingIntegration
          .getJournalEntriesForInvoice(invoiceId);

      // الحصول على حركات المخزون
      final inventoryMovements = <Map<String, dynamic>>[];
      for (final item in invoice.items) {
        final movements = await _inventoryIntegration.getInventoryMovements(
          item.itemId,
          fromDate: invoice.invoiceDate,
          toDate: invoice.invoiceDate,
        );
        inventoryMovements.addAll(movements.map((m) => m.toMap()));
      }

      // حساب تكلفة البضاعة المباعة
      final costOfGoodsSold = await _inventoryIntegration
          .calculateCostOfGoodsSold(invoice);

      return {
        'invoice': invoice.toMap(),
        'journalEntries': journalEntries.map((e) => e.toMap()).toList(),
        'inventoryMovements': inventoryMovements,
        'costOfGoodsSold': costOfGoodsSold,
        'integrationStatus': {
          'hasJournalEntries': journalEntries.isNotEmpty,
          'hasInventoryMovements': inventoryMovements.isNotEmpty,
          'isFullyIntegrated':
              journalEntries.isNotEmpty && inventoryMovements.isNotEmpty,
        },
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على تفاصيل التكامل للفاتورة',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoiceId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إعادة تطبيق التكامل للفاتورة
  Future<void> reapplyInvoiceIntegration(int invoiceId) async {
    try {
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice == null) {
        throw BusinessRuleException('الفاتورة غير موجودة');
      }

      LoggingService.info(
        'بدء إعادة تطبيق التكامل للفاتورة',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoiceId, 'invoiceNumber': invoice.invoiceNumber},
      );

      // إلغاء التأثيرات السابقة
      await _reverseInvoiceEffects(invoice);

      // إعادة تطبيق التكامل
      await _inventoryIntegration.updateInventoryFromInvoice(invoice);
      await _accountingIntegration.createJournalEntryFromInvoice(invoice);

      LoggingService.info(
        'تم إعادة تطبيق التكامل للفاتورة بنجاح',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoiceId, 'invoiceNumber': invoice.invoiceNumber},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إعادة تطبيق التكامل للفاتورة',
        category: 'EnhancedInvoiceService',
        data: {'invoiceId': invoiceId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من سلامة التكامل لجميع الفواتير
  Future<Map<String, dynamic>> validateAllInvoicesIntegration() async {
    try {
      final allInvoices = await _invoiceService.getAllInvoices();
      int totalInvoices = allInvoices.length;
      int integratedInvoices = 0;
      int missingJournalEntries = 0;
      int missingInventoryMovements = 0;
      List<Map<String, dynamic>> issues = [];

      for (final invoice in allInvoices) {
        final hasJournal = await _accountingIntegration
            .hasJournalEntryForInvoice(invoice.id!);
        final hasInventory = (await _inventoryIntegration.getInventoryMovements(
          invoice.items.first.itemId,
          fromDate: invoice.invoiceDate,
          toDate: invoice.invoiceDate,
        )).isNotEmpty;

        if (hasJournal && hasInventory) {
          integratedInvoices++;
        } else {
          if (!hasJournal) {
            missingJournalEntries++;
            issues.add({
              'invoiceId': invoice.id,
              'invoiceNumber': invoice.invoiceNumber,
              'issue': 'missing_journal_entry',
            });
          }
          if (!hasInventory) {
            missingInventoryMovements++;
            issues.add({
              'invoiceId': invoice.id,
              'invoiceNumber': invoice.invoiceNumber,
              'issue': 'missing_inventory_movement',
            });
          }
        }
      }

      return {
        'totalInvoices': totalInvoices,
        'integratedInvoices': integratedInvoices,
        'missingJournalEntries': missingJournalEntries,
        'missingInventoryMovements': missingInventoryMovements,
        'integrationPercentage': totalInvoices > 0
            ? (integratedInvoices / totalInvoices * 100)
            : 0,
        'issues': issues,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من سلامة التكامل',
        category: 'EnhancedInvoiceService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
