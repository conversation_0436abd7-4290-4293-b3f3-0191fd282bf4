/// نموذج المستودع
/// يمثل مستودع رئيسي يحتوي على مواقع متعددة لتخزين الأصناف
library;

class Warehouse {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final String? address;
  final String? phone;
  final String? email;
  final String? managerName;
  final String warehouseType; // 'main', 'branch', 'virtual', 'consignment'
  final bool isActive;
  final bool isDefault; // المستودع الافتراضي
  final DateTime createdAt;
  final DateTime updatedAt;

  Warehouse({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.address,
    this.phone,
    this.email,
    this.managerName,
    this.warehouseType = 'main',
    this.isActive = true,
    this.isDefault = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'address': address,
      'phone': phone,
      'email': email,
      'manager_name': managerName,
      'warehouse_type': warehouseType,
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory Warehouse.fromMap(Map<String, dynamic> map) {
    return Warehouse(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      managerName: map['manager_name'],
      warehouseType: map['warehouse_type'] ?? 'main',
      isActive: (map['is_active'] ?? 1) == 1,
      isDefault: (map['is_default'] ?? 0) == 1,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// نسخ مع تعديل
  Warehouse copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    String? address,
    String? phone,
    String? email,
    String? managerName,
    String? warehouseType,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Warehouse(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      managerName: managerName ?? this.managerName,
      warehouseType: warehouseType ?? this.warehouseType,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Warehouse(id: $id, code: $code, name: $name, type: $warehouseType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Warehouse && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  String get displayName {
    return description?.isNotEmpty == true ? '$name ($description)' : name;
  }

  String get warehouseTypeDisplay {
    switch (warehouseType) {
      case 'main':
        return 'مستودع رئيسي';
      case 'branch':
        return 'مستودع فرعي';
      case 'virtual':
        return 'مستودع افتراضي';
      case 'consignment':
        return 'مستودع أمانة';
      default:
        return warehouseType;
    }
  }

  bool get hasContactInfo {
    return phone?.isNotEmpty == true || email?.isNotEmpty == true;
  }

  bool get hasManager {
    return managerName?.isNotEmpty == true;
  }

  bool get hasAddress {
    return address?.isNotEmpty == true;
  }
}

/// أنواع المستودعات المدعومة
enum WarehouseType {
  main('main', 'مستودع رئيسي'),
  branch('branch', 'مستودع فرعي'),
  virtual('virtual', 'مستودع افتراضي'),
  consignment('consignment', 'مستودع أمانة');

  const WarehouseType(this.value, this.displayName);
  final String value;
  final String displayName;

  static WarehouseType fromString(String value) {
    return WarehouseType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => WarehouseType.main,
    );
  }
}
