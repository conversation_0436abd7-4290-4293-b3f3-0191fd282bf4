class Account {
  final int? id;
  final String code;
  final String name;
  final String type;
  final int? parentId;
  final int level;
  final bool isActive;
  final double balance;
  final int currencyId;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  Account({
    this.id,
    required this.code,
    required this.name,
    required this.type,
    this.parentId,
    this.level = 1,
    this.isActive = true,
    this.balance = 0.0,
    required this.currencyId,
    this.description,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'type': type,
      'parent_id': parentId,
      'level': level,
      'is_active': isActive ? 1 : 0,
      'balance': balance,
      'currency_id': currencyId,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      type: map['type'] ?? '',
      parentId: map['parent_id']?.toInt(),
      level: map['level']?.toInt() ?? 1,
      isActive: (map['is_active'] ?? 1) == 1,
      balance: map['balance']?.toDouble() ?? 0.0,
      currencyId: map['currency_id']?.toInt() ?? 1,
      description: map['description'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Account copyWith({
    int? id,
    String? code,
    String? name,
    String? type,
    int? parentId,
    int? level,
    bool? isActive,
    double? balance,
    int? currencyId,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      type: type ?? this.type,
      parentId: parentId ?? this.parentId,
      level: level ?? this.level,
      isActive: isActive ?? this.isActive,
      balance: balance ?? this.balance,
      currencyId: currencyId ?? this.currencyId,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Account(id: $id, code: $code, name: $name, type: $type, balance: $balance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  bool get isDebitAccount {
    return type == 'asset' || type == 'expense' || type == 'purchase';
  }

  bool get isCreditAccount {
    return type == 'liability' || type == 'equity' || type == 'revenue' || type == 'sale';
  }

  String get displayBalance {
    return balance.toStringAsFixed(2);
  }

  String get accountTypeArabic {
    switch (type) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق الملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      case 'purchase':
        return 'مشتريات';
      case 'sale':
        return 'مبيعات';
      case 'inventory':
        return 'مخزون';
      default:
        return 'غير محدد';
    }
  }

  String get accountIcon {
    switch (type) {
      case 'asset':
        return '💰';
      case 'liability':
        return '📋';
      case 'equity':
        return '🏛️';
      case 'revenue':
        return '📈';
      case 'expense':
        return '📉';
      case 'purchase':
        return '🛒';
      case 'sale':
        return '💳';
      case 'inventory':
        return '📦';
      default:
        return '📊';
    }
  }
}
