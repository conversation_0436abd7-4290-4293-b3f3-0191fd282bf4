/// اختبارات خدمة الضرائب
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'package:smart_ledger/database/database_helper.dart';
import 'package:smart_ledger/services/tax_service.dart';
import 'package:smart_ledger/models/invoice.dart';
import 'package:smart_ledger/constants/app_constants.dart';

void main() {
  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  group('اختبارات خدمة الضرائب', () {
    late DatabaseHelper databaseHelper;
    late TaxService taxService;

    setUp(() async {
      databaseHelper = DatabaseHelper();
      await databaseHelper.database;
      taxService = TaxService();
    });

    tearDown(() async {
      final db = await databaseHelper.database;
      await db.close();
    });

    test('إنشاء ضريبة جديدة', () async {
      final tax = Tax(
        code: 'VAT',
        name: 'ضريبة القيمة المضافة',
        type: 'percentage',
        rate: 11.0,
        isDefault: true,
      );

      final taxId = await taxService.insertTax(tax);
      expect(taxId, greaterThan(0));

      final savedTax = await taxService.getTaxById(taxId);
      expect(savedTax, isNotNull);
      expect(savedTax!.code, equals('VAT'));
      expect(savedTax.rate, equals(11.0));
      expect(savedTax.isDefault, isTrue);
    });

    test('حساب مبلغ الضريبة بالنسبة المئوية', () async {
      final tax = Tax(
        code: 'VAT',
        name: 'ضريبة القيمة المضافة',
        type: 'percentage',
        rate: 10.0,
      );

      final taxAmount = tax.calculateTaxAmount(100.0);
      expect(taxAmount, equals(10.0));
    });

    test('حساب مبلغ الضريبة الثابت', () async {
      final tax = Tax(
        code: 'FIXED',
        name: 'ضريبة ثابتة',
        type: 'fixed',
        rate: 25.0,
      );

      final taxAmount = tax.calculateTaxAmount(100.0);
      expect(taxAmount, equals(25.0));
    });

    test('حساب ضريبة الفاتورة', () async {
      // إنشاء ضريبة افتراضية
      final tax = Tax(
        code: 'VAT',
        name: 'ضريبة القيمة المضافة',
        type: 'percentage',
        rate: 10.0,
        isDefault: true,
      );
      await taxService.insertTax(tax);

      // إنشاء فاتورة اختبار
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        invoiceDate: DateTime.now(),
        type: AppConstants.invoiceTypeSale,
        customerId: 1,
        subtotal: 100.0,
        totalAmount: 100.0,
        currencyId: 1,
        items: [],
      );

      final taxCalculation = await taxService.calculateInvoiceTax(invoice);

      expect(taxCalculation['taxAmount'], equals(10.0));
      expect(taxCalculation['subtotalBeforeTax'], equals(100.0));
      expect(taxCalculation['subtotalAfterTax'], equals(110.0));
    });

    test('الحصول على الضريبة الافتراضية', () async {
      // إنشاء ضرائب متعددة
      await taxService.insertTax(
        Tax(code: 'TAX1', name: 'ضريبة 1', type: 'percentage', rate: 5.0),
      );

      await taxService.insertTax(
        Tax(
          code: 'TAX2',
          name: 'ضريبة 2',
          type: 'percentage',
          rate: 10.0,
          isDefault: true,
        ),
      );

      final defaultTax = await taxService.getDefaultTax();
      expect(defaultTax, isNotNull);
      expect(defaultTax!.code, equals('TAX2'));
      expect(defaultTax.isDefault, isTrue);
    });

    test('تحديث ضريبة', () async {
      final tax = Tax(
        code: 'VAT',
        name: 'ضريبة القيمة المضافة',
        type: 'percentage',
        rate: 10.0,
      );

      final taxId = await taxService.insertTax(tax);
      final updatedTax = tax.copyWith(id: taxId, rate: 15.0, isDefault: true);

      await taxService.updateTax(updatedTax);

      final savedTax = await taxService.getTaxById(taxId);
      expect(savedTax!.rate, equals(15.0));
      expect(savedTax.isDefault, isTrue);
    });

    test('حذف ضريبة', () async {
      final tax = Tax(
        code: 'TEMP',
        name: 'ضريبة مؤقتة',
        type: 'percentage',
        rate: 5.0,
      );

      final taxId = await taxService.insertTax(tax);
      await taxService.deleteTax(taxId);

      final deletedTax = await taxService.getTaxById(taxId);
      expect(deletedTax, isNull);
    });

    test('البحث في الضرائب', () async {
      await taxService.insertTax(
        Tax(
          code: 'VAT',
          name: 'ضريبة القيمة المضافة',
          type: 'percentage',
          rate: 10.0,
        ),
      );

      await taxService.insertTax(
        Tax(
          code: 'SERVICE',
          name: 'ضريبة الخدمات',
          type: 'percentage',
          rate: 5.0,
        ),
      );

      final searchResults = await taxService.searchTaxes('ضريبة');
      expect(searchResults.length, equals(2));

      final vatResults = await taxService.searchTaxes('VAT');
      expect(vatResults.length, equals(1));
      expect(vatResults.first.code, equals('VAT'));
    });

    test('تفعيل وإلغاء تفعيل ضريبة', () async {
      final tax = Tax(
        code: 'TEST',
        name: 'ضريبة اختبار',
        type: 'percentage',
        rate: 5.0,
        isActive: true,
      );

      final taxId = await taxService.insertTax(tax);

      // إلغاء التفعيل
      await taxService.toggleTaxStatus(taxId);
      final deactivatedTax = await taxService.getTaxById(taxId);
      expect(deactivatedTax!.isActive, isFalse);

      // إعادة التفعيل
      await taxService.toggleTaxStatus(taxId);
      final reactivatedTax = await taxService.getTaxById(taxId);
      expect(reactivatedTax!.isActive, isTrue);
    });

    test('إنشاء الضرائب الافتراضية السورية', () async {
      await taxService.createDefaultSyrianTaxes();

      final vatTax = await taxService.getTaxByCode('VAT');
      expect(vatTax, isNotNull);
      expect(vatTax!.name, equals('ضريبة القيمة المضافة'));
      expect(vatTax.rate, equals(11.0));
      expect(vatTax.isDefault, isTrue);

      final serviceTax = await taxService.getTaxByCode('SERVICE');
      expect(serviceTax, isNotNull);
      expect(serviceTax!.name, equals('ضريبة الخدمات'));
      expect(serviceTax.rate, equals(5.0));
    });

    test('الحصول على إحصائيات الضرائب', () async {
      // إنشاء ضرائب متنوعة
      await taxService.insertTax(
        Tax(
          code: 'TAX1',
          name: 'ضريبة نشطة',
          type: 'percentage',
          rate: 10.0,
          isActive: true,
          isDefault: true,
        ),
      );

      await taxService.insertTax(
        Tax(
          code: 'TAX2',
          name: 'ضريبة غير نشطة',
          type: 'percentage',
          rate: 5.0,
          isActive: false,
        ),
      );

      final stats = await taxService.getTaxStatistics();
      expect(stats['total_taxes'], equals(2));
      expect(stats['active_taxes'], equals(1));
      expect(stats['default_taxes'], equals(1));
    });

    test('التحقق من صحة بيانات الضريبة', () async {
      // اختبار كود فارغ
      expect(
        () => taxService.insertTax(
          Tax(code: '', name: 'ضريبة', type: 'percentage', rate: 10.0),
        ),
        throwsA(isA<Exception>()),
      );

      // اختبار اسم فارغ
      expect(
        () => taxService.insertTax(
          Tax(code: 'TEST', name: '', type: 'percentage', rate: 10.0),
        ),
        throwsA(isA<Exception>()),
      );

      // اختبار معدل سالب
      expect(
        () => taxService.insertTax(
          Tax(code: 'TEST', name: 'ضريبة', type: 'percentage', rate: -5.0),
        ),
        throwsA(isA<Exception>()),
      );

      // اختبار نسبة أكبر من 100%
      expect(
        () => taxService.insertTax(
          Tax(code: 'TEST', name: 'ضريبة', type: 'percentage', rate: 150.0),
        ),
        throwsA(isA<Exception>()),
      );
    });
  });
}
