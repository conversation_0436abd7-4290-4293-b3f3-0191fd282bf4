/// خدمة ربط الفواتير بالمخزون
/// تقوم بتحديث كميات المخزون تلقائياً عند حفظ الفواتير
/// وحساب تكلفة البضاعة المباعة وتسجيل حركات المخزون
library;

import '../database/database_helper.dart';
import '../models/invoice.dart';
import '../models/item.dart';
import '../services/item_service.dart';
import '../services/settings_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';
import '../exceptions/validation_exception.dart';

/// نموذج حركة المخزون
class InventoryMovement {
  final int? id;
  final int itemId;
  final String movementType; // 'in', 'out', 'adjustment'
  final double quantity;
  final double unitCost;
  final double totalCost;
  final String referenceType; // 'invoice', 'adjustment'
  final int? referenceId;
  final String? description;
  final DateTime movementDate;
  final DateTime createdAt;

  InventoryMovement({
    this.id,
    required this.itemId,
    required this.movementType,
    required this.quantity,
    required this.unitCost,
    required this.totalCost,
    required this.referenceType,
    this.referenceId,
    this.description,
    required this.movementDate,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'movement_type': movementType,
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_cost': totalCost,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'description': description,
      'movement_date': movementDate.toIso8601String().split('T')[0],
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory InventoryMovement.fromMap(Map<String, dynamic> map) {
    return InventoryMovement(
      id: map['id']?.toInt(),
      itemId: map['item_id']?.toInt() ?? 0,
      movementType: map['movement_type'] ?? '',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unitCost: map['unit_cost']?.toDouble() ?? 0.0,
      totalCost: map['total_cost']?.toDouble() ?? 0.0,
      referenceType: map['reference_type'] ?? '',
      referenceId: map['reference_id']?.toInt(),
      description: map['description'],
      movementDate: DateTime.parse(
        map['movement_date'] ?? DateTime.now().toIso8601String(),
      ),
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

class InventoryInvoiceIntegrationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ItemService _itemService = ItemService();
  final SettingsService _settingsService = SettingsService();

  /// تحديث المخزون من فاتورة
  Future<void> updateInventoryFromInvoice(Invoice invoice) async {
    try {
      // التحقق من تفعيل تتبع المخزون
      final trackInventory = await _settingsService.getTrackInventory();
      if (!trackInventory) {
        LoggingService.info(
          'تتبع المخزون غير مفعل - تم تجاهل تحديث المخزون',
          category: 'InventoryInvoiceIntegration',
          data: {
            'invoiceId': invoice.id,
            'invoiceNumber': invoice.invoiceNumber,
          },
        );
        return;
      }

      // التحقق من صحة الفاتورة
      _validateInvoiceForInventory(invoice);

      // تحديث المخزون حسب نوع الفاتورة
      switch (invoice.type) {
        case AppConstants.invoiceTypeSale:
          await _processSaleInventoryUpdate(invoice);
          break;
        case AppConstants.invoiceTypePurchase:
          await _processPurchaseInventoryUpdate(invoice);
          break;
        case AppConstants.invoiceTypeSaleReturn:
          await _processSaleReturnInventoryUpdate(invoice);
          break;
        case AppConstants.invoiceTypePurchaseReturn:
          await _processPurchaseReturnInventoryUpdate(invoice);
          break;
        default:
          throw BusinessRuleException('نوع فاتورة غير مدعوم: ${invoice.type}');
      }

      LoggingService.info(
        'تم تحديث المخزون من فاتورة بنجاح',
        category: 'InventoryInvoiceIntegration',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'invoiceType': invoice.type,
          'itemsCount': invoice.items.length,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المخزون من فاتورة',
        category: 'InventoryInvoiceIntegration',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// معالجة تحديث مخزون المبيعات
  Future<void> _processSaleInventoryUpdate(Invoice invoice) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      for (final invoiceItem in invoice.items) {
        // الحصول على الصنف
        final item = await _itemService.getItemById(invoiceItem.itemId);
        if (item == null) {
          throw BusinessRuleException('الصنف غير موجود: ${invoiceItem.itemId}');
        }

        // التحقق من توفر الكمية
        final allowNegative = await _settingsService
            .getAllowNegativeInventory();
        if (!allowNegative && item.quantity < invoiceItem.quantity) {
          throw BusinessRuleException(
            'الكمية المتوفرة من ${item.name} غير كافية. المتوفر: ${item.quantity}, المطلوب: ${invoiceItem.quantity}',
          );
        }

        // تحديث كمية الصنف (خصم)
        final newQuantity = item.quantity - invoiceItem.quantity;
        await _updateItemQuantity(txn, item.id!, newQuantity);

        // تسجيل حركة المخزون
        await _recordInventoryMovement(
          txn,
          InventoryMovement(
            itemId: item.id!,
            movementType: 'out',
            quantity: invoiceItem.quantity,
            unitCost: item.costPrice,
            totalCost: invoiceItem.quantity * item.costPrice,
            referenceType: 'invoice',
            referenceId: invoice.id,
            description: 'مبيعات - فاتورة رقم ${invoice.invoiceNumber}',
            movementDate: invoice.invoiceDate,
          ),
        );

        // تسجيل العملية في سجل المراجعة
        await AuditService.logUpdate(
          entityType: 'item_inventory',
          entityId: item.id!,
          entityName: item.name,
          oldValues: {'quantity': item.quantity},
          newValues: {'quantity': newQuantity},
          description: 'تحديث مخزون من فاتورة مبيعات ${invoice.invoiceNumber}',
          category: 'Inventory',
        );
      }
    });
  }

  /// معالجة تحديث مخزون المشتريات
  Future<void> _processPurchaseInventoryUpdate(Invoice invoice) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      for (final invoiceItem in invoice.items) {
        // الحصول على الصنف
        final item = await _itemService.getItemById(invoiceItem.itemId);
        if (item == null) {
          throw BusinessRuleException('الصنف غير موجود: ${invoiceItem.itemId}');
        }

        // تحديث كمية الصنف (إضافة)
        final newQuantity = item.quantity + invoiceItem.quantity;
        await _updateItemQuantity(txn, item.id!, newQuantity);

        // تحديث متوسط سعر التكلفة
        final newCostPrice = await _calculateNewAverageCost(
          item,
          invoiceItem.quantity,
          invoiceItem.unitPrice,
        );
        await _updateItemCostPrice(txn, item.id!, newCostPrice);

        // تسجيل حركة المخزون
        await _recordInventoryMovement(
          txn,
          InventoryMovement(
            itemId: item.id!,
            movementType: 'in',
            quantity: invoiceItem.quantity,
            unitCost: invoiceItem.unitPrice,
            totalCost: invoiceItem.quantity * invoiceItem.unitPrice,
            referenceType: 'invoice',
            referenceId: invoice.id,
            description: 'مشتريات - فاتورة رقم ${invoice.invoiceNumber}',
            movementDate: invoice.invoiceDate,
          ),
        );

        // تسجيل العملية في سجل المراجعة
        await AuditService.logUpdate(
          entityType: 'item_inventory',
          entityId: item.id!,
          entityName: item.name,
          oldValues: {'quantity': item.quantity, 'cost_price': item.costPrice},
          newValues: {'quantity': newQuantity, 'cost_price': newCostPrice},
          description: 'تحديث مخزون من فاتورة مشتريات ${invoice.invoiceNumber}',
          category: 'Inventory',
        );
      }
    });
  }

  /// معالجة تحديث مخزون مردود المبيعات
  Future<void> _processSaleReturnInventoryUpdate(Invoice invoice) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      for (final invoiceItem in invoice.items) {
        // الحصول على الصنف
        final item = await _itemService.getItemById(invoiceItem.itemId);
        if (item == null) {
          throw BusinessRuleException('الصنف غير موجود: ${invoiceItem.itemId}');
        }

        // تحديث كمية الصنف (إضافة - عكس المبيعات)
        final newQuantity = item.quantity + invoiceItem.quantity;
        await _updateItemQuantity(txn, item.id!, newQuantity);

        // تسجيل حركة المخزون
        await _recordInventoryMovement(
          txn,
          InventoryMovement(
            itemId: item.id!,
            movementType: 'in',
            quantity: invoiceItem.quantity,
            unitCost: item.costPrice,
            totalCost: invoiceItem.quantity * item.costPrice,
            referenceType: 'invoice',
            referenceId: invoice.id,
            description: 'مردود مبيعات - فاتورة رقم ${invoice.invoiceNumber}',
            movementDate: invoice.invoiceDate,
          ),
        );

        // تسجيل العملية في سجل المراجعة
        await AuditService.logUpdate(
          entityType: 'item_inventory',
          entityId: item.id!,
          entityName: item.name,
          oldValues: {'quantity': item.quantity},
          newValues: {'quantity': newQuantity},
          description: 'تحديث مخزون من مردود مبيعات ${invoice.invoiceNumber}',
          category: 'Inventory',
        );
      }
    });
  }

  /// معالجة تحديث مخزون مردود المشتريات
  Future<void> _processPurchaseReturnInventoryUpdate(Invoice invoice) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      for (final invoiceItem in invoice.items) {
        // الحصول على الصنف
        final item = await _itemService.getItemById(invoiceItem.itemId);
        if (item == null) {
          throw BusinessRuleException('الصنف غير موجود: ${invoiceItem.itemId}');
        }

        // التحقق من توفر الكمية
        final allowNegative = await _settingsService
            .getAllowNegativeInventory();
        if (!allowNegative && item.quantity < invoiceItem.quantity) {
          throw BusinessRuleException(
            'الكمية المتوفرة من ${item.name} غير كافية للمردود. المتوفر: ${item.quantity}, المطلوب: ${invoiceItem.quantity}',
          );
        }

        // تحديث كمية الصنف (خصم - عكس المشتريات)
        final newQuantity = item.quantity - invoiceItem.quantity;
        await _updateItemQuantity(txn, item.id!, newQuantity);

        // تسجيل حركة المخزون
        await _recordInventoryMovement(
          txn,
          InventoryMovement(
            itemId: item.id!,
            movementType: 'out',
            quantity: invoiceItem.quantity,
            unitCost: item.costPrice,
            totalCost: invoiceItem.quantity * item.costPrice,
            referenceType: 'invoice',
            referenceId: invoice.id,
            description: 'مردود مشتريات - فاتورة رقم ${invoice.invoiceNumber}',
            movementDate: invoice.invoiceDate,
          ),
        );

        // تسجيل العملية في سجل المراجعة
        await AuditService.logUpdate(
          entityType: 'item_inventory',
          entityId: item.id!,
          entityName: item.name,
          oldValues: {'quantity': item.quantity},
          newValues: {'quantity': newQuantity},
          description: 'تحديث مخزون من مردود مشتريات ${invoice.invoiceNumber}',
          category: 'Inventory',
        );
      }
    });
  }

  // ===============================
  // الطرق المساعدة
  // ===============================

  /// التحقق من صحة الفاتورة للمخزون
  void _validateInvoiceForInventory(Invoice invoice) {
    if (invoice.id == null) {
      throw ValidationException('معرف الفاتورة مطلوب');
    }

    if (invoice.items.isEmpty) {
      throw ValidationException(
        'الفاتورة يجب أن تحتوي على عنصر واحد على الأقل',
      );
    }

    for (final item in invoice.items) {
      if (item.quantity <= 0) {
        throw ValidationException('كمية الصنف يجب أن تكون أكبر من الصفر');
      }
      if (item.unitPrice < 0) {
        throw ValidationException('سعر الوحدة لا يمكن أن يكون سالباً');
      }
    }
  }

  /// تحديث كمية الصنف
  Future<void> _updateItemQuantity(
    dynamic txn,
    int itemId,
    double newQuantity,
  ) async {
    await txn.update(
      AppConstants.itemsTable,
      {'quantity': newQuantity, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  /// تحديث سعر تكلفة الصنف
  Future<void> _updateItemCostPrice(
    dynamic txn,
    int itemId,
    double newCostPrice,
  ) async {
    await txn.update(
      AppConstants.itemsTable,
      {
        'cost_price': newCostPrice,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  /// حساب متوسط سعر التكلفة الجديد
  Future<double> _calculateNewAverageCost(
    Item item,
    double newQuantity,
    double newUnitCost,
  ) async {
    final inventoryMethod = await _settingsService.getInventoryMethod();

    switch (inventoryMethod) {
      case 'FIFO':
        // في طريقة FIFO، نحتفظ بسعر التكلفة الحالي للمخزون الموجود
        // ونضيف المخزون الجديد بسعره
        return newUnitCost;

      case 'LIFO':
        // في طريقة LIFO، نستخدم سعر آخر دفعة
        return newUnitCost;

      case 'WEIGHTED_AVERAGE':
      default:
        // حساب المتوسط المرجح
        final currentValue = item.quantity * item.costPrice;
        final newValue = newQuantity * newUnitCost;
        final totalQuantity = item.quantity + newQuantity;

        if (totalQuantity == 0) return item.costPrice;

        return (currentValue + newValue) / totalQuantity;
    }
  }

  /// تسجيل حركة المخزون
  Future<void> _recordInventoryMovement(
    dynamic txn,
    InventoryMovement movement,
  ) async {
    // إنشاء جدول حركات المخزون إذا لم يكن موجوداً
    await _createInventoryMovementsTableIfNotExists(txn);

    final movementData = movement.toMap();
    movementData.remove('id');

    await txn.insert('inventory_movements', movementData);
  }

  /// إنشاء جدول حركات المخزون
  Future<void> _createInventoryMovementsTableIfNotExists(dynamic txn) async {
    await txn.execute('''
      CREATE TABLE IF NOT EXISTS inventory_movements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id INTEGER NOT NULL,
        movement_type TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_cost REAL NOT NULL,
        total_cost REAL NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        description TEXT,
        movement_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id)
      )
    ''');
  }

  /// حساب تكلفة البضاعة المباعة
  Future<double> calculateCostOfGoodsSold(Invoice invoice) async {
    if (invoice.type != AppConstants.invoiceTypeSale) {
      return 0.0;
    }

    double totalCost = 0.0;

    for (final invoiceItem in invoice.items) {
      final item = await _itemService.getItemById(invoiceItem.itemId);
      if (item != null) {
        totalCost += invoiceItem.quantity * item.costPrice;
      }
    }

    return totalCost;
  }

  /// الحصول على حركات المخزون للصنف
  Future<List<InventoryMovement>> getInventoryMovements(
    int itemId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'item_id = ?';
    List<dynamic> whereArgs = [itemId];

    if (fromDate != null) {
      whereClause += ' AND movement_date >= ?';
      whereArgs.add(fromDate.toIso8601String().split('T')[0]);
    }

    if (toDate != null) {
      whereClause += ' AND movement_date <= ?';
      whereArgs.add(toDate.toIso8601String().split('T')[0]);
    }

    try {
      final result = await db.query(
        'inventory_movements',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'movement_date DESC, id DESC',
      );

      return result.map((map) => InventoryMovement.fromMap(map)).toList();
    } catch (e) {
      // إذا لم يكن الجدول موجوداً، إرجاع قائمة فارغة
      LoggingService.warning(
        'جدول حركات المخزون غير موجود',
        category: 'InventoryInvoiceIntegration',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إلغاء تحديث المخزون للفاتورة
  Future<void> reverseInventoryUpdate(Invoice invoice) async {
    try {
      // إنشاء فاتورة عكسية لإلغاء التأثير
      final reverseInvoice = _createReverseInvoice(invoice);
      await updateInventoryFromInvoice(reverseInvoice);

      LoggingService.info(
        'تم إلغاء تحديث المخزون للفاتورة',
        category: 'InventoryInvoiceIntegration',
        data: {'invoiceId': invoice.id, 'invoiceNumber': invoice.invoiceNumber},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء تحديث المخزون للفاتورة',
        category: 'InventoryInvoiceIntegration',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إنشاء فاتورة عكسية
  Invoice _createReverseInvoice(Invoice original) {
    String reverseType;
    switch (original.type) {
      case AppConstants.invoiceTypeSale:
        reverseType = AppConstants.invoiceTypeSaleReturn;
        break;
      case AppConstants.invoiceTypePurchase:
        reverseType = AppConstants.invoiceTypePurchaseReturn;
        break;
      case AppConstants.invoiceTypeSaleReturn:
        reverseType = AppConstants.invoiceTypeSale;
        break;
      case AppConstants.invoiceTypePurchaseReturn:
        reverseType = AppConstants.invoiceTypePurchase;
        break;
      default:
        throw BusinessRuleException(
          'نوع فاتورة غير مدعوم للعكس: ${original.type}',
        );
    }

    return original.copyWith(
      type: reverseType,
      invoiceNumber: 'REV-${original.invoiceNumber}',
    );
  }
}
