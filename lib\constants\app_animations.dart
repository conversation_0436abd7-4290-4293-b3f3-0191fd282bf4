import 'package:flutter/material.dart';

/// ثوابت الرسوم المتحركة والتأثيرات البصرية للتطبيق
class AppAnimations {
  // مدة الرسوم المتحركة
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 800);

  // منحنيات الرسوم المتحركة
  static const Curve easeIn = Curves.easeIn;
  static const Curve easeOut = Curves.easeOut;
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve bounceIn = Curves.bounceIn;
  static const Curve bounceOut = Curves.bounceOut;
  static const Curve elasticIn = Curves.elasticIn;
  static const Curve elasticOut = Curves.elasticOut;
  static const Curve fastOutSlowIn = Curves.fastOutSlowIn;
  static const Curve decelerate = Curves.decelerate;

  // انتقالات الصفحات
  static PageRouteBuilder<T> slideTransition<T>(
    Widget page, {
    Duration duration = normal,
    Curve curve = fastOutSlowIn,
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );
        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  static PageRouteBuilder<T> fadeTransition<T>(
    Widget page, {
    Duration duration = normal,
    Curve curve = easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation.drive(
            CurveTween(curve: curve),
          ),
          child: child,
        );
      },
    );
  }

  static PageRouteBuilder<T> scaleTransition<T>(
    Widget page, {
    Duration duration = normal,
    Curve curve = fastOutSlowIn,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );
        return ScaleTransition(
          scale: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  // رسوم متحركة للقوائم
  static Widget listItemAnimation({
    required Widget child,
    required Animation<double> animation,
    int index = 0,
    Duration delay = const Duration(milliseconds: 50),
  }) {
    final delayedAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: animation,
        curve: Interval(
          (index * delay.inMilliseconds) / 1000.0,
          1.0,
          curve: fastOutSlowIn,
        ),
      ),
    );

    return SlideTransition(
      position: delayedAnimation.drive(
        Tween<Offset>(
          begin: const Offset(0.0, 0.3),
          end: Offset.zero,
        ),
      ),
      child: FadeTransition(
        opacity: delayedAnimation,
        child: child,
      ),
    );
  }

  // رسوم متحركة للبطاقات
  static Widget cardAnimation({
    required Widget child,
    required Animation<double> animation,
    Duration delay = Duration.zero,
  }) {
    final delayedAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: animation,
        curve: Interval(
          delay.inMilliseconds / 1000.0,
          1.0,
          curve: elasticOut,
        ),
      ),
    );

    return ScaleTransition(
      scale: delayedAnimation,
      child: FadeTransition(
        opacity: delayedAnimation,
        child: child,
      ),
    );
  }

  // تأثيرات التمرير
  static Widget hoverEffect({
    required Widget child,
    Color? hoverColor,
    double scale = 1.05,
    Duration duration = fast,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: AnimatedContainer(
        duration: duration,
        curve: easeInOut,
        child: child,
      ),
    );
  }

  // تأثيرات الضغط
  static Widget pressEffect({
    required Widget child,
    required VoidCallback onPressed,
    double scale = 0.95,
    Duration duration = fast,
  }) {
    return GestureDetector(
      onTapDown: (_) {},
      onTapUp: (_) => onPressed(),
      onTapCancel: () {},
      child: AnimatedScale(
        scale: 1.0,
        duration: duration,
        curve: easeInOut,
        child: child,
      ),
    );
  }

  // رسوم متحركة للتحميل
  static Widget loadingAnimation({
    Color? color,
    double size = 24.0,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? Colors.blue,
        ),
      ),
    );
  }

  // رسوم متحركة للنجاح
  static Widget successAnimation({
    Color? color,
    double size = 48.0,
    Duration duration = slow,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      curve: elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Icon(
            Icons.check_circle,
            color: color ?? Colors.green,
            size: size,
          ),
        );
      },
    );
  }

  // رسوم متحركة للخطأ
  static Widget errorAnimation({
    Color? color,
    double size = 48.0,
    Duration duration = slow,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      curve: elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Icon(
            Icons.error,
            color: color ?? Colors.red,
            size: size,
          ),
        );
      },
    );
  }

  // تأثيرات الموجة (Ripple)
  static Widget rippleEffect({
    required Widget child,
    required VoidCallback onTap,
    Color? splashColor,
    Color? highlightColor,
    BorderRadius? borderRadius,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        splashColor: splashColor,
        highlightColor: highlightColor,
        borderRadius: borderRadius,
        child: child,
      ),
    );
  }

  // انتقالات مخصصة للحوارات
  static Widget dialogTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return ScaleTransition(
      scale: CurvedAnimation(
        parent: animation,
        curve: elasticOut,
      ),
      child: FadeTransition(
        opacity: animation,
        child: child,
      ),
    );
  }

  // رسوم متحركة للأرقام
  static Widget numberCountAnimation({
    required double begin,
    required double end,
    required Duration duration,
    TextStyle? textStyle,
    String? prefix,
    String? suffix,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: begin, end: end),
      duration: duration,
      curve: easeOut,
      builder: (context, value, child) {
        return Text(
          '${prefix ?? ''}${value.toStringAsFixed(0)}${suffix ?? ''}',
          style: textStyle,
        );
      },
    );
  }
}
