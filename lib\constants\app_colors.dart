import 'package:flutter/material.dart';

class AppColors {
  // نظام ألوان حديث ومريح للعين - مستوحى من Material Design 3

  // الألوان الأساسية - تدرج أزرق عصري
  static const Color primary = Color(0xFF1976D2); // أزرق حيوي وحديث
  static const Color primaryLight = Color(0xFF42A5F5); // أزرق فاتح مشرق
  static const Color primaryDark = Color(0xFF0D47A1); // أزرق داكن عميق
  static const Color primaryContainer = Color(0xFFE3F2FD); // حاوي أزرق فاتح

  // الألوان الثانوية - تدرج بنفسجي أنيق
  static const Color secondary = Color(0xFF7C4DFF); // بنفسجي حديث
  static const Color secondaryLight = Color(0xFFB39DDB); // بنفسجي فاتح
  static const Color secondaryDark = Color(0xFF512DA8); // بنفسجي داكن
  static const Color secondaryContainer = Color(0xFFF3E5F5); // حاوي بنفسجي فاتح

  // ألوان الخلفية - تدرج رمادي دافئ
  static const Color background = Color(0xFFFAFAFA); // رمادي فاتح جداً
  static const Color surface = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surfaceVariant = Color(0xFFF5F5F5); // رمادي فاتح
  static const Color surfaceContainer = Color(0xFFF8F9FA); // حاوي سطح
  static const Color surfaceContainerHigh = Color(0xFFECEDF0); // حاوي سطح مرتفع

  // ألوان النص - تباين محسن
  static const Color textPrimary = Color(0xFF212121); // رمادي داكن قوي
  static const Color textSecondary = Color(0xFF757575); // رمادي متوسط
  static const Color textHint = Color(0xFFBDBDBD); // رمادي فاتح للتلميحات
  static const Color textOnPrimary = Color(0xFFFFFFFF); // نص على الأساسي
  static const Color textOnSurface = Color(0xFF1C1B1F); // نص على السطح

  // ألوان الحالة - ألوان حديثة وواضحة
  static const Color success = Color(0xFF4CAF50); // أخضر حيوي
  static const Color successLight = Color(0xFF81C784); // أخضر فاتح
  static const Color successDark = Color(0xFF388E3C); // أخضر داكن
  static const Color successContainer = Color(0xFFE8F5E8); // حاوي أخضر

  static const Color warning = Color(0xFFFF9800); // برتقالي حيوي
  static const Color warningLight = Color(0xFFFFB74D); // برتقالي فاتح
  static const Color warningDark = Color(0xFFF57C00); // برتقالي داكن
  static const Color warningContainer = Color(0xFFFFF3E0); // حاوي برتقالي

  static const Color error = Color(0xFFF44336); // أحمر حيوي
  static const Color errorLight = Color(0xFFE57373); // أحمر فاتح
  static const Color errorDark = Color(0xFFD32F2F); // أحمر داكن
  static const Color errorContainer = Color(0xFFFFEBEE); // حاوي أحمر

  static const Color info = Color(0xFF2196F3); // أزرق للمعلومات
  static const Color infoLight = Color(0xFF64B5F6); // أزرق فاتح
  static const Color infoDark = Color(0xFF1976D2); // أزرق داكن
  static const Color infoContainer = Color(0xFFE3F2FD); // حاوي أزرق

  // ألوان إضافية للمحاسبة - ألوان متميزة ومعبرة
  static const Color income = Color(0xFF4CAF50); // أخضر للإيرادات
  static const Color expense = Color(0xFFF44336); // أحمر للمصروفات
  static const Color asset = Color(0xFF2196F3); // أزرق للأصول
  static const Color liability = Color(0xFFFF9800); // برتقالي للخصوم
  static const Color equity = Color(0xFF9C27B0); // بنفسجي لحقوق الملكية
  static const Color inventory = Color(0xFF607D8B); // رمادي مزرق للمخزون

  // ألوان الحدود والفواصل - حديثة ومتناسقة
  static const Color border = Color(0xFFE0E0E0); // رمادي فاتح للحدود
  static const Color borderLight = Color(0xFFF5F5F5); // رمادي فاتح جداً
  static const Color borderDark = Color(0xFFBDBDBD); // رمادي متوسط
  static const Color divider = Color(0xFFEEEEEE); // رمادي فاتح للفواصل
  static const Color outline = Color(0xFF79747E); // خط خارجي
  static const Color outlineVariant = Color(0xFFCAC4D0); // خط خارجي متغير

  // ألوان الظلال - ظلال ناعمة وطبيعية
  static const Color shadow = Color(0x1F000000); // ظل خفيف
  static const Color shadowMedium = Color(0x33000000); // ظل متوسط
  static const Color shadowDark = Color(0x4D000000); // ظل قوي
  static const Color elevation1 = Color(0x0A000000); // ارتفاع 1
  static const Color elevation2 = Color(0x14000000); // ارتفاع 2
  static const Color elevation3 = Color(0x1F000000); // ارتفاع 3

  // تدرجات لونية حديثة وجذابة
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary, primaryDark],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryLight, secondary, secondaryDark],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [background, surfaceContainer],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [surface, surfaceVariant],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [successLight, success],
  );

  static const LinearGradient warningGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [warningLight, warning],
  );

  static const LinearGradient errorGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [errorLight, error],
  );

  // ألوان إضافية للتفاعل
  static const Color hover = Color(0x0A1976D2); // لون التمرير
  static const Color pressed = Color(0x1A1976D2); // لون الضغط
  static const Color focus = Color(0x1F1976D2); // لون التركيز
  static const Color selected = Color(0x14E3F2FD); // لون التحديد
  static const Color disabled = Color(0x61000000); // لون التعطيل
  static const Color disabledContainer = Color(0x1F000000); // حاوي معطل

  // ألوان خاصة للتطبيق المحاسبي
  static const Color profit = Color(0xFF4CAF50); // ربح
  static const Color loss = Color(0xFFF44336); // خسارة
  static const Color neutral = Color(0xFF9E9E9E); // محايد
  static const Color pending = Color(0xFFFF9800); // معلق
  static const Color completed = Color(0xFF4CAF50); // مكتمل
  static const Color cancelled = Color(0xFFF44336); // ملغي
  static const Color draft = Color(0xFF9E9E9E); // مسودة

  // ألوان للرسوم البيانية
  static const List<Color> chartColors = [
    Color(0xFF1976D2), // أزرق
    Color(0xFF4CAF50), // أخضر
    Color(0xFFF44336), // أحمر
    Color(0xFFFF9800), // برتقالي
    Color(0xFF9C27B0), // بنفسجي
    Color(0xFF607D8B), // رمادي مزرق
    Color(0xFF795548), // بني
    Color(0xFFE91E63), // وردي
    Color(0xFF009688), // تيل
    Color(0xFFFFEB3B), // أصفر
  ];

  // ألوان للثيم الداكن (للاستخدام المستقبلي)
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkPrimary = Color(0xFF90CAF9);
  static const Color darkSecondary = Color(0xFFCE93D8);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);

  // دوال مساعدة للألوان
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  static Color darken(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
}
