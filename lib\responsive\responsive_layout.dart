import 'package:flutter/material.dart';
import 'breakpoints.dart';

/// نظام التخطيط المتجاوب الذي يتكيف مع أحجام الشاشات المختلفة
class ResponsiveLayout extends StatelessWidget {
  /// التخطيط للهواتف المحمولة (أقل من 600px)
  final Widget mobile;
  
  /// التخطيط للأجهزة اللوحية (600px - 1200px)
  final Widget? tablet;
  
  /// التخطيط لأجهزة سطح المكتب (أكبر من 1200px)
  final Widget? desktop;
  
  /// التخطيط للشاشات الكبيرة جداً (أكبر من 1920px)
  final Widget? largeDesktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // شاشات كبيرة جداً
        if (width >= Breakpoints.largeDesktop && largeDesktop != null) {
          return largeDesktop!;
        }
        
        // أجهزة سطح المكتب
        if (width >= Breakpoints.desktop && desktop != null) {
          return desktop!;
        }
        
        // الأجهزة اللوحية
        if (width >= Breakpoints.tablet && tablet != null) {
          return tablet!;
        }
        
        // الهواتف المحمولة (افتراضي)
        return mobile;
      },
    );
  }
}

/// ويدجت مساعد لبناء تخطيط متجاوب بناءً على نوع الجهاز
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceType = DeviceType.fromWidth(constraints.maxWidth);
        return builder(context, deviceType);
      },
    );
  }
}

/// ويدجت للحصول على معلومات الشاشة الحالية
class ScreenInfo extends InheritedWidget {
  final DeviceType deviceType;
  final double width;
  final double height;
  final bool isPortrait;
  final bool isLandscape;

  const ScreenInfo({
    super.key,
    required this.deviceType,
    required this.width,
    required this.height,
    required this.isPortrait,
    required this.isLandscape,
    required super.child,
  });

  static ScreenInfo? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<ScreenInfo>();
  }

  @override
  bool updateShouldNotify(ScreenInfo oldWidget) {
    return deviceType != oldWidget.deviceType ||
           width != oldWidget.width ||
           height != oldWidget.height ||
           isPortrait != oldWidget.isPortrait;
  }
}

/// ويدجت لتوفير معلومات الشاشة للتطبيق
class ScreenInfoProvider extends StatelessWidget {
  final Widget child;

  const ScreenInfoProvider({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final mediaQuery = MediaQuery.of(context);
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;
        final deviceType = DeviceType.fromWidth(width);
        final isPortrait = mediaQuery.orientation == Orientation.portrait;
        final isLandscape = mediaQuery.orientation == Orientation.landscape;

        return ScreenInfo(
          deviceType: deviceType,
          width: width,
          height: height,
          isPortrait: isPortrait,
          isLandscape: isLandscape,
          child: child,
        );
      },
    );
  }
}

/// امتدادات مساعدة للحصول على معلومات الشاشة
extension ResponsiveExtensions on BuildContext {
  /// الحصول على نوع الجهاز الحالي
  DeviceType get deviceType {
    final screenInfo = ScreenInfo.of(this);
    if (screenInfo != null) {
      return screenInfo.deviceType;
    }
    
    final width = MediaQuery.of(this).size.width;
    return DeviceType.fromWidth(width);
  }

  /// التحقق من كون الجهاز هاتف محمول
  bool get isMobile => deviceType == DeviceType.mobile;

  /// التحقق من كون الجهاز لوحي
  bool get isTablet => deviceType == DeviceType.tablet;

  /// التحقق من كون الجهاز سطح مكتب
  bool get isDesktop => deviceType == DeviceType.desktop;

  /// التحقق من كون الجهاز شاشة كبيرة
  bool get isLargeDesktop => deviceType == DeviceType.largeDesktop;

  /// الحصول على عرض الشاشة
  double get screenWidth => MediaQuery.of(this).size.width;

  /// الحصول على ارتفاع الشاشة
  double get screenHeight => MediaQuery.of(this).size.height;

  /// التحقق من الاتجاه العمودي
  bool get isPortrait => MediaQuery.of(this).orientation == Orientation.portrait;

  /// التحقق من الاتجاه الأفقي
  bool get isLandscape => MediaQuery.of(this).orientation == Orientation.landscape;
}

/// ويدجت للتخطيط التكيفي بناءً على المحتوى
class AdaptiveLayout extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final double spacing;
  final bool forceVertical;
  final bool forceHorizontal;

  const AdaptiveLayout({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.spacing = 8.0,
    this.forceVertical = false,
    this.forceHorizontal = false,
  });

  @override
  Widget build(BuildContext context) {
    if (forceVertical || (!forceHorizontal && context.isMobile)) {
      return Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: _addSpacing(children, true),
      );
    } else {
      return Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: _addSpacing(children, false),
      );
    }
  }

  List<Widget> _addSpacing(List<Widget> widgets, bool isVertical) {
    if (widgets.isEmpty) return widgets;
    
    final spacedWidgets = <Widget>[];
    for (int i = 0; i < widgets.length; i++) {
      spacedWidgets.add(widgets[i]);
      if (i < widgets.length - 1) {
        spacedWidgets.add(
          isVertical 
            ? SizedBox(height: spacing)
            : SizedBox(width: spacing),
        );
      }
    }
    return spacedWidgets;
  }
}
