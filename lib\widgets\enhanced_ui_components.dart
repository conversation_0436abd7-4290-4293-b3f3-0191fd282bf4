import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_responsive.dart';
import '../constants/accessibility_constants.dart';
import 'animated_widgets.dart';

/// مكونات واجهة المستخدم المحسنة مع دعم كامل للتصميم الحديث وإمكانية الوصول
class EnhancedUIComponents {
  /// بطاقة محسنة مع رسوم متحركة وإمكانية وصول
  static Widget enhancedCard({
    required BuildContext context,
    required Widget child,
    String? title,
    String? subtitle,
    IconData? icon,
    VoidCallback? onTap,
    Color? backgroundColor,
    double? elevation,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    String? semanticLabel,
    String? semanticHint,
  }) {
    return AnimatedWidgets.animatedCard(
      margin: margin ?? AppResponsive.getPadding(context),
      padding: padding ?? const EdgeInsets.all(20),
      backgroundColor: backgroundColor ?? AppColors.surface,
      elevation: elevation ?? 4,
      onTap: onTap,
      child: AccessibilityConstants.accessibleWidget(
        label: semanticLabel ?? title ?? 'بطاقة',
        hint: semanticHint ?? (onTap != null ? 'اضغط للتفاعل' : null),
        button: onTap != null,
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null || title != null)
              Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      color: AppColors.primary,
                      size: AppResponsive.getFontSize(context, baseSize: 24),
                    ),
                    const SizedBox(width: 12),
                  ],
                  if (title != null)
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontSize: AppResponsive.getFontSize(
                            context,
                            baseSize: 18,
                          ),
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                ],
              ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: AppResponsive.getFontSize(context, baseSize: 14),
                  color: AppColors.textSecondary,
                ),
              ),
            ],
            if (title != null || subtitle != null) const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  /// زر محسن مع رسوم متحركة وإمكانية وصول
  static Widget enhancedButton({
    required BuildContext context,
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    Color? backgroundColor,
    Color? foregroundColor,
    bool isLoading = false,
    bool isEnabled = true,
    ButtonStyle? style,
    String? semanticLabel,
    String? semanticHint,
  }) {
    return AccessibilityConstants.accessibleWidget(
      label: semanticLabel ?? text,
      hint: semanticHint ?? 'زر $text',
      button: true,
      onTap: isEnabled && !isLoading ? onPressed : null,
      child: AnimatedWidgets.animatedButton(
        text: text,
        onPressed: onPressed,
        icon: icon,
        isLoading: isLoading,
        isEnabled: isEnabled,
        backgroundColor: backgroundColor,
        textColor: foregroundColor,
      ),
    );
  }

  /// حقل إدخال محسن مع إمكانية وصول
  static Widget enhancedTextField({
    required BuildContext context,
    required String label,
    String? hint,
    String? value,
    ValueChanged<String>? onChanged,
    TextInputType? keyboardType,
    bool obscureText = false,
    bool enabled = true,
    bool required = false,
    String? errorText,
    IconData? prefixIcon,
    Widget? suffixIcon,
    TextEditingController? controller,
    FocusNode? focusNode,
    int? maxLines,
    String? semanticLabel,
    String? semanticHint,
  }) {
    return AccessibilityConstants.accessibleWidget(
      label: semanticLabel ?? '$label${required ? ' مطلوب' : ''}',
      hint: semanticHint ?? hint,
      textField: true,
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        onChanged: onChanged,
        keyboardType: keyboardType,
        obscureText: obscureText,
        enabled: enabled,
        maxLines: maxLines ?? 1,
        style: TextStyle(
          fontSize: AppResponsive.getFontSize(context, baseSize: 16),
          color: AppColors.textPrimary,
        ),
        decoration: InputDecoration(
          labelText: '$label${required ? ' *' : ''}',
          hintText: hint,
          errorText: errorText,
          prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
          suffixIcon: suffixIcon,
          filled: true,
          fillColor: AppColors.surfaceContainer,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.outline),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.outline),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.error),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  /// قائمة محسنة مع رسوم متحركة
  static Widget enhancedList({
    required BuildContext context,
    required List<Widget> children,
    String? title,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    String? semanticLabel,
  }) {
    return AccessibilityConstants.accessibleWidget(
      label: semanticLabel ?? title ?? 'قائمة',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Padding(
              padding: padding ?? AppResponsive.getPadding(context),
              child: Text(
                title,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontSize: AppResponsive.getFontSize(context, baseSize: 20),
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          Expanded(
            child: AnimatedWidgets.animatedList(
              children: children,
              padding: padding,
            ),
          ),
        ],
      ),
    );
  }

  /// شريط تطبيق محسن ومتجاوب
  static PreferredSizeWidget enhancedAppBar({
    required BuildContext context,
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
    double? elevation,
    String? semanticLabel,
  }) {
    return AppResponsive.responsiveAppBar(
      context: context,
      title: title,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
    );
  }

  /// مؤشر تحميل محسن
  static Widget enhancedLoadingIndicator({
    required BuildContext context,
    String? message,
    Color? color,
    double? size,
  }) {
    return AccessibilityConstants.accessibleWidget(
      label: 'جاري التحميل',
      hint: message,
      child: AnimatedWidgets.animatedLoadingIndicator(
        message: message,
        color: color ?? AppColors.primary,
        size: size ?? 48,
      ),
    );
  }

  /// رسالة تنبيه محسنة
  static Widget enhancedAlert({
    required BuildContext context,
    required String message,
    required AlertType type,
    VoidCallback? onDismiss,
    Duration? displayDuration,
  }) {
    String semanticLabel;
    switch (type) {
      case AlertType.success:
        semanticLabel = 'رسالة نجاح';
        break;
      case AlertType.warning:
        semanticLabel = 'رسالة تحذير';
        break;
      case AlertType.error:
        semanticLabel = 'رسالة خطأ';
        break;
      case AlertType.info:
        semanticLabel = 'رسالة معلومات';
        break;
    }

    return AccessibilityConstants.accessibleWidget(
      label: semanticLabel,
      hint: message,
      child: AnimatedWidgets.animatedAlert(
        message: message,
        type: type,
        onDismiss: onDismiss,
        displayDuration: displayDuration ?? const Duration(seconds: 4),
      ),
    );
  }

  /// حاوي متجاوب محسن
  static Widget enhancedContainer({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? backgroundColor,
    double? maxWidth,
    String? semanticLabel,
  }) {
    return AccessibilityConstants.accessibleWidget(
      label: semanticLabel,
      child: AppResponsive.responsiveContainer(
        context: context,
        maxWidth: maxWidth,
        padding: padding,
        margin: margin,
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.background,
            borderRadius: BorderRadius.circular(16),
          ),
          child: child,
        ),
      ),
    );
  }

  /// نموذج متجاوب محسن
  static Widget enhancedForm({
    required BuildContext context,
    required List<Widget> fields,
    String? title,
    double? spacing,
    String? semanticLabel,
  }) {
    return AccessibilityConstants.accessibleWidget(
      label: semanticLabel ?? title ?? 'نموذج',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontSize: AppResponsive.getFontSize(context, baseSize: 20),
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 24),
          ],
          AppResponsive.responsiveForm(
            context: context,
            fields: fields,
            spacing: spacing ?? 16,
          ),
        ],
      ),
    );
  }
}
