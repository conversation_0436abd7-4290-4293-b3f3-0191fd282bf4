import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'keyboard_intents.dart';

/// خدمة إدارة اختصارات لوحة المفاتيح
/// تدير جميع اختصارات لوحة المفاتيح في التطبيق
class KeyboardShortcutsService {
  static final KeyboardShortcutsService _instance =
      KeyboardShortcutsService._internal();
  factory KeyboardShortcutsService() => _instance;
  KeyboardShortcutsService._internal();

  // ===============================
  // الاختصارات الأساسية
  // ===============================

  /// اختصارات التطبيق الأساسية
  static Map<LogicalKeySet, Intent> get globalShortcuts => {
    // اختصارات الملف
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN):
        const NewIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyO):
        const OpenIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS):
        const SaveIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyP):
        const PrintIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyQ):
        const QuitIntent(),

    // اختصارات التحرير
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyZ):
        const UndoIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyY):
        const RedoIntent(),
    LogicalKeySet(
      LogicalKeyboardKey.control,
      LogicalKeyboardKey.shift,
      LogicalKeyboardKey.keyZ,
    ): const RedoIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyC):
        const CopyIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyV):
        const PasteIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyX):
        const CutIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyA):
        const SelectAllIntent(),

    // اختصارات البحث والتنقل
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF):
        const SearchIntent(),
    LogicalKeySet(LogicalKeyboardKey.f3): const FindNextIntent(),
    LogicalKeySet(LogicalKeyboardKey.shift, LogicalKeyboardKey.f3):
        const FindPreviousIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyH):
        const ReplaceIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyG):
        const GoToIntent(),

    // اختصارات العرض
    LogicalKeySet(LogicalKeyboardKey.f11): const FullScreenIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.equal):
        const ZoomInIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.minus):
        const ZoomOutIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.digit0):
        const ResetZoomIntent(),

    // اختصارات التنقل
    LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.arrowLeft):
        const NavigateBackIntent(),
    LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.arrowRight):
        const NavigateForwardIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.tab):
        const NextTabIntent(),
    LogicalKeySet(
      LogicalKeyboardKey.control,
      LogicalKeyboardKey.shift,
      LogicalKeyboardKey.tab,
    ): const PreviousTabIntent(),

    // اختصارات المساعدة
    LogicalKeySet(LogicalKeyboardKey.f1): const HelpIntent(),
    LogicalKeySet(LogicalKeyboardKey.escape): const CancelIntent(),
    LogicalKeySet(LogicalKeyboardKey.enter): const ConfirmIntent(),
  };

  /// اختصارات النماذج
  static Map<LogicalKeySet, Intent> get formShortcuts => {
    ...globalShortcuts,

    // اختصارات النماذج المحددة
    LogicalKeySet(LogicalKeyboardKey.f2): const EditIntent(),
    LogicalKeySet(LogicalKeyboardKey.delete): const DeleteIntent(),
    LogicalKeySet(LogicalKeyboardKey.insert): const InsertIntent(),
    LogicalKeySet(LogicalKeyboardKey.f5): const RefreshIntent(),
    LogicalKeySet(LogicalKeyboardKey.f9): const CalculateIntent(),
    LogicalKeySet(LogicalKeyboardKey.f12): const SaveAsIntent(),

    // اختصارات التنقل في النماذج
    LogicalKeySet(LogicalKeyboardKey.tab): const NextFieldIntent(),
    LogicalKeySet(LogicalKeyboardKey.shift, LogicalKeyboardKey.tab):
        const PreviousFieldIntent(),
    LogicalKeySet(LogicalKeyboardKey.pageUp): const FirstRecordIntent(),
    LogicalKeySet(LogicalKeyboardKey.pageDown): const LastRecordIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.arrowUp):
        const PreviousRecordIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.arrowDown):
        const NextRecordIntent(),
  };

  /// اختصارات التقارير
  static Map<LogicalKeySet, Intent> get reportShortcuts => {
    ...globalShortcuts,

    // اختصارات التقارير المحددة
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyR):
        const RunReportIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyE):
        const ExportIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyD):
        const PrintPreviewIntent(),
    LogicalKeySet(LogicalKeyboardKey.f6): const FilterIntent(),
    LogicalKeySet(LogicalKeyboardKey.f7): const SortIntent(),
    LogicalKeySet(LogicalKeyboardKey.f8): const GroupByIntent(),
  };

  // ===============================
  // الاختصارات المحاسبية المخصصة
  // ===============================

  /// اختصارات الحسابات
  static Map<LogicalKeySet, Intent> get accountShortcuts => {
    ...formShortcuts,

    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyB):
        const BalanceSheetIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyI):
        const IncomeStatementIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyT):
        const TrialBalanceIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyL):
        const LedgerIntent(),
  };

  /// اختصارات القيود المحاسبية
  static Map<LogicalKeySet, Intent> get journalShortcuts => {
    ...formShortcuts,

    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyJ):
        const NewJournalEntryIntent(),
    LogicalKeySet(LogicalKeyboardKey.f4): const PostJournalIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyU):
        const UnpostJournalIntent(),
    LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.keyD):
        const AddDebitLineIntent(),
    LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.keyC):
        const AddCreditLineIntent(),
  };

  /// اختصارات الفواتير
  static Map<LogicalKeySet, Intent> get invoiceShortcuts => {
    ...formShortcuts,

    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyM):
        const NewInvoiceIntent(),
    LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.keyL):
        const AddLineItemIntent(),
    LogicalKeySet(LogicalKeyboardKey.f10): const CalculateTotalIntent(),
    LogicalKeySet(
      LogicalKeyboardKey.control,
      LogicalKeyboardKey.shift,
      LogicalKeyboardKey.keyP,
    ): const PrintInvoiceIntent(),
  };

  // ===============================
  // إدارة الاختصارات
  // ===============================

  /// الحصول على اختصارات حسب نوع الشاشة
  static Map<LogicalKeySet, Intent> getShortcutsForScreen(String screenType) {
    switch (screenType.toLowerCase()) {
      case 'account':
      case 'accounts':
        return accountShortcuts;
      case 'journal':
      case 'journal_entry':
        return journalShortcuts;
      case 'invoice':
      case 'invoices':
        return invoiceShortcuts;
      case 'report':
      case 'reports':
        return reportShortcuts;
      case 'form':
        return formShortcuts;
      default:
        return globalShortcuts;
    }
  }

  /// الحصول على وصف الاختصار
  static String getShortcutDescription(LogicalKeySet keySet) {
    final shortcuts = _getAllShortcutDescriptions();
    return shortcuts[keySet] ?? 'غير محدد';
  }

  /// الحصول على جميع أوصاف الاختصارات
  static Map<LogicalKeySet, String> _getAllShortcutDescriptions() {
    return {
      // اختصارات الملف
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN):
          'جديد',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyO): 'فتح',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS): 'حفظ',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyP):
          'طباعة',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyQ):
          'خروج',

      // اختصارات التحرير
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyZ):
          'تراجع',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyY):
          'إعادة',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyC): 'نسخ',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyV): 'لصق',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyX): 'قص',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyA):
          'تحديد الكل',

      // اختصارات البحث
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF): 'بحث',
      LogicalKeySet(LogicalKeyboardKey.f3): 'البحث التالي',
      LogicalKeySet(LogicalKeyboardKey.shift, LogicalKeyboardKey.f3):
          'البحث السابق',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyH):
          'استبدال',

      // اختصارات النماذج
      LogicalKeySet(LogicalKeyboardKey.f2): 'تحرير',
      LogicalKeySet(LogicalKeyboardKey.delete): 'حذف',
      LogicalKeySet(LogicalKeyboardKey.insert): 'إدراج',
      LogicalKeySet(LogicalKeyboardKey.f5): 'تحديث',

      // اختصارات محاسبية
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyJ):
          'قيد محاسبي جديد',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyM):
          'فاتورة جديدة',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyB):
          'الميزانية العمومية',
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyI):
          'قائمة الدخل',

      // اختصارات أخرى
      LogicalKeySet(LogicalKeyboardKey.f1): 'مساعدة',
      LogicalKeySet(LogicalKeyboardKey.escape): 'إلغاء',
      LogicalKeySet(LogicalKeyboardKey.enter): 'تأكيد',
    };
  }

  /// الحصول على قائمة الاختصارات مجمعة حسب الفئة
  static Map<String, Map<LogicalKeySet, String>> getShortcutsByCategory() {
    return {
      'ملف': {
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN):
            'جديد',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyO):
            'فتح',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS):
            'حفظ',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyP):
            'طباعة',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyQ):
            'خروج',
      },
      'تحرير': {
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyZ):
            'تراجع',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyY):
            'إعادة',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyC):
            'نسخ',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyV):
            'لصق',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyX):
            'قص',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyA):
            'تحديد الكل',
      },
      'بحث': {
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF):
            'بحث',
        LogicalKeySet(LogicalKeyboardKey.f3): 'البحث التالي',
        LogicalKeySet(LogicalKeyboardKey.shift, LogicalKeyboardKey.f3):
            'البحث السابق',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyH):
            'استبدال',
      },
      'محاسبة': {
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyJ):
            'قيد محاسبي جديد',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyM):
            'فاتورة جديدة',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyB):
            'الميزانية العمومية',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyI):
            'قائمة الدخل',
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyT):
            'ميزان المراجعة',
      },
      'تنقل': {
        LogicalKeySet(LogicalKeyboardKey.tab): 'الحقل التالي',
        LogicalKeySet(LogicalKeyboardKey.shift, LogicalKeyboardKey.tab):
            'الحقل السابق',
        LogicalKeySet(LogicalKeyboardKey.pageUp): 'أول سجل',
        LogicalKeySet(LogicalKeyboardKey.pageDown): 'آخر سجل',
      },
      'مساعدة': {
        LogicalKeySet(LogicalKeyboardKey.f1): 'مساعدة',
        LogicalKeySet(LogicalKeyboardKey.escape): 'إلغاء',
        LogicalKeySet(LogicalKeyboardKey.enter): 'تأكيد',
      },
    };
  }
}
