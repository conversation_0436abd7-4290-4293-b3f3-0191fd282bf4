import 'package:flutter/material.dart';
import '../services/encryption_service.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../constants/app_theme.dart';
import 'home_screen.dart';
import 'password_setup_screen.dart';

/// شاشة تسجيل الدخول بكلمة مرور قاعدة البيانات
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;
  int _failedAttempts = 0;
  static const int _maxFailedAttempts = 5;

  @override
  void initState() {
    super.initState();
    _checkEncryptionSetup();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _checkEncryptionSetup() async {
    final isSetup = await EncryptionService.isEncryptionSetup();
    if (!isSetup && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const PasswordSetupScreen(isFirstTime: true),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 60),

              // الشعار والعنوان
              _buildHeader(),

              const SizedBox(height: 60),

              // النموذج
              _buildForm(),

              const SizedBox(height: 24),

              // زر تسجيل الدخول
              _buildLoginButton(),

              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                _buildErrorMessage(),
              ],

              const SizedBox(height: 32),

              // خيارات إضافية
              _buildAdditionalOptions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.account_balance,
            size: 60,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 24),
        const Text(
          'Smart Ledger',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: AppTheme.textColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'دفتر الحسابات الذكي',
          style: TextStyle(
            fontSize: 18,
            color: AppTheme.textColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'أدخل كلمة مرور قاعدة البيانات للمتابعة',
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.textColor.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: TextFormField(
        controller: _passwordController,
        obscureText: !_isPasswordVisible,
        enabled: !_isLoading,
        decoration: InputDecoration(
          labelText: 'كلمة مرور قاعدة البيانات',
          prefixIcon: const Icon(Icons.lock),
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: AppTheme.cardColor,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'كلمة المرور مطلوبة';
          }
          return null;
        },
        onFieldSubmitted: (_) => _handleLogin(),
      ),
    );
  }

  Widget _buildLoginButton() {
    return ElevatedButton(
      onPressed: _isLoading || _failedAttempts >= _maxFailedAttempts
          ? null
          : _handleLogin,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              _failedAttempts >= _maxFailedAttempts
                  ? 'تم حظر المحاولات'
                  : 'تسجيل الدخول',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalOptions() {
    return Column(
      children: [
        if (_failedAttempts > 0) ...[
          Text(
            'المحاولات المتبقية: ${_maxFailedAttempts - _failedAttempts}',
            style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
        ],

        TextButton(
          onPressed: _isLoading ? null : _showResetDialog,
          child: Text(
            'نسيت كلمة المرور؟',
            style: TextStyle(
              color: AppTheme.primaryColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final password = _passwordController.text;

      // تهيئة قاعدة البيانات مع كلمة المرور
      final databaseHelper = DatabaseHelper();
      final success = await databaseHelper.initializeDatabase(password);

      if (success) {
        LoggingService.security('تم تسجيل الدخول بنجاح', category: 'Login');

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      } else {
        _failedAttempts++;
        LoggingService.security(
          'فشل في تسجيل الدخول - المحاولة $_failedAttempts',
          category: 'Login',
        );

        setState(() {
          if (_failedAttempts >= _maxFailedAttempts) {
            _errorMessage = 'تم حظر المحاولات بسبب كثرة المحاولات الفاشلة';
          } else {
            _errorMessage =
                'كلمة مرور خاطئة. المحاولات المتبقية: ${_maxFailedAttempts - _failedAttempts}';
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تسجيل الدخول: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين كلمة المرور'),
        content: const Text(
          'تحذير: إعادة تعيين كلمة المرور سيؤدي إلى فقدان جميع البيانات المشفرة.\n\nهل أنت متأكد من المتابعة؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _resetEncryption();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _resetEncryption() async {
    final success = await EncryptionService.resetEncryption();
    if (success && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const PasswordSetupScreen(isFirstTime: true),
        ),
      );
    }
  }
}
