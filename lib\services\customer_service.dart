import '../database/database_helper.dart';
import '../models/customer.dart';
import '../constants/app_constants.dart';
import 'validation_service.dart';
import '../exceptions/validation_exception.dart';
import 'data_sanitization_service.dart';

class CustomerService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<Customer>> getAllCustomers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  Future<List<Customer>> getActiveCustomers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  Future<Customer?> getCustomerById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  Future<Customer?> getCustomerByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  Future<int> insertCustomer(Customer customer) async {
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات باستخدام ValidationService
    final validation = ValidationService.validateCustomer(
      code: customer.code,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
    );

    if (!validation.isValid) {
      throw ValidationException(validation.errorMessage!);
    }

    // التحقق من عدم تكرار الكود
    final existingCustomer = await getCustomerByCode(customer.code);
    if (existingCustomer != null) {
      throw ValidationException.duplicate('كود العميل', customer.code);
    }

    // تنظيف البيانات باستخدام DataSanitizationService
    final sanitizedData = DataSanitizationService.sanitizeCustomerData(
      code: customer.code,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
    );

    final cleanCustomer = customer.copyWith(
      code: sanitizedData['code']!,
      name: sanitizedData['name']!,
      phone: sanitizedData['phone']!.isEmpty ? null : sanitizedData['phone']!,
      email: sanitizedData['email']!.isEmpty ? null : sanitizedData['email']!,
      address: sanitizedData['address']!.isEmpty
          ? null
          : sanitizedData['address']!,
    );

    final customerData = cleanCustomer.toMap();
    customerData.remove('id'); // إزالة المعرف للإدراج التلقائي

    return await db.insert(AppConstants.customersTable, customerData);
  }

  Future<int> updateCustomer(Customer customer) async {
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات باستخدام ValidationService
    final validation = ValidationService.validateCustomer(
      code: customer.code,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
    );

    if (!validation.isValid) {
      throw ValidationException(validation.errorMessage!);
    }

    // التحقق من عدم تكرار الكود مع عملاء آخرين
    final existingCustomer = await getCustomerByCode(customer.code);
    if (existingCustomer != null && existingCustomer.id != customer.id) {
      throw ValidationException.duplicate('كود العميل', customer.code);
    }

    // تنظيف البيانات
    final cleanCustomer = customer.copyWith(
      name: ValidationService.sanitizeText(customer.name),
      phone: customer.phone != null
          ? ValidationService.sanitizeText(customer.phone!)
          : null,
      email: customer.email != null
          ? ValidationService.sanitizeText(customer.email!)
          : null,
      address: customer.address != null
          ? ValidationService.sanitizeText(customer.address!)
          : null,
      updatedAt: DateTime.now(),
    );

    final customerData = cleanCustomer.toMap();

    return await db.update(
      AppConstants.customersTable,
      customerData,
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  Future<int> deleteCustomer(int id) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم وجود فواتير للعميل
    final invoices = await db.query(
      AppConstants.invoicesTable,
      where: 'customer_id = ?',
      whereArgs: [id],
      limit: 1,
    );
    final hasInvoices = invoices.isNotEmpty;

    // التحقق من عدم وجود قيود محاسبية للعميل
    final journalEntries = await db.query(
      AppConstants.journalEntryDetailsTable,
      where: 'account_id = ?',
      whereArgs: [id],
      limit: 1,
    );
    final hasJournalEntries = journalEntries.isNotEmpty;

    // استخدام ValidationService للتحقق من قواعد الحذف
    ValidationService.validateCustomerDeletion(
      id,
      hasInvoices: hasInvoices,
      hasJournalEntries: hasJournalEntries,
    );

    return await db.delete(
      AppConstants.customersTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateCustomerBalance(
    int customerId,
    double amount,
    bool isDebit,
  ) async {
    final db = await _databaseHelper.database;
    final customer = await getCustomerById(customerId);

    if (customer == null) {
      throw Exception('العميل غير موجود');
    }

    double newBalance = customer.balance;

    // العملاء: المدين يزيد الرصيد (دين على العميل)، الدائن يقلل الرصيد (دفع من العميل)
    newBalance += isDebit ? amount : -amount;

    await db.update(
      AppConstants.customersTable,
      {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [customerId],
    );
  }

  Future<List<Customer>> searchCustomers(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: '(name LIKE ? OR code LIKE ? OR phone LIKE ?) AND is_active = 1',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  Future<String> generateCustomerCode() async {
    final db = await _databaseHelper.database;

    // استعلام محسن للحصول على أكبر رقم كود
    final result = await db.rawQuery('''
      SELECT MAX(CAST(SUBSTR(code, 2) AS INTEGER)) as max_number
      FROM ${AppConstants.customersTable}
      WHERE code LIKE 'C%' AND code GLOB 'C[0-9]*'
    ''');

    final maxNumber = result.first['max_number'] as int? ?? 0;
    return 'C${(maxNumber + 1).toString().padLeft(4, '0')}';
  }

  Future<List<Customer>> getCustomersWithDebt() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: 'balance > 0 AND is_active = 1',
      orderBy: 'balance DESC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  Future<List<Customer>> getCustomersWithCredit() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: 'balance < 0 AND is_active = 1',
      orderBy: 'balance ASC',
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  Future<Map<String, double>> getCustomersSummary() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_customers,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_customers,
        COUNT(CASE WHEN balance > 0 THEN 1 END) as customers_with_debt,
        COUNT(CASE WHEN balance < 0 THEN 1 END) as customers_with_credit,
        SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END) as total_debt,
        SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END) as total_credit
      FROM ${AppConstants.customersTable}
    ''');

    final row = result.first;
    return {
      'total_customers': (row['total_customers'] as num?)?.toDouble() ?? 0.0,
      'active_customers': (row['active_customers'] as num?)?.toDouble() ?? 0.0,
      'customers_with_debt':
          (row['customers_with_debt'] as num?)?.toDouble() ?? 0.0,
      'customers_with_credit':
          (row['customers_with_credit'] as num?)?.toDouble() ?? 0.0,
      'total_debt': (row['total_debt'] as num?)?.toDouble() ?? 0.0,
      'total_credit': (row['total_credit'] as num?)?.toDouble() ?? 0.0,
    };
  }
}
