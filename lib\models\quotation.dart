/// نموذج عرض السعر
/// يحتوي على جميع البيانات المتعلقة بعروض الأسعار
library;

/// حالات عرض السعر
enum QuotationStatus {
  draft('draft', 'مسودة'),
  sent('sent', 'مرسل'),
  accepted('accepted', 'مقبول'),
  rejected('rejected', 'مرفوض'),
  expired('expired', 'منتهي الصلاحية'),
  converted('converted', 'محول إلى فاتورة');

  const QuotationStatus(this.code, this.displayName);
  final String code;
  final String displayName;

  static QuotationStatus fromCode(String code) {
    return QuotationStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => QuotationStatus.draft,
    );
  }
}

/// عنصر في عرض السعر
class QuotationItem {
  final int? id;
  final int quotationId;
  final int itemId;
  final String itemName;
  final String itemCode;
  final double quantity;
  final double unitPrice;
  final double totalPrice;
  final double discountPercentage;
  final double discountAmount;
  final double taxPercentage;
  final double taxAmount;
  final double netAmount;
  final DateTime createdAt;

  QuotationItem({
    this.id,
    required this.quotationId,
    required this.itemId,
    required this.itemName,
    required this.itemCode,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    this.taxPercentage = 0.0,
    this.taxAmount = 0.0,
    required this.netAmount,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quotation_id': quotationId,
      'item_id': itemId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'tax_percentage': taxPercentage,
      'tax_amount': taxAmount,
      'net_amount': netAmount,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory QuotationItem.fromMap(Map<String, dynamic> map) {
    return QuotationItem(
      id: map['id']?.toInt(),
      quotationId: map['quotation_id']?.toInt() ?? 0,
      itemId: map['item_id']?.toInt() ?? 0,
      itemName: map['item_name'] ?? '',
      itemCode: map['item_code'] ?? '',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unitPrice: map['unit_price']?.toDouble() ?? 0.0,
      totalPrice: map['total_price']?.toDouble() ?? 0.0,
      discountPercentage: map['discount_percentage']?.toDouble() ?? 0.0,
      discountAmount: map['discount_amount']?.toDouble() ?? 0.0,
      taxPercentage: map['tax_percentage']?.toDouble() ?? 0.0,
      taxAmount: map['tax_amount']?.toDouble() ?? 0.0,
      netAmount: map['net_amount']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  QuotationItem copyWith({
    int? id,
    int? quotationId,
    int? itemId,
    String? itemName,
    String? itemCode,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountPercentage,
    double? discountAmount,
    double? taxPercentage,
    double? taxAmount,
    double? netAmount,
    DateTime? createdAt,
  }) {
    return QuotationItem(
      id: id ?? this.id,
      quotationId: quotationId ?? this.quotationId,
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      itemCode: itemCode ?? this.itemCode,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      taxPercentage: taxPercentage ?? this.taxPercentage,
      taxAmount: taxAmount ?? this.taxAmount,
      netAmount: netAmount ?? this.netAmount,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// نموذج عرض السعر الرئيسي
class Quotation {
  final int? id;
  final String quotationNumber;
  final DateTime quotationDate;
  final DateTime? validUntil;
  final int? customerId;
  final int? supplierId;
  final String? customerName;
  final String? supplierName;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final int currencyId;
  final QuotationStatus status;
  final String? notes;
  final String? terms;
  final String? reference;
  final int? convertedToInvoice;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<QuotationItem> items;

  Quotation({
    this.id,
    required this.quotationNumber,
    required this.quotationDate,
    this.validUntil,
    this.customerId,
    this.supplierId,
    this.customerName,
    this.supplierName,
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.totalAmount = 0.0,
    required this.currencyId,
    this.status = QuotationStatus.draft,
    this.notes,
    this.terms,
    this.reference,
    this.convertedToInvoice,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.items = const [],
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quotation_number': quotationNumber,
      'quotation_date': quotationDate.toIso8601String().split('T')[0],
      'valid_until': validUntil?.toIso8601String().split('T')[0],
      'customer_id': customerId,
      'supplier_id': supplierId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'currency_id': currencyId,
      'status': status.code,
      'notes': notes,
      'terms': terms,
      'reference': reference,
      'converted_to_invoice': convertedToInvoice,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Quotation.fromMap(Map<String, dynamic> map) {
    return Quotation(
      id: map['id']?.toInt(),
      quotationNumber: map['quotation_number'] ?? '',
      quotationDate: DateTime.parse(map['quotation_date']),
      validUntil: map['valid_until'] != null ? DateTime.parse(map['valid_until']) : null,
      customerId: map['customer_id']?.toInt(),
      supplierId: map['supplier_id']?.toInt(),
      customerName: map['customer_name'],
      supplierName: map['supplier_name'],
      subtotal: map['subtotal']?.toDouble() ?? 0.0,
      taxAmount: map['tax_amount']?.toDouble() ?? 0.0,
      discountAmount: map['discount_amount']?.toDouble() ?? 0.0,
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      currencyId: map['currency_id']?.toInt() ?? 1,
      status: QuotationStatus.fromCode(map['status'] ?? 'draft'),
      notes: map['notes'],
      terms: map['terms'],
      reference: map['reference'],
      convertedToInvoice: map['converted_to_invoice']?.toInt(),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Quotation copyWith({
    int? id,
    String? quotationNumber,
    DateTime? quotationDate,
    DateTime? validUntil,
    int? customerId,
    int? supplierId,
    String? customerName,
    String? supplierName,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    int? currencyId,
    QuotationStatus? status,
    String? notes,
    String? terms,
    String? reference,
    int? convertedToInvoice,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<QuotationItem>? items,
  }) {
    return Quotation(
      id: id ?? this.id,
      quotationNumber: quotationNumber ?? this.quotationNumber,
      quotationDate: quotationDate ?? this.quotationDate,
      validUntil: validUntil ?? this.validUntil,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      customerName: customerName ?? this.customerName,
      supplierName: supplierName ?? this.supplierName,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      currencyId: currencyId ?? this.currencyId,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      terms: terms ?? this.terms,
      reference: reference ?? this.reference,
      convertedToInvoice: convertedToInvoice ?? this.convertedToInvoice,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
    );
  }

  // خصائص مساعدة
  bool get isExpired {
    if (validUntil == null) return false;
    return DateTime.now().isAfter(validUntil!);
  }

  bool get canBeConverted {
    return status == QuotationStatus.accepted && convertedToInvoice == null;
  }

  bool get isConverted {
    return convertedToInvoice != null;
  }

  String get displayStatus {
    if (isExpired && status != QuotationStatus.converted) {
      return QuotationStatus.expired.displayName;
    }
    return status.displayName;
  }

  @override
  String toString() {
    return 'Quotation(id: $id, number: $quotationNumber, total: $totalAmount)';
  }
}
