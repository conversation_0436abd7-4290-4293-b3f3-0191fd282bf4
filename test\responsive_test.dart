import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/responsive/responsive.dart';
import 'package:smart_ledger/main.dart';

void main() {
  group('اختبارات التصميم المتجاوب', () {
    testWidgets('اختبار ResponsiveLayout على أحجام شاشات مختلفة', (WidgetTester tester) async {
      // اختبار الهاتف المحمول (320x568)
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(
        MaterialApp(
          home: ResponsiveLayout(
            mobile: Container(
              key: const Key('mobile'),
              child: const Text('Mobile Layout'),
            ),
            tablet: Container(
              key: const Key('tablet'),
              child: const Text('Tablet Layout'),
            ),
            desktop: Container(
              key: const Key('desktop'),
              child: const Text('Desktop Layout'),
            ),
          ),
        ),
      );

      expect(find.byKey(const Key('mobile')), findsOneWidget);
      expect(find.byKey(const Key('tablet')), findsNothing);
      expect(find.byKey(const Key('desktop')), findsNothing);

      // اختبار الجهاز اللوحي (768x1024)
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(
        MaterialApp(
          home: ResponsiveLayout(
            mobile: Container(
              key: const Key('mobile'),
              child: const Text('Mobile Layout'),
            ),
            tablet: Container(
              key: const Key('tablet'),
              child: const Text('Tablet Layout'),
            ),
            desktop: Container(
              key: const Key('desktop'),
              child: const Text('Desktop Layout'),
            ),
          ),
        ),
      );

      expect(find.byKey(const Key('mobile')), findsNothing);
      expect(find.byKey(const Key('tablet')), findsOneWidget);
      expect(find.byKey(const Key('desktop')), findsNothing);

      // اختبار سطح المكتب (1920x1080)
      await tester.binding.setSurfaceSize(const Size(1920, 1080));
      await tester.pumpWidget(
        MaterialApp(
          home: ResponsiveLayout(
            mobile: Container(
              key: const Key('mobile'),
              child: const Text('Mobile Layout'),
            ),
            tablet: Container(
              key: const Key('tablet'),
              child: const Text('Tablet Layout'),
            ),
            desktop: Container(
              key: const Key('desktop'),
              child: const Text('Desktop Layout'),
            ),
          ),
        ),
      );

      expect(find.byKey(const Key('mobile')), findsNothing);
      expect(find.byKey(const Key('tablet')), findsNothing);
      expect(find.byKey(const Key('desktop')), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار نقاط الكسر (Breakpoints)', (WidgetTester tester) async {
      // اختبار نقاط الكسر المختلفة
      expect(Breakpoints.getDeviceType(320), DeviceType.mobile);
      expect(Breakpoints.getDeviceType(480), DeviceType.mobile);
      expect(Breakpoints.getDeviceType(600), DeviceType.tablet);
      expect(Breakpoints.getDeviceType(768), DeviceType.tablet);
      expect(Breakpoints.getDeviceType(1200), DeviceType.desktop);
      expect(Breakpoints.getDeviceType(1920), DeviceType.largeDesktop);
      expect(Breakpoints.getDeviceType(2560), DeviceType.largeDesktop);

      // اختبار عدد الأعمدة
      expect(Breakpoints.getGridColumns(320), 1);
      expect(Breakpoints.getGridColumns(600), 2);
      expect(Breakpoints.getGridColumns(900), 3);
      expect(Breakpoints.getGridColumns(1200), 4);
      expect(Breakpoints.getGridColumns(1920), 6);
    });

    testWidgets('اختبار AppDimensions على أحجام مختلفة', (WidgetTester tester) async {
      // اختبار الهاتف المحمول
      var dimensions = AppDimensions.fromWidth(320);
      expect(dimensions.gridColumns, 1);
      expect(dimensions.fontSizeH1, 24);
      expect(dimensions.paddingM, 16);

      // اختبار الجهاز اللوحي
      dimensions = AppDimensions.fromWidth(768);
      expect(dimensions.gridColumns, 2);
      expect(dimensions.fontSizeH1, 28);
      expect(dimensions.paddingM, 20);

      // اختبار سطح المكتب
      dimensions = AppDimensions.fromWidth(1200);
      expect(dimensions.gridColumns, 4);
      expect(dimensions.fontSizeH1, 32);
      expect(dimensions.paddingM, 24);

      // اختبار الشاشة الكبيرة
      dimensions = AppDimensions.fromWidth(1920);
      expect(dimensions.gridColumns, 6);
      expect(dimensions.fontSizeH1, 36);
      expect(dimensions.paddingM, 28);
    });

    testWidgets('اختبار ResponsiveText على أحجام مختلفة', (WidgetTester tester) async {
      // اختبار على الهاتف المحمول
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenInfoProvider(
              child: Column(
                children: const [
                  ResponsiveText.h1('عنوان رئيسي'),
                  ResponsiveText.h2('عنوان ثانوي'),
                  ResponsiveText.body('نص عادي'),
                ],
              ),
            ),
          ),
        ),
      );

      // التحقق من وجود النصوص
      expect(find.text('عنوان رئيسي'), findsOneWidget);
      expect(find.text('عنوان ثانوي'), findsOneWidget);
      expect(find.text('نص عادي'), findsOneWidget);

      // اختبار على الجهاز اللوحي
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenInfoProvider(
              child: Column(
                children: const [
                  ResponsiveText.h1('عنوان رئيسي'),
                  ResponsiveText.h2('عنوان ثانوي'),
                  ResponsiveText.body('نص عادي'),
                ],
              ),
            ),
          ),
        ),
      );

      expect(find.text('عنوان رئيسي'), findsOneWidget);
      expect(find.text('عنوان ثانوي'), findsOneWidget);
      expect(find.text('نص عادي'), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار ResponsiveGrid على أحجام مختلفة', (WidgetTester tester) async {
      final testItems = List.generate(6, (index) => 
        Container(
          key: Key('item_$index'),
          child: Text('Item $index'),
        ),
      );

      // اختبار على الهاتف المحمول
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenInfoProvider(
              child: ResponsiveGrid(
                children: testItems,
              ),
            ),
          ),
        ),
      );

      // التحقق من وجود العناصر
      for (int i = 0; i < 6; i++) {
        expect(find.byKey(Key('item_$i')), findsOneWidget);
      }

      // اختبار على الجهاز اللوحي
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenInfoProvider(
              child: ResponsiveGrid(
                children: testItems,
              ),
            ),
          ),
        ),
      );

      for (int i = 0; i < 6; i++) {
        expect(find.byKey(Key('item_$i')), findsOneWidget);
      }

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار AdaptiveLayout', (WidgetTester tester) async {
      final testWidgets = [
        Container(key: const Key('widget1'), child: const Text('Widget 1')),
        Container(key: const Key('widget2'), child: const Text('Widget 2')),
        Container(key: const Key('widget3'), child: const Text('Widget 3')),
      ];

      // اختبار على الهاتف المحمول (يجب أن يكون عمودي)
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenInfoProvider(
              child: AdaptiveLayout(
                children: testWidgets,
              ),
            ),
          ),
        ),
      );

      // التحقق من وجود العناصر
      expect(find.byKey(const Key('widget1')), findsOneWidget);
      expect(find.byKey(const Key('widget2')), findsOneWidget);
      expect(find.byKey(const Key('widget3')), findsOneWidget);

      // اختبار على سطح المكتب (يجب أن يكون أفقي)
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ScreenInfoProvider(
              child: AdaptiveLayout(
                children: testWidgets,
              ),
            ),
          ),
        ),
      );

      expect(find.byKey(const Key('widget1')), findsOneWidget);
      expect(find.byKey(const Key('widget2')), findsOneWidget);
      expect(find.byKey(const Key('widget3')), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('اختبار الشاشة الرئيسية على أحجام مختلفة', (WidgetTester tester) async {
      // اختبار على الهاتف المحمول
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(const SmartLedgerApp());
      await tester.pumpAndSettle();

      // التحقق من وجود العناصر الأساسية
      expect(find.text('دفتر الحسابات الذكي'), findsOneWidget);

      // اختبار على الجهاز اللوحي
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(const SmartLedgerApp());
      await tester.pumpAndSettle();

      expect(find.text('دفتر الحسابات الذكي'), findsOneWidget);

      // اختبار على سطح المكتب
      await tester.binding.setSurfaceSize(const Size(1920, 1080));
      await tester.pumpWidget(const SmartLedgerApp());
      await tester.pumpAndSettle();

      expect(find.text('دفتر الحسابات الذكي'), findsOneWidget);

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });
  });

  group('اختبارات الأداء للتصميم المتجاوب', () {
    testWidgets('اختبار أداء تغيير حجم الشاشة', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();

      // بناء التطبيق على حجم صغير
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(const SmartLedgerApp());
      await tester.pumpAndSettle();

      // تغيير إلى حجم كبير
      await tester.binding.setSurfaceSize(const Size(1920, 1080));
      await tester.pumpAndSettle();

      stopwatch.stop();

      // التحقق من أن الوقت معقول (أقل من ثانية واحدة)
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));

      // إعادة تعيين حجم الشاشة
      await tester.binding.setSurfaceSize(null);
    });
  });
}
