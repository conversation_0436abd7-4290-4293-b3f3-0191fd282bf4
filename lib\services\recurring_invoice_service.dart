/// خدمة الفواتير المتكررة
/// توفر جميع العمليات المتعلقة بإدارة الفواتير المتكررة والدورية
library;

import 'dart:async';
import '../database/database_helper.dart';
import '../models/recurring_invoice.dart';

import '../constants/app_constants.dart';
import '../services/invoice_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

class RecurringInvoiceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();

  Timer? _schedulerTimer;
  static const Duration _checkInterval = Duration(hours: 1); // فحص كل ساعة

  /// بدء جدولة الفواتير المتكررة
  void startScheduler() {
    _schedulerTimer?.cancel();
    _schedulerTimer = Timer.periodic(_checkInterval, (timer) {
      _processRecurringInvoices();
    });

    LoggingService.info(
      'تم بدء جدولة الفواتير المتكررة',
      category: 'RecurringInvoiceService',
    );
  }

  /// إيقاف جدولة الفواتير المتكررة
  void stopScheduler() {
    _schedulerTimer?.cancel();
    _schedulerTimer = null;

    LoggingService.info(
      'تم إيقاف جدولة الفواتير المتكررة',
      category: 'RecurringInvoiceService',
    );
  }

  /// الحصول على جميع الفواتير المتكررة
  Future<List<RecurringInvoice>> getAllRecurringInvoices() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT ri.*, 
               c.name as customer_name,
               s.name as supplier_name
        FROM ${AppConstants.recurringInvoicesTable} ri
        LEFT JOIN ${AppConstants.customersTable} c ON ri.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON ri.supplier_id = s.id
        ORDER BY ri.next_generation_date ASC, ri.template_name ASC
      ''');

      return List.generate(maps.length, (i) {
        return RecurringInvoice.fromMap(maps[i]);
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الفواتير المتكررة',
        category: 'RecurringInvoiceService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على فاتورة متكررة بالمعرف
  Future<RecurringInvoice?> getRecurringInvoiceById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
        '''
        SELECT ri.*, 
               c.name as customer_name,
               s.name as supplier_name
        FROM ${AppConstants.recurringInvoicesTable} ri
        LEFT JOIN ${AppConstants.customersTable} c ON ri.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON ri.supplier_id = s.id
        WHERE ri.id = ?
      ''',
        [id],
      );

      if (maps.isNotEmpty) {
        return RecurringInvoice.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء فاتورة متكررة جديدة
  Future<int> insertRecurringInvoice(RecurringInvoice recurringInvoice) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار اسم النموذج
      final existingTemplate = await getRecurringInvoiceByName(
        recurringInvoice.templateName,
      );
      if (existingTemplate != null) {
        throw Exception('اسم النموذج موجود مسبقاً');
      }

      final recurringInvoiceData = recurringInvoice.toMap();
      recurringInvoiceData.remove('id');

      final id = await db.insert(
        AppConstants.recurringInvoicesTable,
        recurringInvoiceData,
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'INSERT',
        entityType: 'recurring_invoices',
        entityId: id,
        entityName: recurringInvoice.templateName,
        description:
            'إنشاء فاتورة متكررة جديدة: ${recurringInvoice.templateName}',
        newValues: recurringInvoiceData,
      );

      LoggingService.info(
        'تم إنشاء فاتورة متكررة جديدة',
        category: 'RecurringInvoiceService',
        data: {'id': id, 'templateName': recurringInvoice.templateName},
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {
          'templateName': recurringInvoice.templateName,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// تحديث فاتورة متكررة
  Future<void> updateRecurringInvoice(RecurringInvoice recurringInvoice) async {
    try {
      final db = await _databaseHelper.database;

      final recurringInvoiceData = recurringInvoice.toMap();
      recurringInvoiceData['updated_at'] = DateTime.now().toIso8601String();

      await db.update(
        AppConstants.recurringInvoicesTable,
        recurringInvoiceData,
        where: 'id = ?',
        whereArgs: [recurringInvoice.id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'recurring_invoices',
        entityId: recurringInvoice.id!,
        entityName: recurringInvoice.templateName,
        description:
            'تحديث الفاتورة المتكررة: ${recurringInvoice.templateName}',
        newValues: recurringInvoiceData,
      );

      LoggingService.info(
        'تم تحديث الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {
          'id': recurringInvoice.id,
          'templateName': recurringInvoice.templateName,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {'id': recurringInvoice.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف فاتورة متكررة
  Future<void> deleteRecurringInvoice(int id) async {
    try {
      final recurringInvoice = await getRecurringInvoiceById(id);
      if (recurringInvoice == null) {
        throw Exception('الفاتورة المتكررة غير موجودة');
      }

      final db = await _databaseHelper.database;

      await db.delete(
        AppConstants.recurringInvoicesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'DELETE',
        entityType: 'recurring_invoices',
        entityId: id,
        entityName: recurringInvoice.templateName,
        description: 'حذف الفاتورة المتكررة: ${recurringInvoice.templateName}',
        oldValues: recurringInvoice.toMap(),
      );

      LoggingService.info(
        'تم حذف الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {'id': id, 'templateName': recurringInvoice.templateName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على فاتورة متكررة بالاسم
  Future<RecurringInvoice?> getRecurringInvoiceByName(
    String templateName,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.recurringInvoicesTable,
        where: 'template_name = ?',
        whereArgs: [templateName],
      );

      if (maps.isNotEmpty) {
        return RecurringInvoice.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الفاتورة المتكررة بالاسم',
        category: 'RecurringInvoiceService',
        data: {'templateName': templateName, 'error': e.toString()},
      );
      return null;
    }
  }

  /// تفعيل/إلغاء تفعيل فاتورة متكررة
  Future<void> toggleRecurringInvoiceStatus(int id, bool isActive) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        AppConstants.recurringInvoicesTable,
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'recurring_invoices',
        entityId: id,
        description: isActive
            ? 'تفعيل الفاتورة المتكررة'
            : 'إلغاء تفعيل الفاتورة المتكررة',
        newValues: {'is_active': isActive},
      );

      LoggingService.info(
        isActive
            ? 'تم تفعيل الفاتورة المتكررة'
            : 'تم إلغاء تفعيل الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {'id': id, 'isActive': isActive},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة الفاتورة المتكررة',
        category: 'RecurringInvoiceService',
        data: {'id': id, 'isActive': isActive, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على الفواتير المتكررة المستحقة للإنشاء
  Future<List<RecurringInvoice>> getDueRecurringInvoices() async {
    try {
      final db = await _databaseHelper.database;
      final today = DateTime.now().toIso8601String().split('T')[0];

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        '''
        SELECT ri.*, 
               c.name as customer_name,
               s.name as supplier_name
        FROM ${AppConstants.recurringInvoicesTable} ri
        LEFT JOIN ${AppConstants.customersTable} c ON ri.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON ri.supplier_id = s.id
        WHERE ri.is_active = 1 
          AND ri.next_generation_date <= ?
          AND (ri.end_date IS NULL OR ri.end_date >= ?)
        ORDER BY ri.next_generation_date ASC
      ''',
        [today, today],
      );

      return List.generate(maps.length, (i) {
        return RecurringInvoice.fromMap(maps[i]);
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الفواتير المتكررة المستحقة',
        category: 'RecurringInvoiceService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// معالجة الفواتير المتكررة المستحقة
  Future<void> _processRecurringInvoices() async {
    try {
      final dueInvoices = await getDueRecurringInvoices();

      for (final recurringInvoice in dueInvoices) {
        await _generateInvoiceFromRecurring(recurringInvoice);
      }

      if (dueInvoices.isNotEmpty) {
        LoggingService.info(
          'تم معالجة الفواتير المتكررة المستحقة',
          category: 'RecurringInvoiceService',
          data: {'count': dueInvoices.length},
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في معالجة الفواتير المتكررة',
        category: 'RecurringInvoiceService',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء فاتورة من نموذج متكرر
  Future<int?> _generateInvoiceFromRecurring(
    RecurringInvoice recurringInvoice,
  ) async {
    try {
      // إنشاء رقم فاتورة جديد
      final template = recurringInvoice.getParsedInvoiceTemplate();
      if (template == null) {
        throw Exception('لا يمكن تحليل نموذج الفاتورة');
      }

      final invoiceNumber = await _invoiceService.generateInvoiceNumber(
        template.type,
      );

      // إنشاء الفاتورة الجديدة
      final newInvoice = recurringInvoice.generateNewInvoice(
        newInvoiceNumber: invoiceNumber,
        invoiceDate: DateTime.now(),
      );

      if (newInvoice == null) {
        throw Exception('فشل في إنشاء الفاتورة من النموذج');
      }

      // إدراج الفاتورة الجديدة
      final invoiceId = await _invoiceService.insertInvoice(newInvoice);

      // تحديث تاريخ الإنشاء التالي للفاتورة المتكررة
      final updatedRecurringInvoice = recurringInvoice
          .updateNextGenerationDate();
      await updateRecurringInvoice(updatedRecurringInvoice);

      // تسجيل العملية
      await AuditService.log(
        action: 'GENERATE',
        entityType: 'recurring_invoices',
        entityId: recurringInvoice.id!,
        entityName: recurringInvoice.templateName,
        description:
            'إنشاء فاتورة من النموذج المتكرر: ${recurringInvoice.templateName}',
        newValues: {
          'generated_invoice_id': invoiceId,
          'generated_invoice_number': invoiceNumber,
          'generation_date': DateTime.now().toIso8601String(),
        },
      );

      LoggingService.info(
        'تم إنشاء فاتورة من النموذج المتكرر',
        category: 'RecurringInvoiceService',
        data: {
          'recurring_invoice_id': recurringInvoice.id,
          'template_name': recurringInvoice.templateName,
          'generated_invoice_id': invoiceId,
          'generated_invoice_number': invoiceNumber,
        },
      );

      return invoiceId;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء فاتورة من النموذج المتكرر',
        category: 'RecurringInvoiceService',
        data: {
          'recurring_invoice_id': recurringInvoice.id,
          'template_name': recurringInvoice.templateName,
          'error': e.toString(),
        },
      );
      return null;
    }
  }

  /// إنشاء فاتورة يدوياً من نموذج متكرر
  Future<int> generateInvoiceManually(int recurringInvoiceId) async {
    try {
      final recurringInvoice = await getRecurringInvoiceById(
        recurringInvoiceId,
      );
      if (recurringInvoice == null) {
        throw Exception('الفاتورة المتكررة غير موجودة');
      }

      if (!recurringInvoice.isActive) {
        throw Exception('الفاتورة المتكررة غير مفعلة');
      }

      final invoiceId = await _generateInvoiceFromRecurring(recurringInvoice);
      if (invoiceId == null) {
        throw Exception('فشل في إنشاء الفاتورة');
      }

      return invoiceId;
    } catch (e) {
      LoggingService.error(
        'خطأ في الإنشاء اليدوي للفاتورة',
        category: 'RecurringInvoiceService',
        data: {
          'recurring_invoice_id': recurringInvoiceId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// معالجة فورية للفواتير المتكررة المستحقة
  Future<List<int>> processRecurringInvoicesNow() async {
    try {
      final dueInvoices = await getDueRecurringInvoices();
      final generatedInvoiceIds = <int>[];

      for (final recurringInvoice in dueInvoices) {
        final invoiceId = await _generateInvoiceFromRecurring(recurringInvoice);
        if (invoiceId != null) {
          generatedInvoiceIds.add(invoiceId);
        }
      }

      LoggingService.info(
        'تم معالجة الفواتير المتكررة فورياً',
        category: 'RecurringInvoiceService',
        data: {
          'total_due': dueInvoices.length,
          'generated_count': generatedInvoiceIds.length,
        },
      );

      return generatedInvoiceIds;
    } catch (e) {
      LoggingService.error(
        'خطأ في المعالجة الفورية للفواتير المتكررة',
        category: 'RecurringInvoiceService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات الفواتير المتكررة
  Future<Map<String, dynamic>> getRecurringInvoiceStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery('''
        SELECT
          COUNT(*) as total_count,
          SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count,
          SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_count,
          SUM(CASE WHEN is_active = 1 AND next_generation_date <= date('now') THEN 1 ELSE 0 END) as due_count
        FROM ${AppConstants.recurringInvoicesTable}
      ''');

      final stats = result.first;

      return {
        'total_count': stats['total_count'] ?? 0,
        'active_count': stats['active_count'] ?? 0,
        'inactive_count': stats['inactive_count'] ?? 0,
        'due_count': stats['due_count'] ?? 0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الفواتير المتكررة',
        category: 'RecurringInvoiceService',
        data: {'error': e.toString()},
      );
      return {
        'total_count': 0,
        'active_count': 0,
        'inactive_count': 0,
        'due_count': 0,
      };
    }
  }

  /// تنظيف الموارد
  void dispose() {
    stopScheduler();
  }
}
