import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smart_ledger/services/encryption_service.dart';

void main() {
  group('EncryptionService Tests', () {
    setUp(() async {
      // تنظيف SharedPreferences قبل كل اختبار
      SharedPreferences.setMockInitialValues({});
    });

    group('Password Strength Tests', () {
      test('should accept strong password', () async {
        const strongPassword = 'StrongPass123!';
        final result = await EncryptionService.setupDatabasePassword(strongPassword);
        expect(result, isTrue);
      });

      test('should reject weak password - too short', () async {
        const weakPassword = 'weak';
        final result = await EncryptionService.setupDatabasePassword(weakPassword);
        expect(result, isFalse);
      });

      test('should reject weak password - no uppercase', () async {
        const weakPassword = 'weakpass123!';
        final result = await EncryptionService.setupDatabasePassword(weakPassword);
        expect(result, isFalse);
      });

      test('should reject weak password - no lowercase', () async {
        const weakPassword = 'WEAKPASS123!';
        final result = await EncryptionService.setupDatabasePassword(weakPassword);
        expect(result, isFalse);
      });

      test('should reject weak password - no numbers', () async {
        const weakPassword = 'WeakPassword!';
        final result = await EncryptionService.setupDatabasePassword(weakPassword);
        expect(result, isFalse);
      });

      test('should reject weak password - no special characters', () async {
        const weakPassword = 'WeakPassword123';
        final result = await EncryptionService.setupDatabasePassword(weakPassword);
        expect(result, isFalse);
      });
    });

    group('Password Setup Tests', () {
      test('should setup password successfully', () async {
        const password = 'TestPassword123!';
        
        // التحقق من عدم وجود إعداد سابق
        expect(await EncryptionService.isEncryptionSetup(), isFalse);
        
        // إعداد كلمة المرور
        final result = await EncryptionService.setupDatabasePassword(password);
        expect(result, isTrue);
        
        // التحقق من وجود الإعداد
        expect(await EncryptionService.isEncryptionSetup(), isTrue);
      });

      test('should not allow duplicate setup', () async {
        const password = 'TestPassword123!';
        
        // إعداد كلمة المرور للمرة الأولى
        await EncryptionService.setupDatabasePassword(password);
        
        // محاولة إعداد مرة أخرى
        final result = await EncryptionService.setupDatabasePassword(password);
        expect(result, isFalse);
      });
    });

    group('Password Verification Tests', () {
      test('should verify correct password', () async {
        const password = 'TestPassword123!';
        
        // إعداد كلمة المرور
        await EncryptionService.setupDatabasePassword(password);
        
        // التحقق من كلمة المرور الصحيحة
        final result = await EncryptionService.verifyDatabasePassword(password);
        expect(result, isTrue);
      });

      test('should reject incorrect password', () async {
        const correctPassword = 'TestPassword123!';
        const incorrectPassword = 'WrongPassword123!';
        
        // إعداد كلمة المرور
        await EncryptionService.setupDatabasePassword(correctPassword);
        
        // التحقق من كلمة مرور خاطئة
        final result = await EncryptionService.verifyDatabasePassword(incorrectPassword);
        expect(result, isFalse);
      });

      test('should fail verification without setup', () async {
        const password = 'TestPassword123!';
        
        // محاولة التحقق بدون إعداد
        final result = await EncryptionService.verifyDatabasePassword(password);
        expect(result, isFalse);
      });
    });

    group('Password Change Tests', () {
      test('should change password successfully', () async {
        const oldPassword = 'OldPassword123!';
        const newPassword = 'NewPassword456@';
        
        // إعداد كلمة المرور القديمة
        await EncryptionService.setupDatabasePassword(oldPassword);
        
        // تغيير كلمة المرور
        final result = await EncryptionService.changeDatabasePassword(
          oldPassword,
          newPassword,
        );
        expect(result, isTrue);
        
        // التحقق من كلمة المرور الجديدة
        expect(await EncryptionService.verifyDatabasePassword(newPassword), isTrue);
        
        // التحقق من أن كلمة المرور القديمة لم تعد تعمل
        expect(await EncryptionService.verifyDatabasePassword(oldPassword), isFalse);
      });

      test('should fail to change with wrong old password', () async {
        const correctOldPassword = 'OldPassword123!';
        const wrongOldPassword = 'WrongOldPassword123!';
        const newPassword = 'NewPassword456@';
        
        // إعداد كلمة المرور
        await EncryptionService.setupDatabasePassword(correctOldPassword);
        
        // محاولة تغيير كلمة المرور بكلمة مرور قديمة خاطئة
        final result = await EncryptionService.changeDatabasePassword(
          wrongOldPassword,
          newPassword,
        );
        expect(result, isFalse);
        
        // التحقق من أن كلمة المرور القديمة ما زالت تعمل
        expect(await EncryptionService.verifyDatabasePassword(correctOldPassword), isTrue);
      });

      test('should fail to change to weak password', () async {
        const oldPassword = 'OldPassword123!';
        const weakNewPassword = 'weak';
        
        // إعداد كلمة المرور القديمة
        await EncryptionService.setupDatabasePassword(oldPassword);
        
        // محاولة تغيير إلى كلمة مرور ضعيفة
        final result = await EncryptionService.changeDatabasePassword(
          oldPassword,
          weakNewPassword,
        );
        expect(result, isFalse);
        
        // التحقق من أن كلمة المرور القديمة ما زالت تعمل
        expect(await EncryptionService.verifyDatabasePassword(oldPassword), isTrue);
      });
    });

    group('Database Password Derivation Tests', () {
      test('should derive database password for correct user password', () async {
        const password = 'TestPassword123!';
        
        // إعداد كلمة المرور
        await EncryptionService.setupDatabasePassword(password);
        
        // الحصول على كلمة مرور قاعدة البيانات
        final dbPassword = await EncryptionService.getDatabasePassword(password);
        expect(dbPassword, isNotNull);
        expect(dbPassword!.isNotEmpty, isTrue);
      });

      test('should not derive database password for incorrect user password', () async {
        const correctPassword = 'TestPassword123!';
        const incorrectPassword = 'WrongPassword123!';
        
        // إعداد كلمة المرور
        await EncryptionService.setupDatabasePassword(correctPassword);
        
        // محاولة الحصول على كلمة مرور قاعدة البيانات بكلمة مرور خاطئة
        final dbPassword = await EncryptionService.getDatabasePassword(incorrectPassword);
        expect(dbPassword, isNull);
      });

      test('should generate different database passwords for different user passwords', () async {
        const password1 = 'TestPassword123!';
        const password2 = 'DifferentPassword456@';
        
        // إعداد كلمة المرور الأولى
        await EncryptionService.setupDatabasePassword(password1);
        final dbPassword1 = await EncryptionService.getDatabasePassword(password1);
        
        // إعادة تعيين وإعداد كلمة المرور الثانية
        await EncryptionService.resetEncryption();
        await EncryptionService.setupDatabasePassword(password2);
        final dbPassword2 = await EncryptionService.getDatabasePassword(password2);
        
        expect(dbPassword1, isNotNull);
        expect(dbPassword2, isNotNull);
        expect(dbPassword1, isNot(equals(dbPassword2)));
      });
    });

    group('Reset Encryption Tests', () {
      test('should reset encryption successfully', () async {
        const password = 'TestPassword123!';
        
        // إعداد كلمة المرور
        await EncryptionService.setupDatabasePassword(password);
        expect(await EncryptionService.isEncryptionSetup(), isTrue);
        
        // إعادة تعيين التشفير
        final result = await EncryptionService.resetEncryption();
        expect(result, isTrue);
        expect(await EncryptionService.isEncryptionSetup(), isFalse);
      });
    });

    group('Password Requirements Tests', () {
      test('should return password requirements string', () {
        final requirements = EncryptionService.getPasswordRequirements();
        expect(requirements, isNotEmpty);
        expect(requirements, contains('8 أحرف'));
        expect(requirements, contains('حرف كبير'));
        expect(requirements, contains('حرف صغير'));
        expect(requirements, contains('رقم'));
        expect(requirements, contains('رمز خاص'));
      });
    });
  });
}
