import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'logging_service.dart';
import 'audit_service.dart';
import '../constants/app_constants.dart';

/// خدمة تصدير ومشاركة الرسوم البيانية
class ChartExportService {
  /// حفظ الرسم البياني كصورة باستخدام GlobalKey
  static Future<String?> saveChartAsImage({
    required GlobalKey repaintBoundaryKey,
    required String reportType,
    String? customFileName,
    ImageFormat format = ImageFormat.png,
  }) async {
    try {
      LoggingService.info(
        'بدء حفظ الرسم البياني كصورة',
        category: 'ChartExport',
        data: {'reportType': reportType, 'format': format.toString()},
      );

      // طلب الأذونات المطلوبة
      if (!await _requestStoragePermission()) {
        LoggingService.warning(
          'لم يتم منح أذونات التخزين',
          category: 'ChartExport',
        );
        return null;
      }

      // التقاط لقطة شاشة للرسم البياني
      final Uint8List? imageBytes = await _captureChartImage(
        repaintBoundaryKey,
      );
      if (imageBytes == null) {
        LoggingService.error(
          'فشل في التقاط صورة الرسم البياني',
          category: 'ChartExport',
        );
        return null;
      }

      // إنشاء اسم الملف
      final fileName = customFileName ?? _generateFileName(reportType, format);

      // حفظ الملف
      final filePath = await _saveImageFile(imageBytes, fileName);

      if (filePath != null) {
        LoggingService.info(
          'تم حفظ الرسم البياني بنجاح',
          category: 'ChartExport',
          data: {'filePath': filePath},
        );

        // تسجيل العملية في سجل المراجعة
        await AuditService.logCreate(
          entityType: AppConstants.auditEntitySystem,
          entityId: 0,
          entityName: 'رسم بياني محفوظ',
          newValues: {
            'reportType': reportType,
            'fileName': fileName,
            'filePath': filePath,
            'format': format.toString(),
          },
          description: 'تم حفظ رسم بياني كصورة',
          category: 'ChartExport',
        );
      }

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ الرسم البياني',
        category: 'ChartExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// مشاركة الرسم البياني
  static Future<bool> shareChart({
    required GlobalKey repaintBoundaryKey,
    required String reportType,
    String? customText,
    ImageFormat format = ImageFormat.png,
  }) async {
    try {
      LoggingService.info(
        'بدء مشاركة الرسم البياني',
        category: 'ChartExport',
        data: {'reportType': reportType},
      );

      // التقاط لقطة شاشة للرسم البياني
      final Uint8List? imageBytes = await _captureChartImage(
        repaintBoundaryKey,
      );
      if (imageBytes == null) {
        LoggingService.error(
          'فشل في التقاط صورة الرسم البياني للمشاركة',
          category: 'ChartExport',
        );
        return false;
      }

      // إنشاء ملف مؤقت
      final tempDir = await getTemporaryDirectory();
      final fileName = _generateFileName(reportType, format);
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(imageBytes);

      // مشاركة الملف
      final shareText = customText ?? 'رسم بياني من Smart Ledger - $reportType';
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: shareText,
        subject: 'رسم بياني - $reportType',
      );

      LoggingService.info(
        'تم مشاركة الرسم البياني بنجاح',
        category: 'ChartExport',
      );

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'رسم بياني مشارك',
        newValues: {
          'reportType': reportType,
          'fileName': fileName,
          'shareText': shareText,
        },
        description: 'تم مشاركة رسم بياني',
        category: 'ChartExport',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في مشاركة الرسم البياني',
        category: 'ChartExport',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// التقاط صورة للرسم البياني باستخدام RepaintBoundary
  static Future<Uint8List?> _captureChartImage(
    GlobalKey repaintBoundaryKey,
  ) async {
    try {
      // الحصول على RenderRepaintBoundary من GlobalKey
      final RenderRepaintBoundary? boundary =
          repaintBoundaryKey.currentContext?.findRenderObject()
              as RenderRepaintBoundary?;

      if (boundary == null) {
        LoggingService.error(
          'لم يتم العثور على RepaintBoundary',
          category: 'ChartExport',
        );
        return null;
      }

      // التقاط الصورة
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      return byteData?.buffer.asUint8List();
    } catch (e) {
      LoggingService.error(
        'خطأ في التقاط صورة الرسم البياني',
        category: 'ChartExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// طلب أذونات التخزين
  static Future<bool> _requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.storage.request();
        if (status.isDenied) {
          final manageStatus = await Permission.manageExternalStorage.request();
          return manageStatus.isGranted;
        }
        return status.isGranted;
      } else if (Platform.isIOS) {
        final status = await Permission.photos.request();
        return status.isGranted;
      }
      return true; // للمنصات الأخرى
    } catch (e) {
      LoggingService.error(
        'خطأ في طلب أذونات التخزين',
        category: 'ChartExport',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// حفظ ملف الصورة
  static Future<String?> _saveImageFile(
    Uint8List imageBytes,
    String fileName,
  ) async {
    try {
      Directory? directory;

      if (Platform.isAndroid) {
        // للأندرويد، حفظ في مجلد التنزيلات
        directory = Directory('/storage/emulated/0/Download/SmartLedger');
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
      } else if (Platform.isIOS) {
        // لـ iOS، حفظ في مجلد المستندات
        directory = await getApplicationDocumentsDirectory();
        directory = Directory('${directory.path}/Charts');
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
      } else {
        // للمنصات الأخرى
        directory = await getApplicationDocumentsDirectory();
        directory = Directory('${directory.path}/Charts');
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
      }

      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(imageBytes);

      return file.path;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ ملف الصورة',
        category: 'ChartExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء اسم ملف فريد
  static String _generateFileName(String reportType, ImageFormat format) {
    final now = DateTime.now();
    final timestamp =
        '${now.year}${now.month.toString().padLeft(2, '0')}'
        '${now.day.toString().padLeft(2, '0')}_'
        '${now.hour.toString().padLeft(2, '0')}'
        '${now.minute.toString().padLeft(2, '0')}'
        '${now.second.toString().padLeft(2, '0')}';

    final extension = format == ImageFormat.png ? 'png' : 'jpg';
    return 'chart_${reportType}_$timestamp.$extension';
  }

  /// الحصول على مجلد الحفظ
  static Future<Directory> getChartsDirectory() async {
    Directory? directory;

    if (Platform.isAndroid) {
      directory = Directory('/storage/emulated/0/Download/SmartLedger/Charts');
    } else {
      final appDir = await getApplicationDocumentsDirectory();
      directory = Directory('${appDir.path}/Charts');
    }

    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    return directory;
  }
}

/// تنسيقات الصور المدعومة
enum ImageFormat { png, jpg }
