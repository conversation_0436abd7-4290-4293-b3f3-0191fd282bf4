import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import 'logging_service.dart';

/// خدمة تحسين قاعدة البيانات
/// توفر تحسينات للاستعلامات والفهارس لتحسين أداء التقارير
class DatabaseOptimizationService {
  static final DatabaseOptimizationService _instance = 
      DatabaseOptimizationService._internal();
  factory DatabaseOptimizationService() => _instance;
  DatabaseOptimizationService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء الفهارس المحسنة للتقارير
  Future<void> createOptimizedIndexes() async {
    try {
      final db = await _databaseHelper.database;
      
      LoggingService.info(
        'بدء إنشاء الفهارس المحسنة',
        category: 'DatabaseOptimization',
      );

      // فهارس للقيود المحاسبية
      await _createJournalEntryIndexes(db);
      
      // فهارس للحسابات
      await _createAccountIndexes(db);
      
      // فهارس للفواتير
      await _createInvoiceIndexes(db);
      
      // فهارس للعملاء والموردين
      await _createCustomerSupplierIndexes(db);
      
      // فهارس للمخزون
      await _createInventoryIndexes(db);

      LoggingService.info(
        'تم إنشاء جميع الفهارس المحسنة بنجاح',
        category: 'DatabaseOptimization',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الفهارس المحسنة',
        category: 'DatabaseOptimization',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء فهارس القيود المحاسبية
  Future<void> _createJournalEntryIndexes(Database db) async {
    final indexes = [
      // فهرس للتاريخ والحالة (للتقارير الزمنية)
      '''
      CREATE INDEX IF NOT EXISTS idx_journal_entries_date_posted 
      ON ${AppConstants.journalEntriesTable}(entry_date, is_posted)
      ''',
      
      // فهرس لرقم القيد
      '''
      CREATE INDEX IF NOT EXISTS idx_journal_entries_number 
      ON ${AppConstants.journalEntriesTable}(entry_number)
      ''',
      
      // فهرس تفاصيل القيود للحساب والمبلغ
      '''
      CREATE INDEX IF NOT EXISTS idx_journal_entry_details_account_amount 
      ON ${AppConstants.journalEntryDetailsTable}(account_id, debit_amount, credit_amount)
      ''',
      
      // فهرس مركب للحساب والقيد
      '''
      CREATE INDEX IF NOT EXISTS idx_journal_entry_details_account_entry 
      ON ${AppConstants.journalEntryDetailsTable}(account_id, journal_entry_id)
      ''',
    ];

    for (final index in indexes) {
      await db.execute(index);
    }
  }

  /// إنشاء فهارس الحسابات
  Future<void> _createAccountIndexes(Database db) async {
    final indexes = [
      // فهرس لنوع الحساب والحالة
      '''
      CREATE INDEX IF NOT EXISTS idx_accounts_type_active 
      ON ${AppConstants.accountsTable}(account_type, is_active)
      ''',
      
      // فهرس لكود الحساب
      '''
      CREATE INDEX IF NOT EXISTS idx_accounts_code 
      ON ${AppConstants.accountsTable}(code)
      ''',
      
      // فهرس للحساب الأب
      '''
      CREATE INDEX IF NOT EXISTS idx_accounts_parent 
      ON ${AppConstants.accountsTable}(parent_account_id)
      ''',
      
      // فهرس للبحث النصي
      '''
      CREATE INDEX IF NOT EXISTS idx_accounts_name 
      ON ${AppConstants.accountsTable}(name)
      ''',
    ];

    for (final index in indexes) {
      await db.execute(index);
    }
  }

  /// إنشاء فهارس الفواتير
  Future<void> _createInvoiceIndexes(Database db) async {
    final indexes = [
      // فهرس للتاريخ ونوع الفاتورة
      '''
      CREATE INDEX IF NOT EXISTS idx_invoices_date_type 
      ON ${AppConstants.invoicesTable}(invoice_date, invoice_type)
      ''',
      
      // فهرس للعميل/المورد
      '''
      CREATE INDEX IF NOT EXISTS idx_invoices_customer 
      ON ${AppConstants.invoicesTable}(customer_id)
      ''',
      
      // فهرس لرقم الفاتورة
      '''
      CREATE INDEX IF NOT EXISTS idx_invoices_number 
      ON ${AppConstants.invoicesTable}(invoice_number)
      ''',
      
      // فهرس للحالة والتاريخ
      '''
      CREATE INDEX IF NOT EXISTS idx_invoices_status_date 
      ON ${AppConstants.invoicesTable}(status, invoice_date)
      ''',
    ];

    for (final index in indexes) {
      await db.execute(index);
    }
  }

  /// إنشاء فهارس العملاء والموردين
  Future<void> _createCustomerSupplierIndexes(Database db) async {
    final indexes = [
      // فهرس العملاء للحالة والرصيد
      '''
      CREATE INDEX IF NOT EXISTS idx_customers_active_balance 
      ON ${AppConstants.customersTable}(is_active, balance)
      ''',
      
      // فهرس الموردين للحالة والرصيد
      '''
      CREATE INDEX IF NOT EXISTS idx_suppliers_active_balance 
      ON ${AppConstants.suppliersTable}(is_active, balance)
      ''',
      
      // فهرس البحث للعملاء
      '''
      CREATE INDEX IF NOT EXISTS idx_customers_name_code 
      ON ${AppConstants.customersTable}(name, code)
      ''',
      
      // فهرس البحث للموردين
      '''
      CREATE INDEX IF NOT EXISTS idx_suppliers_name_code 
      ON ${AppConstants.suppliersTable}(name, code)
      ''',
    ];

    for (final index in indexes) {
      await db.execute(index);
    }
  }

  /// إنشاء فهارس المخزون
  Future<void> _createInventoryIndexes(Database db) async {
    final indexes = [
      // فهرس للحالة والكمية
      '''
      CREATE INDEX IF NOT EXISTS idx_items_active_quantity 
      ON ${AppConstants.itemsTable}(is_active, quantity)
      ''',
      
      // فهرس للكود والاسم
      '''
      CREATE INDEX IF NOT EXISTS idx_items_code_name 
      ON ${AppConstants.itemsTable}(code, name)
      ''',
      
      // فهرس للفئة
      '''
      CREATE INDEX IF NOT EXISTS idx_items_category 
      ON ${AppConstants.itemsTable}(category_id)
      ''',
      
      // فهرس للحد الأدنى للكمية
      '''
      CREATE INDEX IF NOT EXISTS idx_items_min_quantity 
      ON ${AppConstants.itemsTable}(min_quantity, quantity)
      ''',
    ];

    for (final index in indexes) {
      await db.execute(index);
    }
  }

  /// تحليل الاستعلامات وتحسينها
  Future<QueryOptimizationResult> analyzeQuery(String query) async {
    try {
      final db = await _databaseHelper.database;
      final startTime = DateTime.now();
      
      // تنفيذ EXPLAIN QUERY PLAN
      final explainResult = await db.rawQuery('EXPLAIN QUERY PLAN $query');
      
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);

      final result = QueryOptimizationResult(
        originalQuery: query,
        executionTimeMs: executionTime.inMilliseconds,
        queryPlan: explainResult,
        suggestions: _generateOptimizationSuggestions(explainResult),
      );

      LoggingService.info(
        'تم تحليل الاستعلام',
        category: 'DatabaseOptimization',
        data: {
          'executionTimeMs': executionTime.inMilliseconds,
          'planSteps': explainResult.length,
        },
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحليل الاستعلام',
        category: 'DatabaseOptimization',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// توليد اقتراحات التحسين
  List<String> _generateOptimizationSuggestions(
    List<Map<String, dynamic>> queryPlan,
  ) {
    final suggestions = <String>[];
    
    for (final step in queryPlan) {
      final detail = step['detail']?.toString().toLowerCase() ?? '';
      
      // البحث عن عمليات المسح الكامل للجدول
      if (detail.contains('scan table')) {
        suggestions.add('يُنصح بإضافة فهرس للجدول المذكور لتجنب المسح الكامل');
      }
      
      // البحث عن عمليات الترتيب
      if (detail.contains('use temp b-tree for order by')) {
        suggestions.add('يُنصح بإضافة فهرس للعمود المستخدم في ORDER BY');
      }
      
      // البحث عن عمليات التجميع
      if (detail.contains('use temp b-tree for group by')) {
        suggestions.add('يُنصح بإضافة فهرس للعمود المستخدم في GROUP BY');
      }
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('الاستعلام محسن بشكل جيد');
    }
    
    return suggestions;
  }

  /// تحديث إحصائيات قاعدة البيانات
  Future<void> updateStatistics() async {
    try {
      final db = await _databaseHelper.database;
      
      LoggingService.info(
        'بدء تحديث إحصائيات قاعدة البيانات',
        category: 'DatabaseOptimization',
      );

      // تحديث إحصائيات SQLite
      await db.execute('ANALYZE');

      LoggingService.info(
        'تم تحديث إحصائيات قاعدة البيانات بنجاح',
        category: 'DatabaseOptimization',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث إحصائيات قاعدة البيانات',
        category: 'DatabaseOptimization',
        data: {'error': e.toString()},
      );
    }
  }

  /// تنظيف قاعدة البيانات
  Future<void> vacuum() async {
    try {
      final db = await _databaseHelper.database;
      
      LoggingService.info(
        'بدء تنظيف قاعدة البيانات',
        category: 'DatabaseOptimization',
      );

      await db.execute('VACUUM');

      LoggingService.info(
        'تم تنظيف قاعدة البيانات بنجاح',
        category: 'DatabaseOptimization',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف قاعدة البيانات',
        category: 'DatabaseOptimization',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على معلومات الفهارس
  Future<List<IndexInfo>> getIndexInfo() async {
    try {
      final db = await _databaseHelper.database;
      
      final result = await db.rawQuery('''
        SELECT 
          name,
          tbl_name,
          sql
        FROM sqlite_master 
        WHERE type = 'index' 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY tbl_name, name
      ''');

      return result.map((row) => IndexInfo.fromMap(row)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على معلومات الفهارس',
        category: 'DatabaseOptimization',
        data: {'error': e.toString()},
      );
      return [];
    }
  }
}

/// نتيجة تحليل الاستعلام
class QueryOptimizationResult {
  final String originalQuery;
  final int executionTimeMs;
  final List<Map<String, dynamic>> queryPlan;
  final List<String> suggestions;

  const QueryOptimizationResult({
    required this.originalQuery,
    required this.executionTimeMs,
    required this.queryPlan,
    required this.suggestions,
  });

  Map<String, dynamic> toMap() {
    return {
      'originalQuery': originalQuery,
      'executionTimeMs': executionTimeMs,
      'queryPlan': queryPlan,
      'suggestions': suggestions,
    };
  }
}

/// معلومات الفهرس
class IndexInfo {
  final String name;
  final String tableName;
  final String? sql;

  const IndexInfo({
    required this.name,
    required this.tableName,
    this.sql,
  });

  factory IndexInfo.fromMap(Map<String, dynamic> map) {
    return IndexInfo(
      name: map['name'] ?? '',
      tableName: map['tbl_name'] ?? '',
      sql: map['sql'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'tableName': tableName,
      'sql': sql,
    };
  }
}
