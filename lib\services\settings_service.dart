import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';

class SettingsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على قيمة إعداد معين
  Future<String?> getSetting(String key) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.settingsTable,
      where: 'key = ?',
      whereArgs: [key],
    );

    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return null;
  }

  // تحديث أو إدراج إعداد
  Future<void> setSetting(String key, String value) async {
    final db = await _databaseHelper.database;
    final now = DateTime.now().toIso8601String();

    await db.insert(AppConstants.settingsTable, {
      'key': key,
      'value': value,
      'updated_at': now,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // الحصول على جميع الإعدادات
  Future<Map<String, String>> getAllSettings() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.settingsTable,
    );

    Map<String, String> settings = {};
    for (var map in maps) {
      settings[map['key']] = map['value'];
    }
    return settings;
  }

  // إعدادات الشركة
  Future<String> getCompanyName() async {
    return await getSetting('company_name') ?? 'شركتي';
  }

  Future<void> setCompanyName(String name) async {
    await setSetting('company_name', name);
  }

  Future<String> getCompanyAddress() async {
    return await getSetting('company_address') ?? '';
  }

  Future<void> setCompanyAddress(String address) async {
    await setSetting('company_address', address);
  }

  Future<String> getCompanyPhone() async {
    return await getSetting('company_phone') ?? '';
  }

  Future<void> setCompanyPhone(String phone) async {
    await setSetting('company_phone', phone);
  }

  Future<String> getCompanyEmail() async {
    return await getSetting('company_email') ?? '';
  }

  Future<void> setCompanyEmail(String email) async {
    await setSetting('company_email', email);
  }

  Future<String> getCompanyTaxNumber() async {
    return await getSetting('company_tax_number') ?? '';
  }

  Future<void> setCompanyTaxNumber(String taxNumber) async {
    await setSetting('company_tax_number', taxNumber);
  }

  // إعدادات العملة
  Future<String> getDefaultCurrency() async {
    return await getSetting('default_currency') ?? AppConstants.defaultCurrency;
  }

  Future<void> setDefaultCurrency(String currency) async {
    await setSetting('default_currency', currency);
  }

  // إعدادات اللغة
  Future<String> getLanguage() async {
    return await getSetting('language') ?? AppConstants.defaultLanguage;
  }

  Future<void> setLanguage(String language) async {
    await setSetting('language', language);
  }

  // إعدادات النسخ الاحتياطي
  Future<bool> getAutoBackup() async {
    final value = await getSetting('auto_backup');
    return value == 'true';
  }

  Future<void> setAutoBackup(bool enabled) async {
    await setSetting('auto_backup', enabled.toString());
  }

  Future<int> getBackupInterval() async {
    final value = await getSetting('backup_interval');
    return int.tryParse(value ?? '7') ?? 7; // افتراضي 7 أيام
  }

  Future<void> setBackupInterval(int days) async {
    await setSetting('backup_interval', days.toString());
  }

  // إعدادات الأمان
  Future<bool> getRequirePassword() async {
    final value = await getSetting('require_password');
    return value == 'true';
  }

  Future<void> setRequirePassword(bool required) async {
    await setSetting('require_password', required.toString());
  }

  Future<int> getSessionTimeout() async {
    final value = await getSetting('session_timeout');
    return int.tryParse(value ?? '30') ?? 30; // افتراضي 30 دقيقة
  }

  Future<void> setSessionTimeout(int minutes) async {
    await setSetting('session_timeout', minutes.toString());
  }

  // إعدادات التقارير
  Future<String> getDefaultReportFormat() async {
    return await getSetting('default_report_format') ?? 'PDF';
  }

  Future<void> setDefaultReportFormat(String format) async {
    await setSetting('default_report_format', format);
  }

  Future<bool> getShowReportHeader() async {
    final value = await getSetting('show_report_header');
    return value != 'false'; // افتراضي true
  }

  Future<void> setShowReportHeader(bool show) async {
    await setSetting('show_report_header', show.toString());
  }

  // إعدادات الفواتير
  Future<String> getInvoiceNumberPrefix() async {
    return await getSetting('invoice_number_prefix') ?? 'INV';
  }

  Future<void> setInvoiceNumberPrefix(String prefix) async {
    await setSetting('invoice_number_prefix', prefix);
  }

  Future<bool> getAutoGenerateInvoiceNumber() async {
    final value = await getSetting('auto_generate_invoice_number');
    return value != 'false'; // افتراضي true
  }

  Future<void> setAutoGenerateInvoiceNumber(bool auto) async {
    await setSetting('auto_generate_invoice_number', auto.toString());
  }

  // إعدادات الضرائب
  Future<double> getDefaultTaxRate() async {
    final value = await getSetting('default_tax_rate');
    return double.tryParse(value ?? '0') ?? 0.0;
  }

  Future<void> setDefaultTaxRate(double rate) async {
    await setSetting('default_tax_rate', rate.toString());
  }

  Future<bool> getIncludeTaxInPrice() async {
    final value = await getSetting('include_tax_in_price');
    return value == 'true';
  }

  Future<void> setIncludeTaxInPrice(bool include) async {
    await setSetting('include_tax_in_price', include.toString());
  }

  // إعدادات المخزون
  Future<bool> getTrackInventory() async {
    final value = await getSetting('track_inventory');
    return value != 'false'; // افتراضي true
  }

  Future<void> setTrackInventory(bool track) async {
    await setSetting('track_inventory', track.toString());
  }

  Future<String> getInventoryMethod() async {
    return await getSetting('inventory_method') ?? 'FIFO';
  }

  Future<void> setInventoryMethod(String method) async {
    await setSetting('inventory_method', method);
  }

  Future<bool> getAllowNegativeInventory() async {
    final value = await getSetting('allow_negative_inventory');
    return value == 'true';
  }

  Future<void> setAllowNegativeInventory(bool allow) async {
    await setSetting('allow_negative_inventory', allow.toString());
  }

  // حذف إعداد
  Future<void> deleteSetting(String key) async {
    final db = await _databaseHelper.database;
    await db.delete(
      AppConstants.settingsTable,
      where: 'key = ?',
      whereArgs: [key],
    );
  }

  // إعادة تعيين الإعدادات للقيم الافتراضية
  Future<void> resetToDefaults() async {
    final db = await _databaseHelper.database;
    await db.delete(AppConstants.settingsTable);

    // إعادة إدراج الإعدادات الأساسية
    final now = DateTime.now().toIso8601String();

    await db.insert(AppConstants.settingsTable, {
      'key': 'default_currency',
      'value': AppConstants.defaultCurrency,
      'updated_at': now,
    });

    await db.insert(AppConstants.settingsTable, {
      'key': 'company_name',
      'value': 'شركتي',
      'updated_at': now,
    });

    await db.insert(AppConstants.settingsTable, {
      'key': 'language',
      'value': AppConstants.defaultLanguage,
      'updated_at': now,
    });
  }
}
