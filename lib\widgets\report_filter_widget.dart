import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../models/interactive_report_models.dart';
import '../models/account.dart';
import '../services/account_service.dart';

/// مكون فلترة التقارير المتقدم
class ReportFilterWidget extends StatefulWidget {
  final ReportFilters initialFilters;
  final Function(ReportFilters) onFiltersChanged;
  final String reportType;

  const ReportFilterWidget({
    super.key,
    required this.initialFilters,
    required this.onFiltersChanged,
    required this.reportType,
  });

  @override
  State<ReportFilterWidget> createState() => _ReportFilterWidgetState();
}

class _ReportFilterWidgetState extends State<ReportFilterWidget>
    with TickerProviderStateMixin {
  late ReportFilters _currentFilters;
  late TabController _tabController;

  final AccountService _accountService = AccountService();
  List<Account> _accounts = [];
  bool _isLoadingAccounts = false;

  // Controllers للحقول
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _minAmountController = TextEditingController();
  final TextEditingController _maxAmountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _currentFilters = widget.initialFilters;
    _tabController = TabController(length: 4, vsync: this);
    _initializeControllers();
    _loadAccounts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _searchController.text = _currentFilters.searchText ?? '';
    _minAmountController.text = _currentFilters.minAmount?.toString() ?? '';
    _maxAmountController.text = _currentFilters.maxAmount?.toString() ?? '';
  }

  Future<void> _loadAccounts() async {
    setState(() => _isLoadingAccounts = true);
    try {
      _accounts = await _accountService.getAllAccounts();
    } catch (e) {
      // Handle error
    } finally {
      setState(() => _isLoadingAccounts = false);
    }
  }

  void _updateFilters() {
    widget.onFiltersChanged(_currentFilters);
  }

  void _resetFilters() {
    setState(() {
      _currentFilters = ReportFilters.empty();
      _initializeControllers();
    });
    _updateFilters();
  }

  List<Account> _getFilteredAccounts() {
    if (_currentFilters.searchText == null ||
        _currentFilters.searchText!.isEmpty) {
      return _accounts;
    }

    final searchText = _currentFilters.searchText!.toLowerCase();
    return _accounts.where((account) {
      return account.name.toLowerCase().contains(searchText) ||
          account.code.toLowerCase().contains(searchText);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.filter_list, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'فلاتر التقرير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _resetFilters,
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('إعادة تعيين'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.secondary,
                  ),
                ),
              ],
            ),
          ),

          // Tabs
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.primary,
            tabs: const [
              Tab(text: 'التاريخ'),
              Tab(text: 'الحسابات'),
              Tab(text: 'المبالغ'),
              Tab(text: 'متقدم'),
            ],
          ),

          // Tab Content
          SizedBox(
            height: 300,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDateFilters(),
                _buildAccountFilters(),
                _buildAmountFilters(),
                _buildAdvancedFilters(),
              ],
            ),
          ),

          // Apply Button
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _updateFilters,
                icon: const Icon(Icons.check),
                label: const Text('تطبيق الفلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilters() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فترة التقرير',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Quick Date Ranges
          Wrap(
            spacing: 8,
            children: [
              _buildQuickDateChip('اليوم', () => _setDateRange(0)),
              _buildQuickDateChip('هذا الأسبوع', () => _setDateRange(7)),
              _buildQuickDateChip('هذا الشهر', () => _setDateRange(30)),
              _buildQuickDateChip('هذا العام', () => _setDateRange(365)),
            ],
          ),

          const SizedBox(height: 16),

          // Custom Date Range
          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  'من تاريخ',
                  _currentFilters.fromDate,
                  (date) => setState(() {
                    _currentFilters = _currentFilters.copyWith(fromDate: date);
                  }),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateField(
                  'إلى تاريخ',
                  _currentFilters.toDate,
                  (date) => setState(() {
                    _currentFilters = _currentFilters.copyWith(toDate: date);
                  }),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountFilters() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فلترة الحسابات',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Account Types
          const Text(
            'أنواع الحسابات:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildAccountTypeChip('الأصول', 'asset'),
              _buildAccountTypeChip('الخصوم', 'liability'),
              _buildAccountTypeChip('الإيرادات', 'revenue'),
              _buildAccountTypeChip('المصروفات', 'expense'),
            ],
          ),

          const SizedBox(height: 16),

          // Search Accounts
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: 'البحث في الحسابات',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _currentFilters = _currentFilters.copyWith(
                  searchText: value.isEmpty ? null : value,
                );
              });
            },
          ),

          const SizedBox(height: 16),

          // Specific Accounts Selection
          if (_isLoadingAccounts)
            const Center(child: CircularProgressIndicator())
          else if (_accounts.isNotEmpty)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حسابات محددة:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _getFilteredAccounts().length,
                      itemBuilder: (context, index) {
                        final account = _getFilteredAccounts()[index];
                        final isSelected = _currentFilters.accountIds.contains(
                          account.id,
                        );
                        return CheckboxListTile(
                          title: Text(account.name),
                          subtitle: Text(account.code),
                          value: isSelected,
                          dense: true,
                          onChanged: (selected) {
                            setState(() {
                              final accountIds = List<int>.from(
                                _currentFilters.accountIds,
                              );
                              if (selected == true) {
                                accountIds.add(account.id!);
                              } else {
                                accountIds.remove(account.id);
                              }
                              _currentFilters = _currentFilters.copyWith(
                                accountIds: accountIds,
                              );
                            });
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAmountFilters() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'نطاق المبالغ',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _minAmountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الحد الأدنى',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    final amount = double.tryParse(value);
                    setState(() {
                      _currentFilters = _currentFilters.copyWith(
                        minAmount: amount,
                      );
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _maxAmountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الحد الأقصى',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    final amount = double.tryParse(value);
                    setState(() {
                      _currentFilters = _currentFilters.copyWith(
                        maxAmount: amount,
                      );
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'خيارات متقدمة',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          CheckboxListTile(
            title: const Text('تضمين الحسابات غير النشطة'),
            value: _currentFilters.includeInactive,
            onChanged: (value) {
              setState(() {
                _currentFilters = _currentFilters.copyWith(
                  includeInactive: value ?? false,
                );
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickDateChip(String label, VoidCallback onTap) {
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
      labelStyle: TextStyle(color: AppColors.primary),
    );
  }

  Widget _buildAccountTypeChip(String label, String type) {
    final isSelected = _currentFilters.accountTypes.contains(type);
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          final types = List<String>.from(_currentFilters.accountTypes);
          if (selected) {
            types.add(type);
          } else {
            types.remove(type);
          }
          _currentFilters = _currentFilters.copyWith(accountTypes: types);
        });
      },
      selectedColor: AppColors.primary.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? value,
    Function(DateTime?) onChanged,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (date != null) {
          onChanged(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          value != null
              ? '${value.day}/${value.month}/${value.year}'
              : 'اختر التاريخ',
          style: TextStyle(color: value != null ? Colors.black : Colors.grey),
        ),
      ),
    );
  }

  void _setDateRange(int days) {
    final now = DateTime.now();
    final fromDate = now.subtract(Duration(days: days));
    setState(() {
      _currentFilters = _currentFilters.copyWith(
        fromDate: fromDate,
        toDate: now,
      );
    });
  }
}
