/// استثناء قواعد العمل
/// يُستخدم عند انتهاك قواعد العمل في التطبيق
class BusinessRuleException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  BusinessRuleException(
    this.message, {
    this.code,
    this.details,
  });

  /// استثناء لوجود بيانات مرتبطة
  BusinessRuleException.hasRelatedData(String entityName, String relatedData)
      : message = 'لا يمكن حذف $entityName لوجود $relatedData مرتبطة به',
        code = 'HAS_RELATED_DATA',
        details = {
          'entityName': entityName,
          'relatedData': relatedData,
        };

  /// استثناء لحالة غير صحيحة
  BusinessRuleException.invalidState(String currentState, String requiredState)
      : message = 'الحالة الحالية ($currentState) غير صحيحة. الحالة المطلوبة: $requiredState',
        code = 'INVALID_STATE',
        details = {
          'currentState': currentState,
          'requiredState': requiredState,
        };

  /// استثناء لعملية غير مسموحة
  BusinessRuleException.operationNotAllowed(String operation, String reason)
      : message = 'العملية "$operation" غير مسموحة: $reason',
        code = 'OPERATION_NOT_ALLOWED',
        details = {
          'operation': operation,
          'reason': reason,
        };

  /// استثناء لتجاوز الحد المسموح
  BusinessRuleException.limitExceeded(String limitType, dynamic currentValue, dynamic maxValue)
      : message = 'تم تجاوز الحد المسموح لـ $limitType. القيمة الحالية: $currentValue، الحد الأقصى: $maxValue',
        code = 'LIMIT_EXCEEDED',
        details = {
          'limitType': limitType,
          'currentValue': currentValue,
          'maxValue': maxValue,
        };

  @override
  String toString() {
    if (code != null) {
      return 'BusinessRuleException [$code]: $message';
    }
    return 'BusinessRuleException: $message';
  }
}
