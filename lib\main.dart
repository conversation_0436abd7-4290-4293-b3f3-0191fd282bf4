import 'package:flutter/material.dart';
import 'constants/app_theme.dart';
import 'constants/app_constants.dart';
import 'screens/login_screen.dart';
import 'widgets/keyboard_shortcuts_wrapper.dart';

void main() {
  runApp(const SmartLedgerApp());
}

class SmartLedgerApp extends StatelessWidget {
  const SmartLedgerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      home: const KeyboardShortcutsWrapper(
        screenType: 'global',
        child: LoginScreen(),
      ),
      debugShowCheckedModeBanner: false,
      locale: const Locale('ar', 'SY'),
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );
  }
}
