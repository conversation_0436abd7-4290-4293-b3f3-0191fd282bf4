class Item {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final String unit;
  final double costPrice;
  final double sellingPrice;
  final double quantity;
  final double minQuantity;
  final int currencyId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Item({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.unit,
    this.costPrice = 0.0,
    this.sellingPrice = 0.0,
    this.quantity = 0.0,
    this.minQuantity = 0.0,
    required this.currencyId,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'unit': unit,
      'cost_price': costPrice,
      'selling_price': sellingPrice,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'currency_id': currencyId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Item.fromMap(Map<String, dynamic> map) {
    return Item(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      unit: map['unit'] ?? '',
      costPrice: map['cost_price']?.toDouble() ?? 0.0,
      sellingPrice: map['selling_price']?.toDouble() ?? 0.0,
      quantity: map['quantity']?.toDouble() ?? 0.0,
      minQuantity: map['min_quantity']?.toDouble() ?? 0.0,
      currencyId: map['currency_id']?.toInt() ?? 1,
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Item copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    String? unit,
    double? costPrice,
    double? sellingPrice,
    double? quantity,
    double? minQuantity,
    int? currencyId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Item(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      unit: unit ?? this.unit,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      currencyId: currencyId ?? this.currencyId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Item(id: $id, code: $code, name: $name, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Item && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  String get displayCostPrice {
    return costPrice.toStringAsFixed(2);
  }

  String get displaySellingPrice {
    return sellingPrice.toStringAsFixed(2);
  }

  String get displayQuantity {
    return quantity.toStringAsFixed(2);
  }

  bool get isLowStock {
    return quantity <= minQuantity && minQuantity > 0;
  }

  bool get isOutOfStock {
    return quantity <= 0;
  }

  double get profitMargin {
    if (costPrice == 0) return 0.0;
    return ((sellingPrice - costPrice) / costPrice) * 100;
  }

  String get stockStatus {
    if (isOutOfStock) {
      return 'نفد المخزون';
    } else if (isLowStock) {
      return 'مخزون منخفض';
    } else {
      return 'متوفر';
    }
  }

  String get statusArabic {
    return isActive ? 'نشط' : 'غير نشط';
  }
}
