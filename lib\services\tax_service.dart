/// خدمة الضرائب المتكاملة
/// تقوم بإدارة أنواع الضرائب المختلفة وحساب الضرائب تلقائياً
/// وربطها بالحسابات المحاسبية
library;

import '../database/database_helper.dart';
import '../models/invoice.dart';
import '../services/settings_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';
import '../exceptions/business_rule_exception.dart' as business_exceptions;
import '../exceptions/validation_exception.dart' as validation_exceptions;

/// نموذج الضريبة
class Tax {
  final int? id;
  final String code;
  final String name;
  final String type; // 'percentage', 'fixed'
  final double rate; // النسبة أو المبلغ الثابت
  final bool isActive;
  final bool isDefault;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  Tax({
    this.id,
    required this.code,
    required this.name,
    required this.type,
    required this.rate,
    this.isActive = true,
    this.isDefault = false,
    this.description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'type': type,
      'rate': rate,
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Tax.fromMap(Map<String, dynamic> map) {
    return Tax(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      type: map['type'] ?? 'percentage',
      rate: map['rate']?.toDouble() ?? 0.0,
      isActive: (map['is_active'] ?? 1) == 1,
      isDefault: (map['is_default'] ?? 0) == 1,
      description: map['description'],
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Tax copyWith({
    int? id,
    String? code,
    String? name,
    String? type,
    double? rate,
    bool? isActive,
    bool? isDefault,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tax(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      type: type ?? this.type,
      rate: rate ?? this.rate,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب مبلغ الضريبة
  double calculateTaxAmount(double baseAmount) {
    if (!isActive) return 0.0;

    switch (type) {
      case 'percentage':
        return baseAmount * (rate / 100);
      case 'fixed':
        return rate;
      default:
        return 0.0;
    }
  }

  /// النوع بالعربية
  String get typeArabic {
    switch (type) {
      case 'percentage':
        return 'نسبة مئوية';
      case 'fixed':
        return 'مبلغ ثابت';
      default:
        return 'غير محدد';
    }
  }

  /// عرض المعدل
  String get displayRate {
    switch (type) {
      case 'percentage':
        return '${rate.toStringAsFixed(2)}%';
      case 'fixed':
        return '${rate.toStringAsFixed(2)} ل.س';
      default:
        return rate.toStringAsFixed(2);
    }
  }
}

class TaxService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final SettingsService _settingsService = SettingsService();

  /// إنشاء جدول الضرائب إذا لم يكن موجوداً
  Future<void> _createTaxesTableIfNotExists() async {
    final db = await _databaseHelper.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.taxesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        type TEXT NOT NULL DEFAULT 'percentage',
        rate REAL NOT NULL DEFAULT 0.0,
        is_active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // إنشاء فهرس للكود
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_taxes_code 
      ON ${AppConstants.taxesTable}(code)
    ''');

    // إنشاء فهرس للحالة النشطة
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_taxes_active 
      ON ${AppConstants.taxesTable}(is_active)
    ''');
  }

  /// الحصول على جميع الضرائب
  Future<List<Tax>> getAllTaxes() async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.taxesTable,
      orderBy: 'is_default DESC, name ASC',
    );

    return result.map((map) => Tax.fromMap(map)).toList();
  }

  /// الحصول على الضرائب النشطة
  Future<List<Tax>> getActiveTaxes() async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.taxesTable,
      where: 'is_active = 1',
      orderBy: 'is_default DESC, name ASC',
    );

    return result.map((map) => Tax.fromMap(map)).toList();
  }

  /// الحصول على ضريبة بالمعرف
  Future<Tax?> getTaxById(int id) async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.taxesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (result.isNotEmpty) {
      return Tax.fromMap(result.first);
    }
    return null;
  }

  /// الحصول على ضريبة بالكود
  Future<Tax?> getTaxByCode(String code) async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.taxesTable,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (result.isNotEmpty) {
      return Tax.fromMap(result.first);
    }
    return null;
  }

  /// الحصول على الضريبة الافتراضية
  Future<Tax?> getDefaultTax() async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.taxesTable,
      where: 'is_default = 1 AND is_active = 1',
      limit: 1,
    );

    if (result.isNotEmpty) {
      return Tax.fromMap(result.first);
    }

    // إذا لم توجد ضريبة افتراضية، إنشاء واحدة من الإعدادات
    final defaultRate = await _settingsService.getDefaultTaxRate();
    if (defaultRate > 0) {
      return Tax(
        code: 'DEFAULT',
        name: 'ضريبة القيمة المضافة',
        type: 'percentage',
        rate: defaultRate,
        isDefault: true,
      );
    }

    return null;
  }

  /// إضافة ضريبة جديدة
  Future<int> insertTax(Tax tax) async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات
    _validateTax(tax);

    // التحقق من عدم تكرار الكود
    final existingTax = await getTaxByCode(tax.code);
    if (existingTax != null) {
      throw validation_exceptions.ValidationException(
        'كود الضريبة موجود مسبقاً: ${tax.code}',
      );
    }

    return await db.transaction((txn) async {
      // إذا كانت الضريبة افتراضية، إلغاء الافتراضية من الضرائب الأخرى
      if (tax.isDefault) {
        await txn.update(AppConstants.taxesTable, {
          'is_default': 0,
          'updated_at': DateTime.now().toIso8601String(),
        }, where: 'is_default = 1');
      }

      final taxData = tax.toMap();
      taxData.remove('id');
      final taxId = await txn.insert(AppConstants.taxesTable, taxData);

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: 'tax',
        entityId: taxId,
        entityName: tax.name,
        newValues: tax.toMap(),
        description: 'إنشاء ضريبة جديدة: ${tax.name} (${tax.code})',
        category: 'Tax Management',
      );

      LoggingService.info(
        'تم إنشاء ضريبة جديدة',
        category: 'TaxService',
        data: {
          'taxId': taxId,
          'code': tax.code,
          'name': tax.name,
          'rate': tax.rate,
        },
      );

      return taxId;
    });
  }

  /// تحديث ضريبة
  Future<int> updateTax(Tax tax) async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات
    _validateTax(tax);

    if (tax.id == null) {
      throw validation_exceptions.ValidationException(
        'معرف الضريبة مطلوب للتحديث',
      );
    }

    // التحقق من عدم تكرار الكود مع ضرائب أخرى
    final existingTax = await getTaxByCode(tax.code);
    if (existingTax != null && existingTax.id != tax.id) {
      throw validation_exceptions.ValidationException(
        'كود الضريبة موجود مسبقاً: ${tax.code}',
      );
    }

    // الحصول على البيانات القديمة للمراجعة
    final oldTax = await getTaxById(tax.id!);

    return await db.transaction((txn) async {
      // إذا كانت الضريبة افتراضية، إلغاء الافتراضية من الضرائب الأخرى
      if (tax.isDefault) {
        await txn.update(
          AppConstants.taxesTable,
          {'is_default': 0, 'updated_at': DateTime.now().toIso8601String()},
          where: 'is_default = 1 AND id != ?',
          whereArgs: [tax.id],
        );
      }

      final taxData = tax.copyWith(updatedAt: DateTime.now()).toMap();
      final result = await txn.update(
        AppConstants.taxesTable,
        taxData,
        where: 'id = ?',
        whereArgs: [tax.id],
      );

      // تسجيل العملية في سجل المراجعة
      if (oldTax != null) {
        await AuditService.logUpdate(
          entityType: 'tax',
          entityId: tax.id!,
          entityName: tax.name,
          oldValues: oldTax.toMap(),
          newValues: tax.toMap(),
          description: 'تحديث ضريبة: ${tax.name} (${tax.code})',
          category: 'Tax Management',
        );
      }

      LoggingService.info(
        'تم تحديث ضريبة',
        category: 'TaxService',
        data: {
          'taxId': tax.id,
          'code': tax.code,
          'name': tax.name,
          'rate': tax.rate,
        },
      );

      return result;
    });
  }

  /// حذف ضريبة
  Future<int> deleteTax(int id) async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    // التحقق من وجود الضريبة
    final tax = await getTaxById(id);
    if (tax == null) {
      throw business_exceptions.BusinessRuleException('الضريبة غير موجودة');
    }

    // التحقق من عدم استخدام الضريبة في فواتير
    final isUsed = await _isTaxUsedInInvoices(id);
    if (isUsed) {
      throw business_exceptions.BusinessRuleException(
        'لا يمكن حذف الضريبة لأنها مستخدمة في فواتير موجودة',
      );
    }

    final result = await db.delete(
      AppConstants.taxesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    // تسجيل العملية في سجل المراجعة
    await AuditService.logDelete(
      entityType: 'tax',
      entityId: id,
      entityName: tax.name,
      oldValues: tax.toMap(),
      description: 'حذف ضريبة: ${tax.name} (${tax.code})',
      category: 'Tax Management',
    );

    LoggingService.info(
      'تم حذف ضريبة',
      category: 'TaxService',
      data: {'taxId': id, 'code': tax.code, 'name': tax.name},
    );

    return result;
  }

  /// حساب الضريبة للفاتورة
  Future<Map<String, double>> calculateInvoiceTax(Invoice invoice) async {
    try {
      final includeTaxInPrice = await _settingsService.getIncludeTaxInPrice();
      final defaultTax = await getDefaultTax();

      if (defaultTax == null) {
        return {
          'taxAmount': 0.0,
          'subtotalBeforeTax': invoice.subtotal,
          'subtotalAfterTax': invoice.subtotal,
        };
      }

      double taxAmount = 0.0;
      double subtotalBeforeTax = invoice.subtotal;
      double subtotalAfterTax = invoice.subtotal;

      if (includeTaxInPrice) {
        // الضريبة مشمولة في السعر
        subtotalBeforeTax = invoice.subtotal / (1 + (defaultTax.rate / 100));
        taxAmount = invoice.subtotal - subtotalBeforeTax;
        subtotalAfterTax = invoice.subtotal;
      } else {
        // الضريبة مضافة على السعر
        taxAmount = defaultTax.calculateTaxAmount(invoice.subtotal);
        subtotalBeforeTax = invoice.subtotal;
        subtotalAfterTax = invoice.subtotal + taxAmount;
      }

      return {
        'taxAmount': taxAmount,
        'subtotalBeforeTax': subtotalBeforeTax,
        'subtotalAfterTax': subtotalAfterTax,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب ضريبة الفاتورة',
        category: 'TaxService',
        data: {'invoiceId': invoice.id, 'error': e.toString()},
      );
      return {
        'taxAmount': 0.0,
        'subtotalBeforeTax': invoice.subtotal,
        'subtotalAfterTax': invoice.subtotal,
      };
    }
  }

  /// حساب الضريبة لمبلغ محدد
  Future<double> calculateTaxForAmount(double amount, {Tax? tax}) async {
    tax ??= await getDefaultTax();
    if (tax == null) return 0.0;

    return tax.calculateTaxAmount(amount);
  }

  /// البحث في الضرائب
  Future<List<Tax>> searchTaxes(String query) async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.taxesTable,
      where: 'name LIKE ? OR code LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'is_default DESC, name ASC',
    );

    return result.map((map) => Tax.fromMap(map)).toList();
  }

  /// تفعيل/إلغاء تفعيل ضريبة
  Future<void> toggleTaxStatus(int id) async {
    final tax = await getTaxById(id);
    if (tax == null) {
      throw business_exceptions.BusinessRuleException('الضريبة غير موجودة');
    }

    await updateTax(tax.copyWith(isActive: !tax.isActive));
  }

  /// تعيين ضريبة كافتراضية
  Future<void> setDefaultTax(int id) async {
    final tax = await getTaxById(id);
    if (tax == null) {
      throw business_exceptions.BusinessRuleException('الضريبة غير موجودة');
    }

    if (!tax.isActive) {
      throw business_exceptions.BusinessRuleException(
        'لا يمكن تعيين ضريبة غير نشطة كافتراضية',
      );
    }

    await updateTax(tax.copyWith(isDefault: true));
  }

  /// إنشاء ضرائب افتراضية للنظام السوري
  Future<void> createDefaultSyrianTaxes() async {
    try {
      // ضريبة القيمة المضافة
      final vatExists = await getTaxByCode('VAT');
      if (vatExists == null) {
        await insertTax(
          Tax(
            code: 'VAT',
            name: 'ضريبة القيمة المضافة',
            type: 'percentage',
            rate: 11.0, // النسبة الحالية في سوريا
            isDefault: true,
            description: 'ضريبة القيمة المضافة حسب القانون السوري',
          ),
        );
      }

      // ضريبة الخدمات
      final serviceExists = await getTaxByCode('SERVICE');
      if (serviceExists == null) {
        await insertTax(
          Tax(
            code: 'SERVICE',
            name: 'ضريبة الخدمات',
            type: 'percentage',
            rate: 5.0,
            description: 'ضريبة الخدمات',
          ),
        );
      }

      LoggingService.info(
        'تم إنشاء الضرائب الافتراضية للنظام السوري',
        category: 'TaxService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الضرائب الافتراضية',
        category: 'TaxService',
        data: {'error': e.toString()},
      );
    }
  }

  // ===============================
  // الطرق المساعدة
  // ===============================

  /// التحقق من صحة بيانات الضريبة
  void _validateTax(Tax tax) {
    if (tax.code.trim().isEmpty) {
      throw validation_exceptions.ValidationException('كود الضريبة مطلوب');
    }

    if (tax.name.trim().isEmpty) {
      throw validation_exceptions.ValidationException('اسم الضريبة مطلوب');
    }

    if (tax.rate < 0) {
      throw validation_exceptions.ValidationException(
        'معدل الضريبة لا يمكن أن يكون سالباً',
      );
    }

    if (tax.type == 'percentage' && tax.rate > 100) {
      throw validation_exceptions.ValidationException(
        'نسبة الضريبة لا يمكن أن تزيد عن 100%',
      );
    }

    if (!['percentage', 'fixed'].contains(tax.type)) {
      throw validation_exceptions.ValidationException('نوع الضريبة غير صحيح');
    }
  }

  /// التحقق من استخدام الضريبة في الفواتير
  Future<bool> _isTaxUsedInInvoices(int taxId) async {
    final db = await _databaseHelper.database;

    try {
      final result = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM ${AppConstants.invoicesTable}
        WHERE tax_amount > 0
        LIMIT 1
      ''');

      final count = result.first['count'] as int;
      return count > 0;
    } catch (e) {
      // إذا كان هناك خطأ في الاستعلام، نفترض أن الضريبة مستخدمة للأمان
      return true;
    }
  }

  /// الحصول على إحصائيات الضرائب
  Future<Map<String, dynamic>> getTaxStatistics() async {
    await _createTaxesTableIfNotExists();
    final db = await _databaseHelper.database;

    final result = await db.rawQuery('''
      SELECT
        COUNT(*) as total_taxes,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_taxes,
        COUNT(CASE WHEN is_default = 1 THEN 1 END) as default_taxes
      FROM ${AppConstants.taxesTable}
    ''');

    return result.first;
  }
}
