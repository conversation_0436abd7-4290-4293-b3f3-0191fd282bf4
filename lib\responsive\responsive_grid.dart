import 'package:flutter/material.dart';
import 'app_dimensions.dart';
import 'breakpoints.dart';

/// شبكة متجاوبة تتكيف مع حجم الشاشة
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? columns;
  final double? spacing;
  final double? runSpacing;
  final double? childAspectRatio;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.columns,
    this.spacing,
    this.runSpacing,
    this.childAspectRatio,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);
    final effectiveColumns = columns ?? dimensions.gridColumns;
    final effectiveSpacing = spacing ?? dimensions.gridSpacing;
    final effectiveRunSpacing = runSpacing ?? dimensions.gridSpacing;
    final effectiveAspectRatio = childAspectRatio ?? dimensions.cardAspectRatio;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingM);

    return Padding(
      padding: effectivePadding,
      child: GridView.builder(
        shrinkWrap: shrinkWrap,
        physics: physics,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: effectiveColumns,
          crossAxisSpacing: effectiveSpacing,
          mainAxisSpacing: effectiveRunSpacing,
          childAspectRatio: effectiveAspectRatio,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

/// شبكة متجاوبة مع تحكم مخصص في الأعمدة
class CustomResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final ResponsiveGridColumns columns;
  final double? spacing;
  final double? runSpacing;
  final double? childAspectRatio;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;

  const CustomResponsiveGrid({
    super.key,
    required this.children,
    required this.columns,
    this.spacing,
    this.runSpacing,
    this.childAspectRatio,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);
    final deviceType = DeviceType.fromWidth(MediaQuery.of(context).size.width);
    
    int effectiveColumns;
    switch (deviceType) {
      case DeviceType.mobile:
        effectiveColumns = columns.mobile;
        break;
      case DeviceType.tablet:
        effectiveColumns = columns.tablet;
        break;
      case DeviceType.desktop:
        effectiveColumns = columns.desktop;
        break;
      case DeviceType.largeDesktop:
        effectiveColumns = columns.largeDesktop;
        break;
    }

    final effectiveSpacing = spacing ?? dimensions.gridSpacing;
    final effectiveRunSpacing = runSpacing ?? dimensions.gridSpacing;
    final effectiveAspectRatio = childAspectRatio ?? dimensions.cardAspectRatio;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingM);

    return Padding(
      padding: effectivePadding,
      child: GridView.builder(
        shrinkWrap: shrinkWrap,
        physics: physics,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: effectiveColumns,
          crossAxisSpacing: effectiveSpacing,
          mainAxisSpacing: effectiveRunSpacing,
          childAspectRatio: effectiveAspectRatio,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

/// تكوين الأعمدة للشبكة المتجاوبة
class ResponsiveGridColumns {
  final int mobile;
  final int tablet;
  final int desktop;
  final int largeDesktop;

  const ResponsiveGridColumns({
    required this.mobile,
    required this.tablet,
    required this.desktop,
    required this.largeDesktop,
  });

  /// تكوين افتراضي للبطاقات
  static const cards = ResponsiveGridColumns(
    mobile: 1,
    tablet: 2,
    desktop: 3,
    largeDesktop: 4,
  );

  /// تكوين للعناصر الصغيرة
  static const items = ResponsiveGridColumns(
    mobile: 2,
    tablet: 3,
    desktop: 4,
    largeDesktop: 6,
  );

  /// تكوين للإحصائيات
  static const stats = ResponsiveGridColumns(
    mobile: 1,
    tablet: 2,
    desktop: 4,
    largeDesktop: 6,
  );
}

/// قائمة متجاوبة تتحول إلى شبكة على الشاشات الكبيرة
class ResponsiveListGrid extends StatelessWidget {
  final List<Widget> children;
  final bool forceList;
  final bool forceGrid;
  final ResponsiveGridColumns? gridColumns;
  final double? spacing;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveListGrid({
    super.key,
    required this.children,
    this.forceList = false,
    this.forceGrid = false,
    this.gridColumns,
    this.spacing,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);
    final deviceType = DeviceType.fromWidth(MediaQuery.of(context).size.width);
    final effectiveSpacing = spacing ?? dimensions.spacingM;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingM);

    // تحديد ما إذا كان يجب استخدام الشبكة أم القائمة
    bool useGrid = forceGrid || (!forceList && !deviceType.isMobile);

    if (useGrid) {
      final columns = gridColumns ?? ResponsiveGridColumns.cards;
      return CustomResponsiveGrid(
        columns: columns,
        spacing: effectiveSpacing,
        runSpacing: effectiveSpacing,
        shrinkWrap: shrinkWrap,
        physics: physics,
        padding: effectivePadding,
        children: children,
      );
    } else {
      return Padding(
        padding: effectivePadding,
        child: ListView.separated(
          shrinkWrap: shrinkWrap,
          physics: physics,
          itemCount: children.length,
          separatorBuilder: (context, index) => SizedBox(height: effectiveSpacing),
          itemBuilder: (context, index) => children[index],
        ),
      );
    }
  }
}

/// شبكة متجاوبة للبطاقات مع تحكم في الحد الأقصى للعرض
class ResponsiveCardGrid extends StatelessWidget {
  final List<Widget> children;
  final double? maxCardWidth;
  final double? minCardWidth;
  final double? spacing;
  final double? childAspectRatio;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveCardGrid({
    super.key,
    required this.children,
    this.maxCardWidth,
    this.minCardWidth = 200.0,
    this.spacing,
    this.childAspectRatio,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final effectiveSpacing = spacing ?? dimensions.gridSpacing;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingM);
    final effectiveMinWidth = minCardWidth ?? 200.0;
    final effectiveMaxWidth = maxCardWidth ?? 400.0;

    // حساب عدد الأعمدة بناءً على عرض الشاشة وعرض البطاقة
    final availableWidth = screenWidth - (effectivePadding.horizontal);
    int columns = (availableWidth / (effectiveMinWidth + effectiveSpacing)).floor();
    columns = columns.clamp(1, 6); // حد أدنى 1 وحد أقصى 6 أعمدة

    // التأكد من أن عرض البطاقة لا يتجاوز الحد الأقصى
    final cardWidth = (availableWidth - (effectiveSpacing * (columns - 1))) / columns;
    if (cardWidth > effectiveMaxWidth) {
      columns = (availableWidth / (effectiveMaxWidth + effectiveSpacing)).floor();
      columns = columns.clamp(1, 6);
    }

    return Padding(
      padding: effectivePadding,
      child: GridView.builder(
        shrinkWrap: shrinkWrap,
        physics: physics,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          crossAxisSpacing: effectiveSpacing,
          mainAxisSpacing: effectiveSpacing,
          childAspectRatio: childAspectRatio ?? dimensions.cardAspectRatio,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

/// شبكة متجاوبة مع تحكم في الارتفاع التلقائي
class ResponsiveStaggeredGrid extends StatelessWidget {
  final List<Widget> children;
  final int? columns;
  final double? spacing;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveStaggeredGrid({
    super.key,
    required this.children,
    this.columns,
    this.spacing,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = AppDimensions.of(context);
    final effectiveColumns = columns ?? dimensions.gridColumns;
    final effectiveSpacing = spacing ?? dimensions.gridSpacing;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingM);

    return Padding(
      padding: effectivePadding,
      child: _StaggeredGridView(
        columns: effectiveColumns,
        spacing: effectiveSpacing,
        shrinkWrap: shrinkWrap,
        physics: physics,
        children: children,
      ),
    );
  }
}

/// تنفيذ مبسط لشبكة متدرجة
class _StaggeredGridView extends StatelessWidget {
  final List<Widget> children;
  final int columns;
  final double spacing;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const _StaggeredGridView({
    required this.children,
    required this.columns,
    required this.spacing,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final columnWidth = (constraints.maxWidth - (spacing * (columns - 1))) / columns;
        
        return SingleChildScrollView(
          physics: physics,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(columns, (columnIndex) {
              return SizedBox(
                width: columnWidth,
                child: Column(
                  children: _getColumnChildren(columnIndex),
                ),
              );
            }).expand((column) => [
              column,
              if (column != children.last) SizedBox(width: spacing),
            ]).toList(),
          ),
        );
      },
    );
  }

  List<Widget> _getColumnChildren(int columnIndex) {
    final columnChildren = <Widget>[];
    
    for (int i = columnIndex; i < children.length; i += columns) {
      columnChildren.add(children[i]);
      if (i + columns < children.length) {
        columnChildren.add(SizedBox(height: spacing));
      }
    }
    
    return columnChildren;
  }
}
