import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../services/advanced_export_service.dart';
import '../services/chart_export_service.dart';
import '../services/logging_service.dart';
import 'advanced_export_dialog.dart';

/// ويدجت خيارات التصدير السريع
class ExportOptionsWidget extends StatelessWidget {
  final String reportType;
  final dynamic reportData;
  final Map<String, dynamic> filters;
  final GlobalKey? chartKey;
  final VoidCallback? onExportComplete;

  const ExportOptionsWidget({
    super.key,
    required this.reportType,
    required this.reportData,
    required this.filters,
    this.chartKey,
    this.onExportComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(Icons.file_download, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'خيارات التصدير',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // أزرار التصدير السريع
          Row(
            children: [
              Expanded(
                child: _buildQuickExportButton(
                  context,
                  icon: Icons.picture_as_pdf,
                  label: 'PDF',
                  color: Colors.red,
                  onPressed: () => _quickExportPDF(context),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickExportButton(
                  context,
                  icon: Icons.table_chart,
                  label: 'Excel',
                  color: Colors.green,
                  onPressed: () => _quickExportExcel(context),
                ),
              ),
              if (chartKey != null) ...[
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickExportButton(
                    context,
                    icon: Icons.image,
                    label: 'رسم بياني',
                    color: Colors.blue,
                    onPressed: () => _exportChart(context),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),

          // زر التصدير المتقدم
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showAdvancedExportDialog(context),
              icon: Icon(Icons.settings, color: AppColors.primary),
              label: Text(
                'خيارات متقدمة',
                style: TextStyle(color: AppColors.primary),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickExportButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Future<void> _quickExportPDF(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'جاري تصدير PDF...');

      final filePath = await AdvancedExportService.exportAdvancedPDF(
        reportType: reportType,
        reportData: reportData,
        filters: filters,
      );

      if (context.mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        if (filePath != null) {
          _showSuccessMessage(context, 'تم تصدير PDF بنجاح');
          onExportComplete?.call();
        } else {
          _showErrorMessage(context, 'فشل في تصدير PDF');
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop();
        _showErrorMessage(context, 'خطأ في تصدير PDF: ${e.toString()}');
      }
      LoggingService.error(
        'خطأ في التصدير السريع PDF',
        category: 'ExportOptions',
        data: {'error': e.toString()},
      );
    }
  }

  Future<void> _quickExportExcel(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'جاري تصدير Excel...');

      final filePath = await AdvancedExportService.exportAdvancedExcel(
        reportType: reportType,
        reportData: reportData,
        filters: filters,
      );

      if (context.mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        if (filePath != null) {
          _showSuccessMessage(context, 'تم تصدير Excel بنجاح');
          onExportComplete?.call();
        } else {
          _showErrorMessage(context, 'فشل في تصدير Excel');
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop();
        _showErrorMessage(context, 'خطأ في تصدير Excel: ${e.toString()}');
      }
      LoggingService.error(
        'خطأ في التصدير السريع Excel',
        category: 'ExportOptions',
        data: {'error': e.toString()},
      );
    }
  }

  Future<void> _exportChart(BuildContext context) async {
    if (chartKey == null) return;

    try {
      _showLoadingDialog(context, 'جاري تصدير الرسم البياني...');

      final filePath = await ChartExportService.saveChartAsImage(
        repaintBoundaryKey: chartKey!,
        reportType: reportType,
      );

      if (context.mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        if (filePath != null) {
          _showSuccessMessage(context, 'تم حفظ الرسم البياني بنجاح');
          onExportComplete?.call();
        } else {
          _showErrorMessage(context, 'فشل في حفظ الرسم البياني');
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop();
        _showErrorMessage(context, 'خطأ في تصدير الرسم البياني: ${e.toString()}');
      }
      LoggingService.error(
        'خطأ في تصدير الرسم البياني',
        category: 'ExportOptions',
        data: {'error': e.toString()},
      );
    }
  }

  void _showAdvancedExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AdvancedExportDialog(
        reportType: reportType,
        reportData: reportData,
        filters: filters,
        onExportComplete: onExportComplete,
      ),
    );
  }

  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// ويدجت مبسط لخيارات التصدير في شريط الأدوات
class ExportToolbarWidget extends StatelessWidget {
  final String reportType;
  final dynamic reportData;
  final Map<String, dynamic> filters;
  final GlobalKey? chartKey;
  final VoidCallback? onExportComplete;

  const ExportToolbarWidget({
    super.key,
    required this.reportType,
    required this.reportData,
    required this.filters,
    this.chartKey,
    this.onExportComplete,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.file_download, color: AppColors.primary),
      tooltip: 'تصدير التقرير',
      onSelected: (value) => _handleExportOption(context, value),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'pdf',
          child: Row(
            children: [
              Icon(Icons.picture_as_pdf, color: Colors.red),
              SizedBox(width: 8),
              Text('تصدير PDF'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'excel',
          child: Row(
            children: [
              Icon(Icons.table_chart, color: Colors.green),
              SizedBox(width: 8),
              Text('تصدير Excel'),
            ],
          ),
        ),
        if (chartKey != null)
          const PopupMenuItem(
            value: 'chart',
            child: Row(
              children: [
                Icon(Icons.image, color: Colors.blue),
                SizedBox(width: 8),
                Text('حفظ الرسم البياني'),
              ],
            ),
          ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'advanced',
          child: Row(
            children: [
              Icon(Icons.settings, color: Colors.grey),
              SizedBox(width: 8),
              Text('خيارات متقدمة'),
            ],
          ),
        ),
      ],
    );
  }

  void _handleExportOption(BuildContext context, String option) {
    final exportWidget = ExportOptionsWidget(
      reportType: reportType,
      reportData: reportData,
      filters: filters,
      chartKey: chartKey,
      onExportComplete: onExportComplete,
    );

    switch (option) {
      case 'pdf':
        exportWidget._quickExportPDF(context);
        break;
      case 'excel':
        exportWidget._quickExportExcel(context);
        break;
      case 'chart':
        exportWidget._exportChart(context);
        break;
      case 'advanced':
        exportWidget._showAdvancedExportDialog(context);
        break;
    }
  }
}
