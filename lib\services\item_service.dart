import '../database/database_helper.dart';
import '../models/item.dart';
import '../constants/app_constants.dart';
import 'validation_service.dart';
import '../exceptions/validation_exception.dart';

class ItemService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<Item>> getAllItems() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Item.fromMap(maps[i]);
    });
  }

  Future<List<Item>> getActiveItems() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Item.fromMap(maps[i]);
    });
  }

  Future<Item?> getItemById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Item.fromMap(maps.first);
    }
    return null;
  }

  Future<Item?> getItemByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Item.fromMap(maps.first);
    }
    return null;
  }

  Future<int> insertItem(Item item) async {
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات باستخدام ValidationService
    final validation = ValidationService.validateItem(
      code: item.code,
      name: item.name,
      unit: item.unit,
      costPrice: item.costPrice,
      sellingPrice: item.sellingPrice,
      quantity: item.quantity,
      minQuantity: item.minQuantity,
    );

    if (!validation.isValid) {
      throw ValidationException(validation.errorMessage!);
    }

    // التحقق من عدم تكرار الكود
    final existingItem = await getItemByCode(item.code);
    if (existingItem != null) {
      throw ValidationException.duplicate('كود الصنف', item.code);
    }

    // تنظيف البيانات
    final cleanItem = item.copyWith(
      name: ValidationService.sanitizeText(item.name),
      unit: ValidationService.sanitizeText(item.unit),
      description: item.description != null
          ? ValidationService.sanitizeText(item.description!)
          : null,
    );

    final itemData = cleanItem.toMap();
    itemData.remove('id'); // إزالة المعرف للإدراج التلقائي

    return await db.insert(AppConstants.itemsTable, itemData);
  }

  Future<int> updateItem(Item item) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم تكرار الكود مع أصناف أخرى
    final existingItem = await getItemByCode(item.code);
    if (existingItem != null && existingItem.id != item.id) {
      throw Exception('كود الصنف موجود مسبقاً');
    }

    final itemData = item.copyWith(updatedAt: DateTime.now()).toMap();

    return await db.update(
      AppConstants.itemsTable,
      itemData,
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<int> deleteItem(int id) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم وجود فواتير للصنف
    final invoiceItems = await db.query(
      AppConstants.invoiceItemsTable,
      where: 'item_id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (invoiceItems.isNotEmpty) {
      throw Exception('لا يمكن حذف الصنف لوجود فواتير مرتبطة به');
    }

    return await db.delete(
      AppConstants.itemsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateItemQuantity(
    int itemId,
    double quantity,
    bool isIncrease,
  ) async {
    final db = await _databaseHelper.database;
    final item = await getItemById(itemId);

    if (item == null) {
      throw Exception('الصنف غير موجود');
    }

    double newQuantity = item.quantity;
    newQuantity += isIncrease ? quantity : -quantity;

    if (newQuantity < 0) {
      throw Exception('الكمية المطلوبة غير متوفرة في المخزون');
    }

    await db.update(
      AppConstants.itemsTable,
      {'quantity': newQuantity, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  Future<List<Item>> searchItems(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      where:
          '(name LIKE ? OR code LIKE ? OR description LIKE ?) AND is_active = 1',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Item.fromMap(maps[i]);
    });
  }

  Future<String> generateItemCode() async {
    final db = await _databaseHelper.database;

    // استعلام محسن للحصول على أكبر رقم كود
    final result = await db.rawQuery('''
      SELECT MAX(CAST(SUBSTR(code, 2) AS INTEGER)) as max_number
      FROM ${AppConstants.itemsTable}
      WHERE code LIKE 'I%' AND code GLOB 'I[0-9]*'
    ''');

    final maxNumber = result.first['max_number'] as int? ?? 0;
    return 'I${(maxNumber + 1).toString().padLeft(4, '0')}';
  }

  Future<List<Item>> getLowStockItems() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      where: 'quantity <= min_quantity AND min_quantity > 0 AND is_active = 1',
      orderBy: 'quantity ASC',
    );

    return List.generate(maps.length, (i) {
      return Item.fromMap(maps[i]);
    });
  }

  Future<List<Item>> getOutOfStockItems() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.itemsTable,
      where: 'quantity <= 0 AND is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Item.fromMap(maps[i]);
    });
  }

  Future<Map<String, double>> getItemsSummary() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_items,
        COUNT(CASE WHEN quantity <= 0 THEN 1 END) as out_of_stock,
        COUNT(CASE WHEN quantity <= min_quantity AND min_quantity > 0 THEN 1 END) as low_stock,
        SUM(quantity * cost_price) as total_cost_value,
        SUM(quantity * selling_price) as total_selling_value
      FROM ${AppConstants.itemsTable}
    ''');

    final row = result.first;
    return {
      'total_items': (row['total_items'] as num?)?.toDouble() ?? 0.0,
      'active_items': (row['active_items'] as num?)?.toDouble() ?? 0.0,
      'out_of_stock': (row['out_of_stock'] as num?)?.toDouble() ?? 0.0,
      'low_stock': (row['low_stock'] as num?)?.toDouble() ?? 0.0,
      'total_cost_value': (row['total_cost_value'] as num?)?.toDouble() ?? 0.0,
      'total_selling_value':
          (row['total_selling_value'] as num?)?.toDouble() ?? 0.0,
    };
  }
}
