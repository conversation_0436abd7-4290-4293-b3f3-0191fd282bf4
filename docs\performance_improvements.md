# تحسينات الأداء المقترحة - Smart Ledger

## 🚀 تحسينات فورية (High Priority)

### 1. تحسين دوال إنشاء الأكواد

#### المشكلة:
```dart
// كود غير فعال - يحمل جميع السجلات
Future<String> generateCustomerCode() async {
  final customers = await getAllCustomers(); // ❌ بطيء مع البيانات الكبيرة
  // معالجة في Dart...
}
```

#### الحل:
```dart
// كود محسن - استعلام SQL مباشر
Future<String> generateCustomerCode() async {
  final db = await _databaseHelper.database;
  final result = await db.rawQuery('''
    SELECT MAX(CAST(SUBSTR(code, 2) AS INTEGER)) as max_number 
    FROM ${AppConstants.customersTable} 
    WHERE code LIKE 'C%' AND code REGEXP '^C[0-9]+$'
  ''');
  
  final maxNumber = result.first['max_number'] as int? ?? 0;
  return 'C${(maxNumber + 1).toString().padLeft(4, '0')}';
}
```

### 2. إضافة فهارس قاعدة البيانات

```sql
-- فهارس للبحث السريع
CREATE INDEX idx_customers_code ON customers(code);
CREATE INDEX idx_customers_name ON customers(name);
CREATE INDEX idx_customers_active ON customers(is_active);
CREATE INDEX idx_accounts_type ON accounts(type);
CREATE INDEX idx_accounts_code ON accounts(code);
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_journal_entries_number ON journal_entries(entry_number);

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX idx_customers_active_balance ON customers(is_active, balance);
CREATE INDEX idx_accounts_type_active ON accounts(type, is_active);
```

### 3. حل مشكلة N+1 Query في القيود المحاسبية

#### المشكلة:
```dart
// N+1 Query Problem
for (final map in maps) {
  final entry = JournalEntry.fromMap(map);
  final details = await getJournalEntryDetails(entry.id!); // استعلام إضافي لكل قيد
}
```

#### الحل:
```dart
Future<List<JournalEntry>> getAllJournalEntriesOptimized() async {
  final db = await _databaseHelper.database;
  
  // استعلام واحد مع JOIN
  final result = await db.rawQuery('''
    SELECT 
      je.id, je.entry_number, je.entry_date, je.description, je.type,
      je.total_debit, je.total_credit, je.currency_id, je.is_posted,
      je.created_at, je.updated_at,
      jed.id as detail_id, jed.account_id, jed.debit_amount, 
      jed.credit_amount, jed.description as detail_description,
      a.name as account_name, a.code as account_code
    FROM ${AppConstants.journalEntriesTable} je
    LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON je.id = jed.journal_entry_id
    LEFT JOIN ${AppConstants.accountsTable} a ON jed.account_id = a.id
    ORDER BY je.entry_date DESC, je.id DESC, jed.id ASC
  ''');
  
  return _groupJournalEntries(result);
}

List<JournalEntry> _groupJournalEntries(List<Map<String, dynamic>> result) {
  Map<int, JournalEntry> entriesMap = {};
  
  for (final row in result) {
    final entryId = row['id'] as int;
    
    if (!entriesMap.containsKey(entryId)) {
      entriesMap[entryId] = JournalEntry.fromMap(row).copyWith(details: []);
    }
    
    if (row['detail_id'] != null) {
      final detail = JournalEntryDetail.fromMap({
        'id': row['detail_id'],
        'journal_entry_id': entryId,
        'account_id': row['account_id'],
        'debit_amount': row['debit_amount'],
        'credit_amount': row['credit_amount'],
        'description': row['detail_description'],
      });
      
      entriesMap[entryId] = entriesMap[entryId]!.copyWith(
        details: [...entriesMap[entryId]!.details, detail]
      );
    }
  }
  
  return entriesMap.values.toList();
}
```

## 📄 تحسينات متوسطة الأولوية

### 4. إضافة Pagination للقوائم الطويلة

```dart
class PaginatedService {
  static const int defaultPageSize = 50;
  
  Future<PaginatedResult<Customer>> getCustomersPaginated({
    int page = 1,
    int pageSize = defaultPageSize,
    String? searchTerm,
  }) async {
    final db = await _databaseHelper.database;
    final offset = (page - 1) * pageSize;
    
    String whereClause = 'is_active = 1';
    List<dynamic> whereArgs = [];
    
    if (searchTerm != null && searchTerm.isNotEmpty) {
      whereClause += ' AND (name LIKE ? OR code LIKE ?)';
      whereArgs.addAll(['%$searchTerm%', '%$searchTerm%']);
    }
    
    // الحصول على العدد الإجمالي
    final countResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.customersTable} WHERE $whereClause',
      whereArgs,
    );
    final totalCount = countResult.first['count'] as int;
    
    // الحصول على البيانات المطلوبة
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.customersTable,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'name ASC',
      limit: pageSize,
      offset: offset,
    );
    
    final customers = maps.map((map) => Customer.fromMap(map)).toList();
    
    return PaginatedResult<Customer>(
      items: customers,
      currentPage: page,
      pageSize: pageSize,
      totalCount: totalCount,
      totalPages: (totalCount / pageSize).ceil(),
    );
  }
}

class PaginatedResult<T> {
  final List<T> items;
  final int currentPage;
  final int pageSize;
  final int totalCount;
  final int totalPages;
  
  const PaginatedResult({
    required this.items,
    required this.currentPage,
    required this.pageSize,
    required this.totalCount,
    required this.totalPages,
  });
  
  bool get hasNextPage => currentPage < totalPages;
  bool get hasPreviousPage => currentPage > 1;
}
```

### 5. إضافة Caching للبيانات المتكررة

```dart
class CacheService {
  static final Map<String, CacheEntry> _cache = {};
  static const Duration defaultCacheDuration = Duration(minutes: 5);
  
  static T? get<T>(String key) {
    final entry = _cache[key];
    if (entry == null || entry.isExpired) {
      _cache.remove(key);
      return null;
    }
    return entry.value as T;
  }
  
  static void set<T>(String key, T value, {Duration? duration}) {
    _cache[key] = CacheEntry(
      value: value,
      expiresAt: DateTime.now().add(duration ?? defaultCacheDuration),
    );
  }
  
  static void clear([String? key]) {
    if (key != null) {
      _cache.remove(key);
    } else {
      _cache.clear();
    }
  }
}

class CacheEntry {
  final dynamic value;
  final DateTime expiresAt;
  
  CacheEntry({required this.value, required this.expiresAt});
  
  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

// استخدام Cache في الخدمات
class AccountService {
  Future<List<Account>> getAccountsByType(String type) async {
    final cacheKey = 'accounts_by_type_$type';
    
    // محاولة الحصول من Cache
    List<Account>? cachedAccounts = CacheService.get<List<Account>>(cacheKey);
    if (cachedAccounts != null) {
      return cachedAccounts;
    }
    
    // تحميل من قاعدة البيانات
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'type = ? AND is_active = 1',
      whereArgs: [type],
      orderBy: 'code ASC',
    );
    
    final accounts = maps.map((map) => Account.fromMap(map)).toList();
    
    // حفظ في Cache
    CacheService.set(cacheKey, accounts);
    
    return accounts;
  }
}
```

### 6. تحسين استعلامات التقارير

```dart
class OptimizedReportsService {
  // تقرير ميزان المراجعة محسن
  Future<List<TrialBalanceItem>> getTrialBalanceOptimized() async {
    final db = await _databaseHelper.database;
    
    // استعلام واحد محسن
    final result = await db.rawQuery('''
      SELECT 
        a.id,
        a.code,
        a.name,
        a.type,
        COALESCE(SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0) as total_debit,
        COALESCE(SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0) as total_credit,
        a.balance as opening_balance
      FROM ${AppConstants.accountsTable} a
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.is_active = 1 AND (je.is_posted = 1 OR je.id IS NULL)
      GROUP BY a.id, a.code, a.name, a.type, a.balance
      ORDER BY a.code ASC
    ''');
    
    return result.map((row) => TrialBalanceItem.fromMap(row)).toList();
  }
  
  // تقرير الأرباح والخسائر محسن
  Future<ProfitLossReport> getProfitLossOptimized(DateTime fromDate, DateTime toDate) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        a.type,
        SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
        SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) as net_amount
      FROM ${AppConstants.accountsTable} a
      JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.type IN ('${AppConstants.accountTypeRevenue}', '${AppConstants.accountTypeExpense}')
        AND je.is_posted = 1
        AND je.entry_date BETWEEN ? AND ?
      GROUP BY a.type
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    double totalRevenue = 0;
    double totalExpense = 0;
    
    for (final row in result) {
      final type = row['type'] as String;
      final amount = (row['net_amount'] as num).toDouble();
      
      if (type == AppConstants.accountTypeRevenue) {
        totalRevenue = amount;
      } else if (type == AppConstants.accountTypeExpense) {
        totalExpense = -amount; // المصروفات سالبة
      }
    }
    
    return ProfitLossReport(
      fromDate: fromDate,
      toDate: toDate,
      totalRevenue: totalRevenue,
      totalExpense: totalExpense,
      netProfit: totalRevenue - totalExpense,
    );
  }
}
```

## 🔧 تحسينات طويلة المدى

### 7. إضافة Connection Pooling

```dart
class DatabaseConnectionPool {
  static const int maxConnections = 5;
  static final Queue<Database> _availableConnections = Queue<Database>();
  static final Set<Database> _allConnections = <Database>{};
  static bool _isInitialized = false;
  
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    for (int i = 0; i < maxConnections; i++) {
      final db = await _createConnection();
      _availableConnections.add(db);
      _allConnections.add(db);
    }
    
    _isInitialized = true;
  }
  
  static Future<Database> getConnection() async {
    if (!_isInitialized) await initialize();
    
    if (_availableConnections.isNotEmpty) {
      return _availableConnections.removeFirst();
    }
    
    // إذا لم تكن هناك اتصالات متاحة، انتظر
    await Future.delayed(const Duration(milliseconds: 10));
    return getConnection();
  }
  
  static void releaseConnection(Database db) {
    if (_allConnections.contains(db)) {
      _availableConnections.add(db);
    }
  }
  
  static Future<Database> _createConnection() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    return await openDatabase(path, version: AppConstants.databaseVersion);
  }
}
```

### 8. إضافة Background Processing

```dart
class BackgroundTaskService {
  static final Queue<BackgroundTask> _taskQueue = Queue<BackgroundTask>();
  static bool _isProcessing = false;
  
  static void addTask(BackgroundTask task) {
    _taskQueue.add(task);
    _processQueue();
  }
  
  static Future<void> _processQueue() async {
    if (_isProcessing || _taskQueue.isEmpty) return;
    
    _isProcessing = true;
    
    while (_taskQueue.isNotEmpty) {
      final task = _taskQueue.removeFirst();
      try {
        await task.execute();
      } catch (e) {
        print('Background task failed: $e');
      }
    }
    
    _isProcessing = false;
  }
}

abstract class BackgroundTask {
  Future<void> execute();
}

class CalculateAccountBalancesTask extends BackgroundTask {
  @override
  Future<void> execute() async {
    // حساب أرصدة الحسابات في الخلفية
    final accountService = AccountService();
    await accountService.recalculateAllBalances();
  }
}
```

---

**ملاحظة:** هذه التحسينات ستؤدي إلى تحسن كبير في الأداء، خاصة مع البيانات الكبيرة. يُنصح بتطبيقها تدريجياً واختبار كل تحسين على حدة.
