import 'package:flutter/material.dart';
import '../models/dashboard_models.dart';
import '../services/advanced_dashboard_service.dart';
import '../widgets/advanced_dashboard_widgets.dart';
import '../widgets/loading_widget.dart';
import '../constants/app_colors.dart';
import '../responsive/responsive.dart';

/// شاشة لوحة التحكم المتقدمة
/// تعرض مؤشرات الأداء الرئيسية والرسوم البيانية والملخص المالي
class AdvancedDashboardScreen extends StatefulWidget {
  const AdvancedDashboardScreen({super.key});

  @override
  State<AdvancedDashboardScreen> createState() =>
      _AdvancedDashboardScreenState();
}

class _AdvancedDashboardScreenState extends State<AdvancedDashboardScreen>
    with TickerProviderStateMixin {
  final AdvancedDashboardService _dashboardService = AdvancedDashboardService();

  DashboardData? _dashboardData;
  bool _isLoading = true;
  String? _error;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDashboardData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final data = await _dashboardService.getDashboardData(
        startDate: _startDate,
        endDate: _endDate,
      );

      setState(() {
        _dashboardData = data;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل بيانات لوحة التحكم: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshData() async {
    await _loadDashboardData();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      await _loadDashboardData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h3('لوحة التحكم المتقدمة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'اختيار الفترة الزمنية',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingWidget());
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_dashboardData == null) {
      return _buildEmptyWidget();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDateRangeHeader(),
              const SizedBox(height: 16),
              _buildKPISection(),
              const SizedBox(height: 16),
              _buildFinancialSummarySection(),
              const SizedBox(height: 16),
              _buildChartsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: AppColors.primary, size: 20),
            const SizedBox(width: 8),
            ResponsiveText.body(
              'الفترة: ${_formatDate(_startDate)} - ${_formatDate(_endDate)}',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _selectDateRange,
              icon: const Icon(Icons.edit),
              label: const ResponsiveText.caption('تغيير'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKPISection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText.h3(
          'مؤشرات الأداء الرئيسية',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getCrossAxisCount(context),
            childAspectRatio: 1.2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _dashboardData!.kpis.length,
          itemBuilder: (context, index) {
            return KPICard(
              kpi: _dashboardData!.kpis[index],
              onTap: () => _showKPIDetails(_dashboardData!.kpis[index]),
            );
          },
        ),
      ],
    );
  }

  Widget _buildFinancialSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText.h3(
          'الملخص المالي',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        FinancialSummaryCard(summary: _dashboardData!.financialSummary),
      ],
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText.h3(
          'الرسوم البيانية',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),

        // رسم الإيرادات
        FinancialLineChart(
          data: _dashboardData!.revenueChart,
          title: 'الإيرادات اليومية',
          color: AppColors.success,
          height: 250,
        ),

        // رسم المصروفات
        FinancialLineChart(
          data: _dashboardData!.expenseChart,
          title: 'المصروفات اليومية',
          color: AppColors.error,
          height: 250,
        ),

        // رسم الأرباح
        FinancialLineChart(
          data: _dashboardData!.profitChart,
          title: 'صافي الربح اليومي',
          color: AppColors.primary,
          height: 250,
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 16),
          ResponsiveText.body(
            _error!,
            textAlign: TextAlign.center,
            style: const TextStyle(color: AppColors.error),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
            label: const ResponsiveText.body('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          ResponsiveText.body(
            'لا توجد بيانات لعرضها',
            style: TextStyle(
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showKPIDetails(KPIModel kpi) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ResponsiveText.h3(kpi.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText.body('القيمة: ${kpi.value} ${kpi.unit}'),
            const SizedBox(height: 8),
            ResponsiveText.body(
              'النسبة: ${kpi.percentage.toStringAsFixed(1)}%',
            ),
            const SizedBox(height: 8),
            ResponsiveText.body('الوصف: ${kpi.description}'),
            const SizedBox(height: 8),
            ResponsiveText.caption(
              'آخر تحديث: ${_formatDate(kpi.lastUpdated)}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const ResponsiveText.body('إغلاق'),
          ),
        ],
      ),
    );
  }
}
