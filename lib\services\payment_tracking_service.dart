/// خدمة تتبع الدفعات والمدفوعات الجزئية المتقدمة
/// تتعامل مع ربط الدفعات بالفواتير وتتبع التفاصيل المالية
library;

import '../database/database_helper.dart';
import '../models/payment.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../models/journal_entry.dart';
import '../services/payment_service.dart';
import '../services/invoice_service.dart';
import '../services/journal_entry_service.dart';

/// نموذج تفاصيل ربط الدفعة
class PaymentAllocation {
  final int? id;
  final int paymentId;
  final int invoiceId;
  final double allocatedAmount;
  final DateTime allocationDate;
  final String? notes;

  PaymentAllocation({
    this.id,
    required this.paymentId,
    required this.invoiceId,
    required this.allocatedAmount,
    DateTime? allocationDate,
    this.notes,
  }) : allocationDate = allocationDate ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'payment_id': paymentId,
      'invoice_id': invoiceId,
      'allocated_amount': allocatedAmount,
      'allocation_date': allocationDate.toIso8601String(),
      'notes': notes,
    };
  }

  factory PaymentAllocation.fromMap(Map<String, dynamic> map) {
    return PaymentAllocation(
      id: map['id']?.toInt(),
      paymentId: map['payment_id']?.toInt() ?? 0,
      invoiceId: map['invoice_id']?.toInt() ?? 0,
      allocatedAmount: map['allocated_amount']?.toDouble() ?? 0.0,
      allocationDate: DateTime.parse(
        map['allocation_date'] ?? DateTime.now().toIso8601String(),
      ),
      notes: map['notes'],
    );
  }

  PaymentAllocation copyWith({
    int? id,
    int? paymentId,
    int? invoiceId,
    double? allocatedAmount,
    DateTime? allocationDate,
    String? notes,
  }) {
    return PaymentAllocation(
      id: id ?? this.id,
      paymentId: paymentId ?? this.paymentId,
      invoiceId: invoiceId ?? this.invoiceId,
      allocatedAmount: allocatedAmount ?? this.allocatedAmount,
      allocationDate: allocationDate ?? this.allocationDate,
      notes: notes ?? this.notes,
    );
  }
}

/// نموذج تتبع الدفعات الجزئية
class PartialPaymentTracker {
  final int invoiceId;
  final String invoiceNumber;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final List<PaymentAllocation> allocations;
  final List<Payment> payments;
  final DateTime? lastPaymentDate;
  final int paymentCount;

  PartialPaymentTracker({
    required this.invoiceId,
    required this.invoiceNumber,
    required this.totalAmount,
    required this.paidAmount,
    required this.allocations,
    required this.payments,
  }) : remainingAmount = totalAmount - paidAmount,
       lastPaymentDate = payments.isNotEmpty
           ? payments
                 .map((p) => p.paymentDate)
                 .reduce((a, b) => a.isAfter(b) ? a : b)
           : null,
       paymentCount = payments.length;

  /// نسبة الدفع المكتملة
  double get completionPercentage =>
      totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

  /// هل الفاتورة مدفوعة بالكامل
  bool get isFullyPaid => remainingAmount <= 0.01;

  /// هل توجد دفعات جزئية
  bool get hasPartialPayments => paidAmount > 0.01 && !isFullyPaid;

  /// عدد الأيام منذ آخر دفعة
  int? get daysSinceLastPayment {
    if (lastPaymentDate == null) return null;
    return DateTime.now().difference(lastPaymentDate!).inDays;
  }
}

/// خدمة تتبع الدفعات المتقدمة
class PaymentTrackingService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final PaymentService _paymentService = PaymentService();
  final InvoiceService _invoiceService = InvoiceService();
  final JournalEntryService _journalEntryService = JournalEntryService();

  /// ربط دفعة بفاتورة أو عدة فواتير
  Future<void> allocatePayment({
    required int paymentId,
    required List<PaymentAllocation> allocations,
  }) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // التحقق من صحة الدفعة
      final payment = await _paymentService.getPaymentById(paymentId);
      if (payment == null) {
        throw Exception('الدفعة غير موجودة');
      }

      // التحقق من إجمالي المبالغ المخصصة
      final totalAllocated = allocations.fold(
        0.0,
        (sum, allocation) => sum + allocation.allocatedAmount,
      );
      if (totalAllocated > payment.amount + 0.01) {
        throw Exception('إجمالي المبالغ المخصصة يتجاوز مبلغ الدفعة');
      }

      // حذف التخصيصات السابقة
      await txn.delete(
        'payment_allocations',
        where: 'payment_id = ?',
        whereArgs: [paymentId],
      );

      // إدراج التخصيصات الجديدة
      for (final allocation in allocations) {
        await txn.insert('payment_allocations', allocation.toMap());

        // تحديث حالة الفاتورة
        await _updateInvoicePaymentStatus(allocation.invoiceId, txn);

        // إنشاء قيد محاسبي لتخصيص الدفعة
        await _createPaymentAllocationJournalEntry(payment, allocation, txn);
      }
    });
  }

  /// الحصول على تفاصيل ربط الدفعة
  Future<List<PaymentAllocation>> getPaymentAllocations(int paymentId) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'payment_allocations',
      where: 'payment_id = ?',
      whereArgs: [paymentId],
      orderBy: 'allocation_date DESC',
    );

    return maps.map((map) => PaymentAllocation.fromMap(map)).toList();
  }

  /// الحصول على جميع الدفعات المرتبطة بفاتورة
  Future<List<PaymentAllocation>> getInvoiceAllocations(int invoiceId) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'payment_allocations',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'allocation_date DESC',
    );

    return maps.map((map) => PaymentAllocation.fromMap(map)).toList();
  }

  /// تتبع الدفعات الجزئية لفاتورة
  Future<PartialPaymentTracker> trackPartialPayments(int invoiceId) async {
    // الحصول على الفاتورة
    final invoice = await _invoiceService.getInvoiceById(invoiceId);
    if (invoice == null) {
      throw Exception('الفاتورة غير موجودة');
    }

    // الحصول على التخصيصات
    final allocations = await getInvoiceAllocations(invoiceId);

    // الحصول على الدفعات
    final paymentIds = allocations.map((a) => a.paymentId).toSet();
    final payments = <Payment>[];
    for (final paymentId in paymentIds) {
      final payment = await _paymentService.getPaymentById(paymentId);
      if (payment != null && payment.status == PaymentStatus.confirmed) {
        payments.add(payment);
      }
    }

    // حساب المبلغ المدفوع
    final paidAmount = allocations
        .where(
          (a) => payments.any(
            (p) => p.id == a.paymentId && p.status == PaymentStatus.confirmed,
          ),
        )
        .fold(0.0, (sum, allocation) => sum + allocation.allocatedAmount);

    return PartialPaymentTracker(
      invoiceId: invoiceId,
      invoiceNumber: invoice.invoiceNumber,
      totalAmount: invoice.totalAmount,
      paidAmount: paidAmount,
      allocations: allocations,
      payments: payments,
    );
  }

  /// الحصول على جميع الفواتير ذات الدفعات الجزئية
  Future<List<PartialPaymentTracker>> getPartiallyPaidInvoices({
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على الفواتير المدفوعة جزئياً
    final maps = await db.rawQuery(
      '''
      SELECT DISTINCT i.id, i.invoice_number, i.total_amount
      FROM invoices i
      INNER JOIN payment_allocations pa ON i.id = pa.invoice_id
      INNER JOIN payments p ON pa.payment_id = p.id
      WHERE p.status = ? AND i.status IN (?, ?)
      GROUP BY i.id, i.invoice_number, i.total_amount
      HAVING SUM(pa.allocated_amount) > 0 AND SUM(pa.allocated_amount) < i.total_amount
      ORDER BY i.invoice_date DESC
      LIMIT ? OFFSET ?
    ''',
      [
        PaymentStatus.confirmed.code,
        InvoiceStatus.partiallyPaid.code,
        InvoiceStatus.confirmed.code,
        limit ?? 50,
        offset ?? 0,
      ],
    );

    final trackers = <PartialPaymentTracker>[];
    for (final map in maps) {
      final tracker = await trackPartialPayments(map['id'] as int);
      trackers.add(tracker);
    }

    return trackers;
  }

  /// تطبيق دفعة على عدة فواتير تلقائياً
  Future<void> autoAllocatePayment({
    required int paymentId,
    required List<int> invoiceIds,
    String allocationMethod =
        'proportional', // proportional, oldest_first, largest_first
  }) async {
    final payment = await _paymentService.getPaymentById(paymentId);
    if (payment == null) {
      throw Exception('الدفعة غير موجودة');
    }

    // الحصول على الفواتير
    final invoices = <Invoice>[];
    for (final invoiceId in invoiceIds) {
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice != null) {
        invoices.add(invoice);
      }
    }

    if (invoices.isEmpty) {
      throw Exception('لا توجد فواتير صالحة للتخصيص');
    }

    // حساب التخصيصات حسب الطريقة المختارة
    final allocations = _calculateAllocations(
      payment,
      invoices,
      allocationMethod,
    );

    // تطبيق التخصيصات
    await allocatePayment(paymentId: paymentId, allocations: allocations);
  }

  /// حساب التخصيصات حسب الطريقة المختارة
  List<PaymentAllocation> _calculateAllocations(
    Payment payment,
    List<Invoice> invoices,
    String method,
  ) {
    final allocations = <PaymentAllocation>[];
    double remainingAmount = payment.amount;

    switch (method) {
      case 'proportional':
        // التوزيع النسبي حسب قيمة كل فاتورة
        final totalInvoiceAmount = invoices.fold(
          0.0,
          (sum, inv) => sum + inv.remainingAmount,
        );

        for (final invoice in invoices) {
          if (remainingAmount <= 0.01) break;

          final proportion = invoice.remainingAmount / totalInvoiceAmount;
          final allocatedAmount = (payment.amount * proportion).clamp(
            0.0,
            [
              remainingAmount,
              invoice.remainingAmount,
            ].reduce((a, b) => a < b ? a : b),
          );

          if (allocatedAmount > 0.01) {
            allocations.add(
              PaymentAllocation(
                paymentId: payment.id!,
                invoiceId: invoice.id!,
                allocatedAmount: allocatedAmount,
                notes: 'تخصيص تلقائي - توزيع نسبي',
              ),
            );
            remainingAmount -= allocatedAmount;
          }
        }
        break;

      case 'oldest_first':
        // الأقدم أولاً
        final sortedInvoices = List<Invoice>.from(invoices)
          ..sort((a, b) => a.invoiceDate.compareTo(b.invoiceDate));

        for (final invoice in sortedInvoices) {
          if (remainingAmount <= 0.01) break;

          final allocatedAmount = [
            remainingAmount,
            invoice.remainingAmount,
          ].reduce((a, b) => a < b ? a : b);

          if (allocatedAmount > 0.01) {
            allocations.add(
              PaymentAllocation(
                paymentId: payment.id!,
                invoiceId: invoice.id!,
                allocatedAmount: allocatedAmount,
                notes: 'تخصيص تلقائي - الأقدم أولاً',
              ),
            );
            remainingAmount -= allocatedAmount;
          }
        }
        break;

      case 'largest_first':
        // الأكبر أولاً
        final sortedInvoices = List<Invoice>.from(invoices)
          ..sort((a, b) => b.remainingAmount.compareTo(a.remainingAmount));

        for (final invoice in sortedInvoices) {
          if (remainingAmount <= 0.01) break;

          final allocatedAmount = [
            remainingAmount,
            invoice.remainingAmount,
          ].reduce((a, b) => a < b ? a : b);

          if (allocatedAmount > 0.01) {
            allocations.add(
              PaymentAllocation(
                paymentId: payment.id!,
                invoiceId: invoice.id!,
                allocatedAmount: allocatedAmount,
                notes: 'تخصيص تلقائي - الأكبر أولاً',
              ),
            );
            remainingAmount -= allocatedAmount;
          }
        }
        break;
    }

    return allocations;
  }

  /// تحديث حالة دفع الفاتورة
  Future<void> _updateInvoicePaymentStatus(int invoiceId, dynamic txn) async {
    // حساب إجمالي المبلغ المدفوع للفاتورة
    final result = await txn.rawQuery(
      '''
      SELECT SUM(pa.allocated_amount) as total_paid
      FROM payment_allocations pa
      INNER JOIN payments p ON pa.payment_id = p.id
      WHERE pa.invoice_id = ? AND p.status = ?
    ''',
      [invoiceId, PaymentStatus.confirmed.code],
    );

    final totalPaid = result.first['total_paid'] as double? ?? 0.0;

    // تحديث الفاتورة
    await txn.update(
      'invoices',
      {
        'paid_amount': totalPaid,
        'remaining_amount': 'total_amount - $totalPaid',
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  /// الحصول على إحصائيات الدفعات الجزئية
  Future<Map<String, dynamic>> getPartialPaymentStatistics() async {
    final db = await _databaseHelper.database;

    // عدد الفواتير المدفوعة جزئياً
    final partiallyPaidResult = await db.rawQuery(
      '''
      SELECT COUNT(DISTINCT i.id) as count, SUM(i.remaining_amount) as total_remaining
      FROM invoices i
      WHERE i.status = ?
    ''',
      [InvoiceStatus.partiallyPaid.code],
    );

    // متوسط نسبة الدفع
    final avgPaymentResult = await db.rawQuery(
      '''
      SELECT AVG((i.paid_amount / i.total_amount) * 100) as avg_payment_percentage
      FROM invoices i
      WHERE i.status = ? AND i.total_amount > 0
    ''',
      [InvoiceStatus.partiallyPaid.code],
    );

    // توزيع الدفعات حسب طريقة الدفع
    final methodDistribution = await db.rawQuery(
      '''
      SELECT p.method, COUNT(*) as count, SUM(pa.allocated_amount) as total_amount
      FROM payment_allocations pa
      INNER JOIN payments p ON pa.payment_id = p.id
      WHERE p.status = ?
      GROUP BY p.method
    ''',
      [PaymentStatus.confirmed.code],
    );

    return {
      'partiallyPaidCount': partiallyPaidResult.first['count'] as int? ?? 0,
      'totalRemainingAmount':
          partiallyPaidResult.first['total_remaining'] as double? ?? 0.0,
      'averagePaymentPercentage':
          avgPaymentResult.first['avg_payment_percentage'] as double? ?? 0.0,
      'methodDistribution': methodDistribution
          .map(
            (row) => {
              'method': PaymentMethod.fromCode(row['method'] as String),
              'count': row['count'] as int,
              'totalAmount': row['total_amount'] as double,
            },
          )
          .toList(),
    };
  }

  /// إنشاء قيد محاسبي لتخصيص الدفعة
  Future<void> _createPaymentAllocationJournalEntry(
    Payment payment,
    PaymentAllocation allocation,
    dynamic txn,
  ) async {
    // الحصول على الفاتورة المرتبطة
    final invoice = await _invoiceService.getInvoiceById(allocation.invoiceId);
    if (invoice == null) {
      throw Exception('الفاتورة غير موجودة');
    }

    // تحديد الحسابات المحاسبية
    int cashAccountId = 1; // حساب الصندوق
    int customerAccountId = invoice.customerId ?? 0;
    int supplierAccountId = invoice.supplierId ?? 0;

    // تحديد نوع القيد بناءً على نوع الفاتورة
    List<JournalEntryDetail> details = [];

    if (invoice.type == 'sale' || invoice.type == 'sale_return') {
      // دفعة من عميل
      details = [
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: cashAccountId,
          debitAmount: allocation.allocatedAmount,
          creditAmount: 0.0,
          description: 'استلام دفعة من العميل - ${payment.method.displayName}',
        ),
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: customerAccountId,
          debitAmount: 0.0,
          creditAmount: allocation.allocatedAmount,
          description: 'تسديد جزئي للفاتورة رقم ${invoice.invoiceNumber}',
        ),
      ];
    } else if (invoice.type == 'purchase' ||
        invoice.type == 'purchase_return') {
      // دفعة لمورد
      details = [
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: supplierAccountId,
          debitAmount: allocation.allocatedAmount,
          creditAmount: 0.0,
          description: 'تسديد جزئي للفاتورة رقم ${invoice.invoiceNumber}',
        ),
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: cashAccountId,
          debitAmount: 0.0,
          creditAmount: allocation.allocatedAmount,
          description: 'دفعة للمورد - ${payment.method.displayName}',
        ),
      ];
    }

    // إنشاء رقم القيد
    final entryNumber = await _journalEntryService.generateEntryNumber();

    // إنشاء كائن القيد المحاسبي
    final journalEntry = JournalEntry(
      entryNumber: entryNumber,
      entryDate: payment.paymentDate,
      description: 'دفعة للفاتورة رقم ${invoice.invoiceNumber}',
      type: 'payment',
      totalDebit: allocation.allocatedAmount,
      totalCredit: allocation.allocatedAmount,
      currencyId: invoice.currencyId,
      referenceType: 'payment_allocation',
      referenceId: allocation.id,
      details: details,
    );

    // إنشاء القيد المحاسبي باستخدام transaction موجود
    await _journalEntryService.insertJournalEntryWithTransaction(
      journalEntry,
      txn,
    );
  }

  /// البحث في الدفعات المرتبطة
  Future<List<Map<String, dynamic>>> searchLinkedPayments({
    String? searchTerm,
    PaymentMethod? method,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'p.status = ?';
    List<dynamic> whereArgs = [PaymentStatus.confirmed.code];

    if (searchTerm != null && searchTerm.isNotEmpty) {
      whereClause += ' AND (i.invoice_number LIKE ? OR p.reference LIKE ?)';
      whereArgs.addAll(['%$searchTerm%', '%$searchTerm%']);
    }

    if (method != null) {
      whereClause += ' AND p.method = ?';
      whereArgs.add(method.code);
    }

    if (fromDate != null) {
      whereClause += ' AND p.payment_date >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      whereClause += ' AND p.payment_date <= ?';
      whereArgs.add(toDate.toIso8601String());
    }

    final result = await db.rawQuery(
      '''
      SELECT 
        p.id as payment_id,
        p.amount as payment_amount,
        p.method,
        p.payment_date,
        p.reference,
        i.id as invoice_id,
        i.invoice_number,
        i.total_amount as invoice_total,
        pa.allocated_amount,
        pa.allocation_date
      FROM payments p
      INNER JOIN payment_allocations pa ON p.id = pa.payment_id
      INNER JOIN invoices i ON pa.invoice_id = i.id
      WHERE $whereClause
      ORDER BY p.payment_date DESC, pa.allocation_date DESC
      LIMIT ? OFFSET ?
    ''',
      [...whereArgs, limit ?? 50, offset ?? 0],
    );

    return result;
  }
}
