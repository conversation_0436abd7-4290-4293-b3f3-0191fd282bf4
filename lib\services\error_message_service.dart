/// خدمة رسائل الخطأ المركزية
/// تحتوي على جميع رسائل الخطأ باللغة العربية مع تفاصيل واضحة
class ErrorMessageService {
  
  // ===============================
  // رسائل الخطأ العامة
  // ===============================
  
  static const String requiredField = 'هذا الحقل مطلوب';
  static const String invalidFormat = 'التنسيق غير صحيح';
  static const String duplicateValue = 'هذه القيمة موجودة مسبقاً';
  static const String notFound = 'العنصر غير موجود';
  static const String operationFailed = 'فشلت العملية';
  static const String accessDenied = 'ليس لديك صلاحية للوصول';
  static const String networkError = 'خطأ في الاتصال';
  static const String unknownError = 'حدث خطأ غير متوقع';
  
  // ===============================
  // رسائل التحقق من البيانات
  // ===============================
  
  /// رسائل الحقول المطلوبة
  static String getRequiredFieldMessage(String fieldName) {
    return '$fieldName مطلوب';
  }
  
  /// رسائل طول النص
  static String getLengthErrorMessage(String fieldName, {int? minLength, int? maxLength}) {
    if (minLength != null && maxLength != null) {
      return '$fieldName يجب أن يكون بين $minLength و $maxLength حرف';
    } else if (minLength != null) {
      return '$fieldName يجب أن يكون على الأقل $minLength أحرف';
    } else if (maxLength != null) {
      return '$fieldName يجب أن لا يزيد عن $maxLength حرف';
    }
    return '$fieldName طوله غير صحيح';
  }
  
  /// رسائل التنسيق
  static String getFormatErrorMessage(String fieldName, String expectedFormat) {
    return '$fieldName تنسيقه غير صحيح. التنسيق المطلوب: $expectedFormat';
  }
  
  /// رسائل النطاق
  static String getRangeErrorMessage(String fieldName, {dynamic min, dynamic max}) {
    if (min != null && max != null) {
      return '$fieldName يجب أن يكون بين $min و $max';
    } else if (min != null) {
      return '$fieldName يجب أن يكون أكبر من أو يساوي $min';
    } else if (max != null) {
      return '$fieldName يجب أن يكون أقل من أو يساوي $max';
    }
    return '$fieldName قيمته خارج النطاق المسموح';
  }
  
  /// رسائل القيم المكررة
  static String getDuplicateMessage(String fieldName, dynamic value) {
    return '$fieldName "$value" موجود مسبقاً';
  }
  
  // ===============================
  // رسائل الأكواد
  // ===============================
  
  static const Map<String, String> codeFormatMessages = {
    'account': 'كود الحساب يجب أن يبدأ بحرف ويتبعه 3-6 أرقام (مثال: A1001)',
    'customer': 'كود العميل يجب أن يبدأ بـ C ويتبعه 4-6 أرقام (مثال: C0001)',
    'supplier': 'كود المورد يجب أن يبدأ بـ S ويتبعه 4-6 أرقام (مثال: S0001)',
    'item': 'كود الصنف يجب أن يبدأ بـ I ويتبعه 4-6 أرقام (مثال: I0001)',
    'journal': 'رقم القيد يجب أن يحتوي على أرقام فقط أو يبدأ بحروف',
  };
  
  static String getCodeFormatMessage(String entityType) {
    return codeFormatMessages[entityType] ?? 'تنسيق الكود غير صحيح';
  }
  
  // ===============================
  // رسائل بيانات الاتصال
  // ===============================
  
  static const String phoneFormatMessage = 'رقم الهاتف غير صحيح (مثال: ********** أو +************)';
  static const String emailFormatMessage = 'البريد الإلكتروني غير صحيح';
  
  // ===============================
  // رسائل المبالغ والكميات
  // ===============================
  
  static const String amountNegativeMessage = 'المبلغ لا يمكن أن يكون سالباً';
  static const String amountZeroMessage = 'المبلغ يجب أن يكون أكبر من صفر';
  static const String amountTooLargeMessage = 'المبلغ كبير جداً';
  static const String quantityNegativeMessage = 'الكمية لا يمكن أن تكون سالبة';
  static const String quantityZeroMessage = 'الكمية يجب أن تكون أكبر من صفر';
  static const String quantityTooLargeMessage = 'الكمية كبيرة جداً';
  
  // ===============================
  // رسائل التواريخ
  // ===============================
  
  static String getDateRangeMessage({DateTime? minDate, DateTime? maxDate}) {
    if (minDate != null && maxDate != null) {
      return 'التاريخ يجب أن يكون بين ${_formatDate(minDate)} و ${_formatDate(maxDate)}';
    } else if (minDate != null) {
      return 'التاريخ لا يمكن أن يكون قبل ${_formatDate(minDate)}';
    } else if (maxDate != null) {
      return 'التاريخ لا يمكن أن يكون بعد ${_formatDate(maxDate)}';
    }
    return 'التاريخ غير صحيح';
  }
  
  // ===============================
  // رسائل القيود المحاسبية
  // ===============================
  
  static const String journalEntryMinLinesMessage = 'القيد يجب أن يحتوي على سطرين على الأقل';
  static const String journalEntryBothAmountsMessage = 'لا يمكن أن يكون للسطر مبلغ مدين ودائن معاً';
  static const String journalEntryNoAmountMessage = 'كل سطر يجب أن يحتوي على مبلغ مدين أو دائن';
  
  static String getJournalEntryUnbalancedMessage(double debit, double credit) {
    return 'القيد غير متوازن - مجموع المدين (${debit.toStringAsFixed(2)}) '
           'يجب أن يساوي مجموع الدائن (${credit.toStringAsFixed(2)})';
  }
  
  // ===============================
  // رسائل الفواتير
  // ===============================
  
  static const String invoiceNoItemsMessage = 'الفاتورة يجب أن تحتوي على صنف واحد على الأقل';
  static const String invoiceNoCustomerMessage = 'يجب اختيار عميل للفاتورة';
  
  static String getInvoiceItemErrorMessage(int lineNumber, String error) {
    return 'السطر $lineNumber: $error';
  }
  
  // ===============================
  // رسائل القواعد التجارية
  // ===============================
  
  static String getRelatedDataMessage(String entityType, String relatedType) {
    return 'لا يمكن حذف $entityType لوجود $relatedType مرتبطة به';
  }
  
  static String getAlreadyPostedMessage(String entityType) {
    return 'لا يمكن تعديل $entityType بعد ترحيله';
  }
  
  static String getInsufficientStockMessage(String itemName, double available, double required) {
    return 'المخزون غير كافي للصنف "$itemName" - المتاح: $available, المطلوب: $required';
  }
  
  // ===============================
  // رسائل الأسعار
  // ===============================
  
  static const String sellingPriceLowerThanCostMessage = 'سعر البيع لا يمكن أن يكون أقل من سعر التكلفة';
  
  // ===============================
  // رسائل النجاح
  // ===============================
  
  static const String saveSuccessMessage = 'تم الحفظ بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  static const String operationSuccessMessage = 'تمت العملية بنجاح';
  
  // ===============================
  // رسائل التأكيد
  // ===============================
  
  static const String deleteConfirmationMessage = 'هل أنت متأكد من الحذف؟';
  static const String saveConfirmationMessage = 'هل تريد حفظ التغييرات؟';
  static const String cancelConfirmationMessage = 'هل تريد إلغاء العملية؟ ستفقد التغييرات غير المحفوظة.';
  
  static String getDeleteConfirmationMessage(String entityType, String entityName) {
    return 'هل أنت متأكد من حذف $entityType "$entityName"؟';
  }
  
  // ===============================
  // رسائل التحميل والمعالجة
  // ===============================
  
  static const String loadingMessage = 'جاري التحميل...';
  static const String processingMessage = 'جاري المعالجة...';
  static const String savingMessage = 'جاري الحفظ...';
  static const String deletingMessage = 'جاري الحذف...';
  
  // ===============================
  // دوال مساعدة
  // ===============================
  
  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
           '${date.month.toString().padLeft(2, '0')}/'
           '${date.year}';
  }
  
  /// تنسيق رسالة خطأ مع تفاصيل إضافية
  static String formatErrorWithDetails(String mainMessage, {String? details, String? suggestion}) {
    String message = mainMessage;
    
    if (details != null) {
      message += '\n\nالتفاصيل: $details';
    }
    
    if (suggestion != null) {
      message += '\n\nاقتراح: $suggestion';
    }
    
    return message;
  }
  
  /// الحصول على رسالة خطأ حسب نوع الاستثناء
  static String getErrorMessage(dynamic error) {
    if (error is String) {
      return error;
    } else if (error.toString().contains('UNIQUE constraint failed')) {
      return 'هذه القيمة موجودة مسبقاً';
    } else if (error.toString().contains('FOREIGN KEY constraint failed')) {
      return 'لا يمكن تنفيذ العملية بسبب وجود بيانات مرتبطة';
    } else if (error.toString().contains('NOT NULL constraint failed')) {
      return 'يوجد حقول مطلوبة لم يتم ملؤها';
    } else {
      return unknownError;
    }
  }
}
