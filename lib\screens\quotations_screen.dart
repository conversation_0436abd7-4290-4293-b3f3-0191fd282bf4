/// شاشة عروض الأسعار
/// تعرض جميع عروض الأسعار مع إمكانية إدارتها وتحويلها إلى فواتير
library;

import 'package:flutter/material.dart';
import '../models/quotation.dart';
import '../services/quotation_service.dart';
import '../services/logging_service.dart';
import '../constants/app_colors.dart';

import '../widgets/loading_widget.dart';
import '../widgets/quotation_card.dart';

import 'add_quotation_screen.dart';
import 'quotation_details_screen.dart';

class QuotationsScreen extends StatefulWidget {
  const QuotationsScreen({super.key});

  @override
  State<QuotationsScreen> createState() => _QuotationsScreenState();
}

class _QuotationsScreenState extends State<QuotationsScreen> {
  final QuotationService _quotationService = QuotationService();
  List<Quotation> _quotations = [];
  List<Quotation> _filteredQuotations = [];
  bool _isLoading = true;
  String _searchQuery = '';
  QuotationStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadQuotations();
    _updateExpiredQuotations();
  }

  Future<void> _loadQuotations() async {
    try {
      setState(() => _isLoading = true);

      final quotations = await _quotationService.getAllQuotations();

      setState(() {
        _quotations = quotations;
        _filteredQuotations = quotations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل عروض الأسعار: $e');

      LoggingService.error(
        'خطأ في تحميل عروض الأسعار',
        category: 'QuotationsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  Future<void> _updateExpiredQuotations() async {
    try {
      await _quotationService.updateExpiredQuotations();
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث عروض الأسعار المنتهية الصلاحية',
        category: 'QuotationsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  void _filterQuotations() {
    setState(() {
      _filteredQuotations = _quotations.where((quotation) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            quotation.quotationNumber.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            (quotation.customerName?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ??
                false) ||
            (quotation.supplierName?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ??
                false);

        final matchesStatus =
            _selectedStatus == null || quotation.status == _selectedStatus;

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() => _searchQuery = query);
    _filterQuotations();
  }

  void _onStatusFilterChanged(QuotationStatus? status) {
    setState(() => _selectedStatus = status);
    _filterQuotations();
  }

  Future<void> _convertToInvoice(Quotation quotation) async {
    if (!quotation.canBeConverted) {
      _showErrorSnackBar(
        'لا يمكن تحويل عرض السعر. يجب أن يكون مقبولاً وغير محول مسبقاً',
      );
      return;
    }

    final confirmed = await _showConfirmDialog(
      'تحويل إلى فاتورة',
      'هل تريد تحويل عرض السعر ${quotation.quotationNumber} إلى فاتورة؟',
    );

    if (!confirmed) return;

    try {
      final invoiceId = await _quotationService.convertQuotationToInvoice(
        quotation.id!,
      );

      _showSuccessSnackBar('تم تحويل عرض السعر إلى فاتورة بنجاح');
      _loadQuotations(); // إعادة تحميل القائمة

      LoggingService.info(
        'تم تحويل عرض السعر إلى فاتورة',
        category: 'QuotationsScreen',
        data: {
          'quotation_id': quotation.id,
          'quotation_number': quotation.quotationNumber,
          'invoice_id': invoiceId,
        },
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في تحويل عرض السعر: $e');

      LoggingService.error(
        'خطأ في تحويل عرض السعر إلى فاتورة',
        category: 'QuotationsScreen',
        data: {'quotation_id': quotation.id, 'error': e.toString()},
      );
    }
  }

  Future<void> _updateQuotationStatus(
    Quotation quotation,
    QuotationStatus newStatus,
  ) async {
    try {
      await _quotationService.updateQuotationStatus(quotation.id!, newStatus);
      _showSuccessSnackBar('تم تحديث حالة عرض السعر بنجاح');
      _loadQuotations();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث حالة عرض السعر: $e');
    }
  }

  Future<void> _deleteQuotation(Quotation quotation) async {
    final confirmed = await _showConfirmDialog(
      'حذف عرض السعر',
      'هل تريد حذف عرض السعر ${quotation.quotationNumber}؟\nلا يمكن التراجع عن هذا الإجراء.',
    );

    if (!confirmed) return;

    try {
      await _quotationService.deleteQuotation(quotation.id!);
      _showSuccessSnackBar('تم حذف عرض السعر بنجاح');
      _loadQuotations();
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف عرض السعر: $e');
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('عروض الأسعار'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadQuotations,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _filteredQuotations.isEmpty
                ? _buildEmptyState()
                : _buildQuotationsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddQuotationScreen()),
          );
          if (result == true) {
            _loadQuotations();
          }
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في عروض الأسعار...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('الحالة: '),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButton<QuotationStatus?>(
                  value: _selectedStatus,
                  isExpanded: true,
                  hint: const Text('جميع الحالات'),
                  items: [
                    const DropdownMenuItem<QuotationStatus?>(
                      value: null,
                      child: Text('جميع الحالات'),
                    ),
                    ...QuotationStatus.values.map((status) {
                      return DropdownMenuItem<QuotationStatus?>(
                        value: status,
                        child: Text(status.displayName),
                      );
                    }),
                  ],
                  onChanged: _onStatusFilterChanged,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.description_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد عروض أسعار',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة عرض سعر جديد',
            style: TextStyle(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildQuotationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredQuotations.length,
      itemBuilder: (context, index) {
        final quotation = _filteredQuotations[index];
        return QuotationCard(
          quotation: quotation,
          onTap: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    QuotationDetailsScreen(quotation: quotation),
              ),
            );
            if (result == true) {
              _loadQuotations();
            }
          },
          onConvertToInvoice: () => _convertToInvoice(quotation),
          onUpdateStatus: (status) => _updateQuotationStatus(quotation, status),
          onDelete: () => _deleteQuotation(quotation),
        );
      },
    );
  }
}
