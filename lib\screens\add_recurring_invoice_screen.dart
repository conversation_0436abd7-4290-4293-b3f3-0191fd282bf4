/// شاشة إضافة فاتورة متكررة جديدة
/// تسمح بإنشاء فاتورة متكررة من فاتورة موجودة أو من البداية
library;

import 'package:flutter/material.dart';
import '../models/recurring_invoice.dart';
import '../models/invoice.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../services/recurring_invoice_service.dart';
import '../services/invoice_service.dart';
import '../services/customer_service.dart';
import '../services/supplier_service.dart';
import '../services/logging_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';

class AddRecurringInvoiceScreen extends StatefulWidget {
  final RecurringInvoice? recurringInvoice; // للتعديل

  const AddRecurringInvoiceScreen({super.key, this.recurringInvoice});

  @override
  State<AddRecurringInvoiceScreen> createState() =>
      _AddRecurringInvoiceScreenState();
}

class _AddRecurringInvoiceScreenState extends State<AddRecurringInvoiceScreen> {
  final RecurringInvoiceService _recurringInvoiceService =
      RecurringInvoiceService();
  final InvoiceService _invoiceService = InvoiceService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();

  final _formKey = GlobalKey<FormState>();
  final _templateNameController = TextEditingController();
  final _notesController = TextEditingController();

  List<Invoice> _invoices = [];
  List<Customer> _customers = [];
  List<Supplier> _suppliers = [];

  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  RecurrenceFrequency _frequency = RecurrenceFrequency.monthly;
  Invoice? _selectedInvoice;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    if (widget.recurringInvoice != null) {
      _loadRecurringInvoiceData();
    }
  }

  @override
  void dispose() {
    _templateNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      final invoices = await _invoiceService.getAllInvoices();
      final customers = await _customerService.getActiveCustomers();
      final suppliers = await _supplierService.getActiveSuppliers();

      setState(() {
        _invoices = invoices;
        _customers = customers;
        _suppliers = suppliers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  void _loadRecurringInvoiceData() {
    final recurringInvoice = widget.recurringInvoice!;
    _templateNameController.text = recurringInvoice.templateName;
    _startDate = recurringInvoice.startDate;
    _endDate = recurringInvoice.endDate;
    _frequency = recurringInvoice.frequency;
    _notesController.text = recurringInvoice.notes ?? '';

    // محاولة العثور على الفاتورة النموذجية
    final template = recurringInvoice.getParsedInvoiceTemplate();
    if (template != null) {
      _selectedInvoice = template;
    }
  }

  Future<void> _saveRecurringInvoice() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedInvoice == null) {
      _showErrorSnackBar('يجب اختيار فاتورة نموذجية');
      return;
    }

    setState(() => _isSaving = true);

    try {
      final recurringInvoice = RecurringInvoice.fromInvoice(
        templateName: _templateNameController.text.trim(),
        invoice: _selectedInvoice!,
        frequency: _frequency,
        startDate: _startDate,
        endDate: _endDate,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      if (widget.recurringInvoice == null) {
        await _recurringInvoiceService.insertRecurringInvoice(recurringInvoice);
        _showSuccessSnackBar('تم إنشاء الفاتورة المتكررة بنجاح');
      } else {
        final updatedRecurringInvoice = recurringInvoice.copyWith(
          id: widget.recurringInvoice!.id,
          createdAt: widget.recurringInvoice!.createdAt,
        );
        await _recurringInvoiceService.updateRecurringInvoice(
          updatedRecurringInvoice,
        );
        _showSuccessSnackBar('تم تحديث الفاتورة المتكررة بنجاح');
      }

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الفاتورة المتكررة: $e');
      LoggingService.error(
        'خطأ في حفظ الفاتورة المتكررة',
        category: 'AddRecurringInvoiceScreen',
        data: {'error': e.toString()},
      );
    } finally {
      setState(() => _isSaving = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.recurringInvoice == null
              ? 'إضافة فاتورة متكررة'
              : 'تعديل فاتورة متكررة',
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveRecurringInvoice,
              child: const Text('حفظ', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfo(),
                    const SizedBox(height: 24),
                    _buildInvoiceSelection(),
                    const SizedBox(height: 24),
                    _buildRecurrenceSettings(),
                    const SizedBox(height: 24),
                    _buildNotesSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _templateNameController,
              decoration: const InputDecoration(
                labelText: 'اسم النموذج',
                border: OutlineInputBorder(),
                hintText: 'مثال: فاتورة إيجار شهرية',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم النموذج مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الفاتورة النموذجية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Invoice>(
              value: _selectedInvoice,
              decoration: const InputDecoration(
                labelText: 'اختر الفاتورة النموذجية',
                border: OutlineInputBorder(),
              ),
              items: _invoices.map((invoice) {
                final clientName = _getClientName(invoice);
                return DropdownMenuItem<Invoice>(
                  value: invoice,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${invoice.invoiceNumber} - $clientName',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Text(
                        '${invoice.totalAmount.toStringAsFixed(2)} ل.س',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (invoice) {
                setState(() => _selectedInvoice = invoice);
              },
              validator: (value) {
                if (value == null) {
                  return 'يجب اختيار فاتورة نموذجية';
                }
                return null;
              },
            ),
            if (_selectedInvoice != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معاينة الفاتورة النموذجية:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.blue[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('رقم الفاتورة: ${_selectedInvoice!.invoiceNumber}'),
                    Text('العميل/المورد: ${_getClientName(_selectedInvoice!)}'),
                    Text(
                      'المبلغ الإجمالي: ${_selectedInvoice!.totalAmount.toStringAsFixed(2)} ل.س',
                    ),
                    Text('عدد الأصناف: ${_selectedInvoice!.items.length}'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecurrenceSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات التكرار',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<RecurrenceFrequency>(
              value: _frequency,
              decoration: const InputDecoration(
                labelText: 'تكرار الفاتورة',
                border: OutlineInputBorder(),
              ),
              items: RecurrenceFrequency.values.map((frequency) {
                return DropdownMenuItem<RecurrenceFrequency>(
                  value: frequency,
                  child: Text(frequency.displayName),
                );
              }).toList(),
              onChanged: (frequency) {
                setState(() => _frequency = frequency!);
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _startDate,
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2030),
                      );
                      if (date != null) {
                        setState(() => _startDate = date);
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ البداية',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate:
                            _endDate ??
                            DateTime.now().add(const Duration(days: 365)),
                        firstDate: _startDate,
                        lastDate: DateTime(2030),
                      );
                      setState(() => _endDate = date);
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الانتهاء (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        _endDate != null
                            ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                            : 'بدون انتهاء',
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معاينة التكرار:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('التكرار: ${_frequency.displayName}'),
                  Text('تاريخ البداية: ${_formatDate(_startDate)}'),
                  if (_endDate != null)
                    Text('تاريخ الانتهاء: ${_formatDate(_endDate!)}')
                  else
                    const Text('بدون تاريخ انتهاء'),
                  Text(
                    'التاريخ التالي للإنشاء: ${_formatDate(_frequency.getNextDate(_startDate))}',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                border: OutlineInputBorder(),
                hintText: 'أي ملاحظات حول هذه الفاتورة المتكررة...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  String _getClientName(Invoice invoice) {
    if (invoice.customerId != null) {
      final customer = _customers.firstWhere(
        (c) => c.id == invoice.customerId,
        orElse: () => Customer(
          name: 'عميل غير معروف',
          phone: '',
          email: '',
          code: 'UNKNOWN',
          currencyId: 1,
        ),
      );
      return customer.name;
    } else if (invoice.supplierId != null) {
      final supplier = _suppliers.firstWhere(
        (s) => s.id == invoice.supplierId,
        orElse: () => Supplier(
          name: 'مورد غير معروف',
          phone: '',
          email: '',
          code: 'UNKNOWN',
          currencyId: 1,
        ),
      );
      return supplier.name;
    }
    return 'غير محدد';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
