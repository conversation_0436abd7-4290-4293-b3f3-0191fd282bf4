import 'package:flutter/material.dart';
import '../constants/app_animations.dart';
import '../constants/app_colors.dart';

/// مجموعة من المكونات المتحركة المخصصة للتطبيق
class AnimatedWidgets {
  /// بطاقة متحركة مع تأثيرات بصرية
  static Widget animatedCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
    Duration animationDuration = AppAnimations.normal,
  }) {
    return AnimatedContainer(
      duration: animationDuration,
      curve: AppAnimations.fastOutSlowIn,
      margin: margin ?? const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: elevation ?? 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16.0),
            child: child,
          ),
        ),
      ),
    );
  }

  /// زر متحرك مع تأثيرات
  static Widget animatedButton({
    required String text,
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    bool isLoading = false,
    bool isEnabled = true,
    Duration animationDuration = AppAnimations.fast,
  }) {
    return AnimatedContainer(
      duration: animationDuration,
      curve: AppAnimations.easeInOut,
      child: ElevatedButton.icon(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        icon: isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    textColor ?? AppColors.textOnPrimary,
                  ),
                ),
              )
            : Icon(icon ?? Icons.arrow_forward),
        label: Text(text),
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.primary,
          foregroundColor: textColor ?? AppColors.textOnPrimary,
        ),
      ),
    );
  }

  /// قائمة متحركة
  static Widget animatedList({
    required List<Widget> children,
    Duration staggerDelay = const Duration(milliseconds: 100),
    Axis scrollDirection = Axis.vertical,
    EdgeInsetsGeometry? padding,
  }) {
    return ListView.builder(
      scrollDirection: scrollDirection,
      padding: padding,
      itemCount: children.length,
      itemBuilder: (context, index) {
        return TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: AppAnimations.normal + (staggerDelay * index),
          curve: AppAnimations.fastOutSlowIn,
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 50 * (1 - value)),
              child: Opacity(opacity: value, child: children[index]),
            );
          },
        );
      },
    );
  }

  /// مؤشر تحميل متحرك
  static Widget animatedLoadingIndicator({
    String? message,
    Color? color,
    double size = 48.0,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primary,
            ),
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  /// شريط تقدم متحرك
  static Widget animatedProgressBar({
    required double progress,
    Color? backgroundColor,
    Color? progressColor,
    double height = 8.0,
    BorderRadius? borderRadius,
    Duration animationDuration = AppAnimations.normal,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surfaceVariant,
        borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
      ),
      child: AnimatedContainer(
        duration: animationDuration,
        curve: AppAnimations.easeOut,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
        ),
        child: FractionallySizedBox(
          alignment: Alignment.centerLeft,
          widthFactor: progress.clamp(0.0, 1.0),
          child: Container(
            decoration: BoxDecoration(
              color: progressColor ?? AppColors.primary,
              borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
            ),
          ),
        ),
      ),
    );
  }

  /// رسالة تنبيه متحركة
  static Widget animatedAlert({
    required String message,
    AlertType type = AlertType.info,
    VoidCallback? onDismiss,
    Duration displayDuration = const Duration(seconds: 4),
  }) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (type) {
      case AlertType.success:
        backgroundColor = AppColors.successContainer;
        textColor = AppColors.success;
        icon = Icons.check_circle;
        break;
      case AlertType.warning:
        backgroundColor = AppColors.warningContainer;
        textColor = AppColors.warning;
        icon = Icons.warning;
        break;
      case AlertType.error:
        backgroundColor = AppColors.errorContainer;
        textColor = AppColors.error;
        icon = Icons.error;
        break;
      case AlertType.info:
        backgroundColor = AppColors.infoContainer;
        textColor = AppColors.info;
        icon = Icons.info;
        break;
    }

    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: AppAnimations.normal,
      curve: AppAnimations.elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: textColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(icon, color: textColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    message,
                    style: TextStyle(
                      color: textColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    onPressed: onDismiss,
                    icon: Icon(Icons.close, color: textColor, size: 20),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// عداد متحرك للأرقام
  static Widget animatedCounter({
    required double value,
    String? prefix,
    String? suffix,
    TextStyle? textStyle,
    Duration animationDuration = AppAnimations.slow,
    int decimalPlaces = 0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: value),
      duration: animationDuration,
      curve: AppAnimations.easeOut,
      builder: (context, animatedValue, child) {
        return Text(
          '${prefix ?? ''}${animatedValue.toStringAsFixed(decimalPlaces)}${suffix ?? ''}',
          style:
              textStyle ??
              const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
        );
      },
    );
  }

  /// أيقونة متحركة
  static Widget animatedIcon({
    required IconData icon,
    Color? color,
    double? size,
    Duration animationDuration = AppAnimations.normal,
    bool isActive = false,
  }) {
    return AnimatedContainer(
      duration: animationDuration,
      curve: AppAnimations.elasticOut,
      child: AnimatedScale(
        scale: isActive ? 1.2 : 1.0,
        duration: animationDuration,
        curve: AppAnimations.elasticOut,
        child: Icon(
          icon,
          color:
              color ?? (isActive ? AppColors.primary : AppColors.textSecondary),
          size: size ?? 24,
        ),
      ),
    );
  }
}

/// أنواع التنبيهات
enum AlertType { success, warning, error, info }
