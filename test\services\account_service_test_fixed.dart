import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:smart_ledger/database/database_helper.dart';
import 'package:smart_ledger/services/account_service.dart';
import 'package:smart_ledger/models/account.dart';
import 'package:smart_ledger/constants/app_constants.dart';

void main() {
  late AccountService accountService;
  late DatabaseHelper databaseHelper;

  setUpAll(() {
    // Initialize FFI
    sqfliteFfiInit();
    // Change the default factory for unit testing
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    // Create in-memory database for testing
    databaseHelper = DatabaseHelper();
    accountService = AccountService();

    // Initialize database
    await databaseHelper.database;
  });

  tearDown(() async {
    // Reset database for next test
    // Don't close the database, just clear it
  });

  group('AccountService Tests', () {
    test('should insert account successfully', () async {
      // Arrange
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final account = Account(
        code: 'TEST001_$timestamp',
        name: 'Test Account',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 0.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Act
      final insertedId = await accountService.insertAccount(account);

      // Assert
      expect(insertedId, isNotNull);
      expect(insertedId, greaterThan(0));
    });

    test('should get account by id', () async {
      // Arrange
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final code = 'TEST002_$timestamp';
      final account = Account(
        code: code,
        name: 'Test Account 2',
        type: AppConstants.accountTypeLiability,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 1000.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final insertedId = await accountService.insertAccount(account);

      // Act
      final retrievedAccount = await accountService.getAccountById(insertedId);

      // Assert
      expect(retrievedAccount, isNotNull);
      expect(retrievedAccount!.id, equals(insertedId));
      expect(retrievedAccount.code, equals(code));
      expect(retrievedAccount.name, equals('Test Account 2'));
      expect(retrievedAccount.balance, equals(1000.0));
    });

    test('should get all accounts', () async {
      // Arrange
      final account1 = Account(
        code: 'TEST003',
        name: 'Test Account 3',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 500.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final account2 = Account(
        code: 'TEST004',
        name: 'Test Account 4',
        type: AppConstants.accountTypeExpense,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 200.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await accountService.insertAccount(account1);
      await accountService.insertAccount(account2);

      // Act
      final accounts = await accountService.getAllAccounts();

      // Assert
      expect(accounts.length, greaterThanOrEqualTo(2));
      expect(accounts.any((a) => a.code == 'TEST003'), isTrue);
      expect(accounts.any((a) => a.code == 'TEST004'), isTrue);
    });

    test('should update account successfully', () async {
      // Arrange
      final account = Account(
        code: 'TEST005',
        name: 'Test Account 5',
        type: AppConstants.accountTypeRevenue,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 300.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final insertedId = await accountService.insertAccount(account);
      final updatedAccount = account.copyWith(
        id: insertedId,
        name: 'Updated Test Account 5',
        balance: 600.0,
      );

      // Act
      await accountService.updateAccount(updatedAccount);
      final retrievedAccount = await accountService.getAccountById(insertedId);

      // Assert
      expect(retrievedAccount, isNotNull);
      expect(retrievedAccount!.name, equals('Updated Test Account 5'));
      expect(retrievedAccount.balance, equals(600.0));
    });

    test('should delete account successfully', () async {
      // Arrange
      final account = Account(
        code: 'TEST006',
        name: 'Test Account 6',
        type: AppConstants.accountTypePurchase,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 100.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final insertedId = await accountService.insertAccount(account);

      // Act
      await accountService.deleteAccount(insertedId);
      final retrievedAccount = await accountService.getAccountById(insertedId);

      // Assert
      expect(retrievedAccount, isNull);
    });

    test('should get accounts by type', () async {
      // Arrange
      final assetAccount = Account(
        code: 'ASSET001',
        name: 'Asset Account',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 1000.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final expenseAccount = Account(
        code: 'EXP001',
        name: 'Expense Account',
        type: AppConstants.accountTypeExpense,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 500.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await accountService.insertAccount(assetAccount);
      await accountService.insertAccount(expenseAccount);

      // Act
      final assetAccounts = await accountService.getAccountsByType(
        AppConstants.accountTypeAsset,
      );

      // Assert
      expect(assetAccounts.length, greaterThanOrEqualTo(1));
      expect(
        assetAccounts.every((a) => a.type == AppConstants.accountTypeAsset),
        isTrue,
      );
    });

    test('should generate unique account code', () async {
      // Act
      final code1 = await accountService.generateAccountCode(
        AppConstants.accountTypeAsset,
      );
      final code2 = await accountService.generateAccountCode(
        AppConstants.accountTypeAsset,
      );

      // Assert
      expect(code1, isNotEmpty);
      expect(code2, isNotEmpty);
      expect(code1, isNot(equals(code2)));
    });

    test('should update account balance', () async {
      // Arrange
      final account = Account(
        code: 'BAL001',
        name: 'Balance Test Account',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 1000.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final insertedId = await accountService.insertAccount(account);

      // Act
      await accountService.updateAccountBalance(insertedId, 500.0, true);
      final updatedAccount = await accountService.getAccountById(insertedId);

      // Assert
      expect(updatedAccount, isNotNull);
      expect(
        updatedAccount!.balance,
        equals(1500.0),
      ); // 1000 + 500 (debit increases asset balance)
    });

    test('should get active accounts only', () async {
      // Arrange
      final activeAccount = Account(
        code: 'ACTIVE001',
        name: 'Active Account',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 1000.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final inactiveAccount = Account(
        code: 'INACTIVE001',
        name: 'Inactive Account',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: false,
        balance: 500.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await accountService.insertAccount(activeAccount);
      await accountService.insertAccount(inactiveAccount);

      // Act
      final activeAccounts = await accountService.getActiveAccounts();

      // Assert
      expect(activeAccounts.every((a) => a.isActive), isTrue);
      expect(activeAccounts.any((a) => a.code == 'ACTIVE001'), isTrue);
      expect(activeAccounts.any((a) => a.code == 'INACTIVE001'), isFalse);
    });

    test('should handle account hierarchy correctly', () async {
      // Arrange
      final parentAccount = Account(
        code: 'PARENT001',
        name: 'Parent Account',
        type: AppConstants.accountTypeAsset,
        parentId: null,
        level: 1,
        isActive: true,
        balance: 0.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final parentId = await accountService.insertAccount(parentAccount);

      final childAccount = Account(
        code: 'CHILD001',
        name: 'Child Account',
        type: AppConstants.accountTypeAsset,
        parentId: parentId,
        level: 2,
        isActive: true,
        balance: 1000.0,
        currencyId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Act
      final childId = await accountService.insertAccount(childAccount);
      final retrievedChild = await accountService.getAccountById(childId);

      // Assert
      expect(retrievedChild, isNotNull);
      expect(retrievedChild!.parentId, equals(parentId));
      expect(retrievedChild.level, equals(2));
    });
  });
}
