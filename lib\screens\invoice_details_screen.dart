/// شاشة تفاصيل الفاتورة مع إدارة الدفعات والحالات
/// تعرض تفاصيل شاملة للفاتورة مع إمكانية إدارة الدفعات وتغيير الحالات
library;

import 'package:flutter/material.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../models/payment.dart';
import '../models/item.dart';
import '../services/invoice_service.dart';
import '../services/payment_service.dart';
import '../services/invoice_status_service.dart';
import '../services/invoice_print_service.dart';
import '../services/item_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';
import '../widgets/payment_management_dialog.dart';
import '../widgets/invoice_template_selector.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final int invoiceId;

  const InvoiceDetailsScreen({super.key, required this.invoiceId});

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  final InvoiceService _invoiceService = InvoiceService();
  final PaymentService _paymentService = PaymentService();
  final InvoiceStatusService _statusService = InvoiceStatusService();
  final ItemService _itemService = ItemService();

  Invoice? _invoice;
  PaymentSummary? _paymentSummary;
  List<InvoiceStatusHistory> _statusHistory = [];
  bool _isLoading = true;
  final Map<int, Item> _itemsCache = {};

  @override
  void initState() {
    super.initState();
    _loadInvoiceDetails();
  }

  Future<void> _loadInvoiceDetails() async {
    setState(() => _isLoading = true);

    try {
      final invoice = await _invoiceService.getInvoiceById(widget.invoiceId);
      if (invoice == null) {
        throw Exception('الفاتورة غير موجودة');
      }

      final paymentSummary = await _paymentService.getPaymentSummary(
        widget.invoiceId,
      );
      final statusHistory = await _statusService.getInvoiceStatusHistory(
        widget.invoiceId,
      );

      setState(() {
        _invoice = invoice;
        _paymentSummary = paymentSummary;
        _statusHistory = statusHistory;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل تفاصيل الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showPaymentDialog() async {
    if (_invoice == null) return;

    await showDialog(
      context: context,
      builder: (context) => PaymentManagementDialog(
        invoice: _invoice!,
        onPaymentAdded: _loadInvoiceDetails,
      ),
    );
  }

  Future<void> _changeInvoiceStatus(InvoiceStatus newStatus) async {
    if (_invoice == null) return;

    try {
      await _statusService.changeInvoiceStatus(
        invoiceId: _invoice!.id!,
        newStatus: newStatus,
        reason: _getReasonForStatusChange(newStatus),
        notes: 'تغيير من واجهة المستخدم',
      );

      await _loadInvoiceDetails();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تغيير حالة الفاتورة إلى ${newStatus.displayName}',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  StatusChangeReason _getReasonForStatusChange(InvoiceStatus newStatus) {
    switch (newStatus) {
      case InvoiceStatus.confirmed:
        return StatusChangeReason.confirmation;
      case InvoiceStatus.cancelled:
        return StatusChangeReason.cancellation;
      default:
        return StatusChangeReason.correction;
    }
  }

  /// طباعة الفاتورة
  Future<void> _printInvoice() async {
    if (_invoice == null) return;

    try {
      await InvoicePrintService.printInvoiceWithPreview(context, _invoice!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح معاينة الطباعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طباعة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// طباعة الفاتورة بقوالب احترافية
  Future<void> _printWithTemplates() async {
    if (_invoice == null) return;

    await showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              // رأس الحوار
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.palette, color: Colors.white),
                    const SizedBox(width: 8),
                    const Text(
                      'طباعة بقوالب احترافية',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),

              // محتوى الحوار
              Expanded(
                child: InvoiceTemplateSelector(
                  invoice: _invoice!,
                  showPreview: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على اسم الصنف من المعرف
  Future<String> _getItemName(int itemId) async {
    // التحقق من وجود الصنف في الكاش
    if (_itemsCache.containsKey(itemId)) {
      return _itemsCache[itemId]!.name;
    }

    try {
      // جلب الصنف من قاعدة البيانات
      final item = await _itemService.getItemById(itemId);
      if (item != null) {
        // إضافة الصنف إلى الكاش
        _itemsCache[itemId] = item;
        return item.name;
      }
    } catch (e) {
      // في حالة الخطأ، إرجاع معرف الصنف
      return 'صنف $itemId';
    }

    // في حالة عدم وجود الصنف
    return 'صنف غير موجود ($itemId)';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_invoice?.invoiceNumber ?? 'تفاصيل الفاتورة'),
        actions: [
          if (_invoice != null) ...[
            if (_invoice!.status.canAddPayments)
              IconButton(
                icon: const Icon(Icons.payment),
                onPressed: _showPaymentDialog,
                tooltip: 'إدارة الدفعات',
              ),
            if (_invoice!.status.canPrint)
              IconButton(
                icon: const Icon(Icons.print),
                onPressed: _printInvoice,
                tooltip: 'طباعة الفاتورة',
              ),
            IconButton(
              icon: const Icon(Icons.palette),
              onPressed: _printWithTemplates,
              tooltip: 'طباعة بقوالب احترافية',
            ),
            PopupMenuButton<InvoiceStatus>(
              icon: const Icon(Icons.more_vert),
              onSelected: _changeInvoiceStatus,
              itemBuilder: (context) {
                final allowedTransitions = _invoice!.status
                    .getAllowedTransitions();
                return allowedTransitions.map((status) {
                  return PopupMenuItem(
                    value: status,
                    child: Row(
                      children: [
                        Icon(
                          Icons.circle,
                          size: 12,
                          color: Color(
                            int.parse('0xFF${status.colorCode.substring(1)}'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text('تغيير إلى ${status.displayName}'),
                      ],
                    ),
                  );
                }).toList();
              },
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _invoice == null
          ? const Center(child: Text('الفاتورة غير موجودة'))
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInvoiceHeader(),
                  const SizedBox(height: 16),
                  _buildStatusCard(),
                  const SizedBox(height: 16),
                  if (_paymentSummary != null) ...[
                    _buildPaymentSummaryCard(),
                    const SizedBox(height: 16),
                  ],
                  _buildInvoiceItems(),
                  const SizedBox(height: 16),
                  _buildInvoiceTotals(),
                  const SizedBox(height: 16),
                  _buildStatusHistory(),
                ],
              ),
            ),
    );
  }

  Widget _buildInvoiceHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'فاتورة رقم: ${_invoice!.invoiceNumber}',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'التاريخ: ${_invoice!.invoiceDate.day}/${_invoice!.invoiceDate.month}/${_invoice!.invoiceDate.year}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      if (_invoice!.dueDate != null)
                        Text(
                          'تاريخ الاستحقاق: ${_invoice!.dueDate!.day}/${_invoice!.dueDate!.month}/${_invoice!.dueDate!.year}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Color(
                      int.parse(
                        '0xFF${_invoice!.status.colorCode.substring(1)}',
                      ),
                    ).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _invoice!.status.displayName,
                    style: TextStyle(
                      color: Color(
                        int.parse(
                          '0xFF${_invoice!.status.colorCode.substring(1)}',
                        ),
                      ),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (_invoice!.notes != null && _invoice!.notes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'ملاحظات: ${_invoice!.notes}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.circle,
                  color: Color(
                    int.parse('0xFF${_invoice!.status.colorCode.substring(1)}'),
                  ),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  _invoice!.status.displayName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 16),
                Text(
                  _invoice!.status.description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'الإجراءات المتاحة:',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Wrap(
              spacing: 8,
              children: [
                if (_invoice!.status.canEdit)
                  Chip(
                    label: const Text('يمكن التعديل'),
                    backgroundColor: Colors.green.withValues(alpha: 0.2),
                  ),
                if (_invoice!.status.canAddPayments)
                  Chip(
                    label: const Text('يمكن إضافة دفعات'),
                    backgroundColor: Colors.blue.withValues(alpha: 0.2),
                  ),
                if (_invoice!.status.canPrint)
                  Chip(
                    label: const Text('يمكن الطباعة'),
                    backgroundColor: Colors.orange.withValues(alpha: 0.2),
                  ),
                if (_invoice!.status.canDelete)
                  Chip(
                    label: const Text('يمكن الحذف'),
                    backgroundColor: Colors.red.withValues(alpha: 0.2),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSummaryCard() {
    if (_paymentSummary == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'ملخص الدفعات',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_invoice!.status.canAddPayments)
                  ElevatedButton.icon(
                    onPressed: _showPaymentDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة دفعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPaymentSummaryItem(
                    'إجمالي الفاتورة',
                    _paymentSummary!.totalAmount,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildPaymentSummaryItem(
                    'المبلغ المدفوع',
                    _paymentSummary!.paidAmount,
                    AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildPaymentSummaryItem(
                    'المبلغ المتبقي',
                    _paymentSummary!.remainingAmount,
                    _paymentSummary!.remainingAmount > 0
                        ? Colors.orange
                        : AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: _paymentSummary!.paidPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _paymentSummary!.isFullyPaid
                    ? AppColors.success
                    : Colors.orange,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'نسبة الدفع: ${_paymentSummary!.paidPercentage.toStringAsFixed(1)}% (${_paymentSummary!.payments.length} دفعة)',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSummaryItem(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ل.س',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInvoiceItems() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أصناف الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _invoice!.items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _invoice!.items[index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: FutureBuilder<String>(
                    future: _getItemName(item.itemId),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Text(snapshot.data!);
                      } else if (snapshot.hasError) {
                        return Text('صنف ${item.itemId}');
                      } else {
                        return Text('صنف ${item.itemId}');
                      }
                    },
                  ),
                  subtitle: Text(
                    'الكمية: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)} ل.س',
                  ),
                  trailing: Text(
                    '${item.netAmount.toStringAsFixed(2)} ل.س',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceTotals() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجماليات الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildTotalRow('المجموع الفرعي', _invoice!.subtotal),
            if (_invoice!.discountAmount > 0)
              _buildTotalRow(
                'الخصم',
                -_invoice!.discountAmount,
                color: Colors.red,
              ),
            if (_invoice!.taxAmount > 0)
              _buildTotalRow('الضريبة', _invoice!.taxAmount),
            const Divider(),
            _buildTotalRow(
              'الإجمالي النهائي',
              _invoice!.totalAmount,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(
    String label,
    double amount, {
    Color? color,
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ل.س',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: color ?? (isTotal ? AppColors.primary : null),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusHistory() {
    if (_statusHistory.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سجل تغيير الحالات',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _statusHistory.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final history = _statusHistory[index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: Icon(
                    Icons.history,
                    color: Color(
                      int.parse(
                        '0xFF${history.toStatus.colorCode.substring(1)}',
                      ),
                    ),
                  ),
                  title: Text(
                    '${history.fromStatus.displayName} ← ${history.toStatus.displayName}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('السبب: ${history.reason.displayName}'),
                      Text(
                        'التاريخ: ${history.changedAt.day}/${history.changedAt.month}/${history.changedAt.year}',
                      ),
                      if (history.notes != null && history.notes!.isNotEmpty)
                        Text('ملاحظات: ${history.notes}'),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
