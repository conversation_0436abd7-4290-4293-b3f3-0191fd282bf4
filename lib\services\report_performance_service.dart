import 'dart:collection';
import 'dart:math' as math;
import 'logging_service.dart';

/// خدمة مراقبة أداء التقارير
/// توفر مراقبة شاملة لأداء التقارير وتحليل الاختناقات
class ReportPerformanceService {
  static final ReportPerformanceService _instance =
      ReportPerformanceService._internal();
  factory ReportPerformanceService() => _instance;
  ReportPerformanceService._internal();

  final Map<String, ReportPerformanceMetrics> _reportMetrics = {};
  final Queue<ReportExecutionRecord> _executionHistory = Queue();
  final Map<String, Stopwatch> _activeTimers = {};

  // إعدادات المراقبة
  static const int maxHistorySize = 1000;
  static const Duration slowReportThreshold = Duration(seconds: 5);
  static const Duration verySlowReportThreshold = Duration(seconds: 15);

  /// بدء مراقبة تنفيذ تقرير
  String startReportExecution({
    required String reportType,
    required Map<String, dynamic> filters,
    Map<String, dynamic>? additionalData,
  }) {
    final executionId =
        '${reportType}_${DateTime.now().millisecondsSinceEpoch}';

    final stopwatch = Stopwatch()..start();
    _activeTimers[executionId] = stopwatch;

    LoggingService.debug(
      'بدء مراقبة تنفيذ التقرير',
      category: 'ReportPerformance',
      data: {
        'executionId': executionId,
        'reportType': reportType,
        'filters': filters,
        'additionalData': additionalData,
      },
    );

    return executionId;
  }

  /// إنهاء مراقبة تنفيذ تقرير
  void endReportExecution({
    required String executionId,
    required String reportType,
    required bool success,
    int? recordCount,
    String? errorMessage,
    Map<String, dynamic>? additionalMetrics,
  }) {
    final stopwatch = _activeTimers.remove(executionId);
    if (stopwatch == null) return;

    stopwatch.stop();
    final executionTime = stopwatch.elapsed;

    // تسجيل التنفيذ في التاريخ
    final record = ReportExecutionRecord(
      executionId: executionId,
      reportType: reportType,
      startTime: DateTime.now().subtract(executionTime),
      endTime: DateTime.now(),
      executionTime: executionTime,
      success: success,
      recordCount: recordCount,
      errorMessage: errorMessage,
      additionalMetrics: additionalMetrics ?? {},
    );

    _addExecutionRecord(record);
    _updateReportMetrics(reportType, record);

    // تحليل الأداء
    _analyzePerformance(record);

    LoggingService.info(
      'انتهاء تنفيذ التقرير',
      category: 'ReportPerformance',
      data: {
        'executionId': executionId,
        'reportType': reportType,
        'executionTimeMs': executionTime.inMilliseconds,
        'success': success,
        'recordCount': recordCount,
      },
    );
  }

  /// تسجيل مرحلة في تنفيذ التقرير
  void recordReportPhase({
    required String executionId,
    required String phaseName,
    required Duration duration,
    Map<String, dynamic>? phaseData,
  }) {
    LoggingService.debug(
      'مرحلة تنفيذ التقرير',
      category: 'ReportPerformance',
      data: {
        'executionId': executionId,
        'phaseName': phaseName,
        'durationMs': duration.inMilliseconds,
        'phaseData': phaseData,
      },
    );
  }

  /// الحصول على مقاييس أداء تقرير محدد
  ReportPerformanceMetrics? getReportMetrics(String reportType) {
    return _reportMetrics[reportType];
  }

  /// الحصول على جميع مقاييس الأداء
  Map<String, ReportPerformanceMetrics> getAllMetrics() {
    return Map.unmodifiable(_reportMetrics);
  }

  /// الحصول على تاريخ التنفيذ
  List<ReportExecutionRecord> getExecutionHistory({
    String? reportType,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
  }) {
    var filtered = _executionHistory.where((record) {
      if (reportType != null && record.reportType != reportType) return false;
      if (fromDate != null && record.startTime.isBefore(fromDate)) return false;
      if (toDate != null && record.endTime.isAfter(toDate)) return false;
      return true;
    });

    if (limit != null) {
      filtered = filtered.take(limit);
    }

    return filtered.toList();
  }

  /// تحليل الأداء العام
  PerformanceAnalysis analyzeOverallPerformance() {
    if (_executionHistory.isEmpty) {
      return PerformanceAnalysis.empty();
    }

    final totalExecutions = _executionHistory.length;
    final successfulExecutions = _executionHistory
        .where((r) => r.success)
        .length;
    final failedExecutions = totalExecutions - successfulExecutions;

    final executionTimes = _executionHistory
        .where((r) => r.success)
        .map((r) => r.executionTime.inMilliseconds)
        .toList();

    executionTimes.sort();

    final averageTime = executionTimes.isNotEmpty
        ? executionTimes.reduce((a, b) => a + b) / executionTimes.length
        : 0.0;

    final medianTime = executionTimes.isNotEmpty
        ? executionTimes[executionTimes.length ~/ 2].toDouble()
        : 0.0;

    final p95Time = executionTimes.isNotEmpty
        ? executionTimes[(executionTimes.length * 0.95).floor()].toDouble()
        : 0.0;

    final slowReports = _executionHistory
        .where((r) => r.executionTime > slowReportThreshold)
        .length;

    final verySlowReports = _executionHistory
        .where((r) => r.executionTime > verySlowReportThreshold)
        .length;

    return PerformanceAnalysis(
      totalExecutions: totalExecutions,
      successfulExecutions: successfulExecutions,
      failedExecutions: failedExecutions,
      successRate: totalExecutions > 0
          ? successfulExecutions / totalExecutions
          : 0.0,
      averageExecutionTimeMs: averageTime,
      medianExecutionTimeMs: medianTime,
      p95ExecutionTimeMs: p95Time,
      slowReports: slowReports,
      verySlowReports: verySlowReports,
      reportTypeMetrics: Map.from(_reportMetrics),
    );
  }

  /// الحصول على التقارير الأبطأ
  List<ReportExecutionRecord> getSlowestReports({int limit = 10}) {
    final sorted = _executionHistory.toList()
      ..sort((a, b) => b.executionTime.compareTo(a.executionTime));

    return sorted.take(limit).toList();
  }

  /// الحصول على التقارير الأكثر فشلاً
  Map<String, int> getMostFailedReports() {
    final failureCounts = <String, int>{};

    for (final record in _executionHistory.where((r) => !r.success)) {
      failureCounts[record.reportType] =
          (failureCounts[record.reportType] ?? 0) + 1;
    }

    return Map.fromEntries(
      failureCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value)),
    );
  }

  /// مسح البيانات القديمة
  void clearOldData({Duration? olderThan}) {
    final cutoffDate = DateTime.now().subtract(
      olderThan ?? const Duration(days: 30),
    );

    _executionHistory.removeWhere(
      (record) => record.endTime.isBefore(cutoffDate),
    );

    LoggingService.info(
      'تم مسح البيانات القديمة',
      category: 'ReportPerformance',
      data: {'cutoffDate': cutoffDate.toIso8601String()},
    );
  }

  /// إضافة سجل تنفيذ
  void _addExecutionRecord(ReportExecutionRecord record) {
    _executionHistory.add(record);

    // الحفاظ على حد أقصى للتاريخ
    while (_executionHistory.length > maxHistorySize) {
      _executionHistory.removeFirst();
    }
  }

  /// تحديث مقاييس التقرير
  void _updateReportMetrics(String reportType, ReportExecutionRecord record) {
    final existing = _reportMetrics[reportType];

    if (existing == null) {
      _reportMetrics[reportType] = ReportPerformanceMetrics(
        reportType: reportType,
        totalExecutions: 1,
        successfulExecutions: record.success ? 1 : 0,
        failedExecutions: record.success ? 0 : 1,
        totalExecutionTimeMs: record.executionTime.inMilliseconds,
        minExecutionTimeMs: record.executionTime.inMilliseconds,
        maxExecutionTimeMs: record.executionTime.inMilliseconds,
        lastExecutionTime: record.endTime,
      );
    } else {
      _reportMetrics[reportType] = existing.copyWith(
        totalExecutions: existing.totalExecutions + 1,
        successfulExecutions:
            existing.successfulExecutions + (record.success ? 1 : 0),
        failedExecutions: existing.failedExecutions + (record.success ? 0 : 1),
        totalExecutionTimeMs:
            existing.totalExecutionTimeMs + record.executionTime.inMilliseconds,
        minExecutionTimeMs: math.min(
          existing.minExecutionTimeMs,
          record.executionTime.inMilliseconds,
        ),
        maxExecutionTimeMs: math.max(
          existing.maxExecutionTimeMs,
          record.executionTime.inMilliseconds,
        ),
        lastExecutionTime: record.endTime,
      );
    }
  }

  /// تحليل الأداء
  void _analyzePerformance(ReportExecutionRecord record) {
    // تحذير للتقارير البطيئة
    if (record.executionTime > verySlowReportThreshold) {
      LoggingService.warning(
        'تقرير بطيء جداً',
        category: 'ReportPerformance',
        data: {
          'reportType': record.reportType,
          'executionTimeMs': record.executionTime.inMilliseconds,
          'threshold': verySlowReportThreshold.inMilliseconds,
        },
      );
    } else if (record.executionTime > slowReportThreshold) {
      LoggingService.warning(
        'تقرير بطيء',
        category: 'ReportPerformance',
        data: {
          'reportType': record.reportType,
          'executionTimeMs': record.executionTime.inMilliseconds,
          'threshold': slowReportThreshold.inMilliseconds,
        },
      );
    }

    // تحذير للفشل المتكرر
    final recentFailures = _executionHistory
        .where(
          (r) =>
              r.reportType == record.reportType &&
              !r.success &&
              DateTime.now().difference(r.endTime) <
                  const Duration(minutes: 10),
        )
        .length;

    if (recentFailures >= 3) {
      LoggingService.error(
        'فشل متكرر في التقرير',
        category: 'ReportPerformance',
        data: {
          'reportType': record.reportType,
          'recentFailures': recentFailures,
        },
      );
    }
  }
}

/// سجل تنفيذ التقرير
class ReportExecutionRecord {
  final String executionId;
  final String reportType;
  final DateTime startTime;
  final DateTime endTime;
  final Duration executionTime;
  final bool success;
  final int? recordCount;
  final String? errorMessage;
  final Map<String, dynamic> additionalMetrics;

  const ReportExecutionRecord({
    required this.executionId,
    required this.reportType,
    required this.startTime,
    required this.endTime,
    required this.executionTime,
    required this.success,
    this.recordCount,
    this.errorMessage,
    required this.additionalMetrics,
  });

  Map<String, dynamic> toMap() {
    return {
      'executionId': executionId,
      'reportType': reportType,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'executionTimeMs': executionTime.inMilliseconds,
      'success': success,
      'recordCount': recordCount,
      'errorMessage': errorMessage,
      'additionalMetrics': additionalMetrics,
    };
  }
}

/// مقاييس أداء التقرير
class ReportPerformanceMetrics {
  final String reportType;
  final int totalExecutions;
  final int successfulExecutions;
  final int failedExecutions;
  final int totalExecutionTimeMs;
  final int minExecutionTimeMs;
  final int maxExecutionTimeMs;
  final DateTime lastExecutionTime;

  const ReportPerformanceMetrics({
    required this.reportType,
    required this.totalExecutions,
    required this.successfulExecutions,
    required this.failedExecutions,
    required this.totalExecutionTimeMs,
    required this.minExecutionTimeMs,
    required this.maxExecutionTimeMs,
    required this.lastExecutionTime,
  });

  double get successRate =>
      totalExecutions > 0 ? successfulExecutions / totalExecutions : 0.0;

  double get averageExecutionTimeMs => successfulExecutions > 0
      ? totalExecutionTimeMs / successfulExecutions
      : 0.0;

  ReportPerformanceMetrics copyWith({
    int? totalExecutions,
    int? successfulExecutions,
    int? failedExecutions,
    int? totalExecutionTimeMs,
    int? minExecutionTimeMs,
    int? maxExecutionTimeMs,
    DateTime? lastExecutionTime,
  }) {
    return ReportPerformanceMetrics(
      reportType: reportType,
      totalExecutions: totalExecutions ?? this.totalExecutions,
      successfulExecutions: successfulExecutions ?? this.successfulExecutions,
      failedExecutions: failedExecutions ?? this.failedExecutions,
      totalExecutionTimeMs: totalExecutionTimeMs ?? this.totalExecutionTimeMs,
      minExecutionTimeMs: minExecutionTimeMs ?? this.minExecutionTimeMs,
      maxExecutionTimeMs: maxExecutionTimeMs ?? this.maxExecutionTimeMs,
      lastExecutionTime: lastExecutionTime ?? this.lastExecutionTime,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'reportType': reportType,
      'totalExecutions': totalExecutions,
      'successfulExecutions': successfulExecutions,
      'failedExecutions': failedExecutions,
      'successRate': successRate,
      'averageExecutionTimeMs': averageExecutionTimeMs,
      'minExecutionTimeMs': minExecutionTimeMs,
      'maxExecutionTimeMs': maxExecutionTimeMs,
      'lastExecutionTime': lastExecutionTime.toIso8601String(),
    };
  }
}

/// تحليل الأداء العام
class PerformanceAnalysis {
  final int totalExecutions;
  final int successfulExecutions;
  final int failedExecutions;
  final double successRate;
  final double averageExecutionTimeMs;
  final double medianExecutionTimeMs;
  final double p95ExecutionTimeMs;
  final int slowReports;
  final int verySlowReports;
  final Map<String, ReportPerformanceMetrics> reportTypeMetrics;

  const PerformanceAnalysis({
    required this.totalExecutions,
    required this.successfulExecutions,
    required this.failedExecutions,
    required this.successRate,
    required this.averageExecutionTimeMs,
    required this.medianExecutionTimeMs,
    required this.p95ExecutionTimeMs,
    required this.slowReports,
    required this.verySlowReports,
    required this.reportTypeMetrics,
  });

  factory PerformanceAnalysis.empty() {
    return const PerformanceAnalysis(
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      successRate: 0.0,
      averageExecutionTimeMs: 0.0,
      medianExecutionTimeMs: 0.0,
      p95ExecutionTimeMs: 0.0,
      slowReports: 0,
      verySlowReports: 0,
      reportTypeMetrics: {},
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalExecutions': totalExecutions,
      'successfulExecutions': successfulExecutions,
      'failedExecutions': failedExecutions,
      'successRate': successRate,
      'averageExecutionTimeMs': averageExecutionTimeMs,
      'medianExecutionTimeMs': medianExecutionTimeMs,
      'p95ExecutionTimeMs': p95ExecutionTimeMs,
      'slowReports': slowReports,
      'verySlowReports': verySlowReports,
      'reportTypeCount': reportTypeMetrics.length,
    };
  }
}
