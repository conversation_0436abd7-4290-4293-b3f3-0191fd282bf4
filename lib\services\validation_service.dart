import '../constants/app_constants.dart';
import '../exceptions/validation_exception.dart';
import 'error_message_service.dart';

/// خدمة التحقق من صحة البيانات المركزية
/// تحتوي على جميع قواعد التحقق المطلوبة للتطبيق
class ValidationService {
  // ===============================
  // التحقق من الحقول الأساسية
  // ===============================

  /// التحقق من أن الحقل غير فارغ
  static ValidationResult validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.error(
        ErrorMessageService.getRequiredFieldMessage(fieldName),
      );
    }
    return ValidationResult.success();
  }

  /// التحقق من طول النص
  static ValidationResult validateLength(
    String? value,
    String fieldName, {
    int? minLength,
    int? maxLength,
  }) {
    if (value == null) return ValidationResult.success();

    if (minLength != null && value.length < minLength) {
      return ValidationResult.error(
        ErrorMessageService.getLengthErrorMessage(
          fieldName,
          minLength: minLength,
        ),
      );
    }

    if (maxLength != null && value.length > maxLength) {
      return ValidationResult.error(
        ErrorMessageService.getLengthErrorMessage(
          fieldName,
          maxLength: maxLength,
        ),
      );
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق من البيانات المالية
  // ===============================

  /// التحقق من صحة المبلغ المالي
  static ValidationResult validateAmount(
    double? amount, {
    bool allowZero = true,
  }) {
    if (amount == null) {
      return ValidationResult.error('المبلغ مطلوب');
    }

    if (!allowZero && amount == 0) {
      return ValidationResult.error('المبلغ يجب أن يكون أكبر من صفر');
    }

    if (amount < 0) {
      return ValidationResult.error('المبلغ لا يمكن أن يكون سالباً');
    }

    if (amount > *********.99) {
      return ValidationResult.error('المبلغ كبير جداً');
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة كود الحساب
  static ValidationResult validateAccountCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return ValidationResult.error(
        ErrorMessageService.getRequiredFieldMessage('كود الحساب'),
      );
    }

    final cleanCode = code.trim().toUpperCase();

    // يجب أن يبدأ بحرف ويتبعه أرقام (3-6 أرقام)
    final regex = RegExp(r'^[A-Z][0-9]{3,6}$');
    if (!regex.hasMatch(cleanCode)) {
      return ValidationResult.error(
        ErrorMessageService.getCodeFormatMessage('account'),
      );
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة كود العميل
  static ValidationResult validateCustomerCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return ValidationResult.error('كود العميل مطلوب');
    }

    final cleanCode = code.trim().toUpperCase();

    // يجب أن يبدأ بـ C ويتبعه أرقام
    final regex = RegExp(r'^C[0-9]{4,6}$');
    if (!regex.hasMatch(cleanCode)) {
      return ValidationResult.error(
        'كود العميل يجب أن يبدأ بـ C ويتبعه 4-6 أرقام (مثال: C0001)',
      );
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة كود المورد
  static ValidationResult validateSupplierCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return ValidationResult.error('كود المورد مطلوب');
    }

    final cleanCode = code.trim().toUpperCase();

    // يجب أن يبدأ بـ S ويتبعه أرقام
    final regex = RegExp(r'^S[0-9]{4,6}$');
    if (!regex.hasMatch(cleanCode)) {
      return ValidationResult.error(
        'كود المورد يجب أن يبدأ بـ S ويتبعه 4-6 أرقام (مثال: S0001)',
      );
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة كود الصنف
  static ValidationResult validateItemCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return ValidationResult.error('كود الصنف مطلوب');
    }

    final cleanCode = code.trim().toUpperCase();

    // يجب أن يبدأ بـ I ويتبعه أرقام
    final regex = RegExp(r'^I[0-9]{4,6}$');
    if (!regex.hasMatch(cleanCode)) {
      return ValidationResult.error(
        'كود الصنف يجب أن يبدأ بـ I ويتبعه 4-6 أرقام (مثال: I0001)',
      );
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق من بيانات الاتصال
  // ===============================

  /// التحقق من صحة رقم الهاتف
  static ValidationResult validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return ValidationResult.success(); // اختياري
    }

    final cleanPhone = phone.trim().replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // رقم هاتف سوري أو دولي
    final regex = RegExp(r'^(\+?963|0)?[0-9]{8,9}$');
    if (!regex.hasMatch(cleanPhone)) {
      return ValidationResult.error(
        'رقم الهاتف غير صحيح (مثال: 0933123456 أو +963933123456)',
      );
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة البريد الإلكتروني
  static ValidationResult validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return ValidationResult.success(); // اختياري
    }

    final cleanEmail = email.trim().toLowerCase();

    final regex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!regex.hasMatch(cleanEmail)) {
      return ValidationResult.error('البريد الإلكتروني غير صحيح');
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق من التواريخ
  // ===============================

  /// التحقق من صحة التاريخ
  static ValidationResult validateDate(
    DateTime? date, {
    DateTime? minDate,
    DateTime? maxDate,
  }) {
    if (date == null) {
      return ValidationResult.error('التاريخ مطلوب');
    }

    final now = DateTime.now();
    final defaultMinDate = DateTime(1900);
    final defaultMaxDate = DateTime(now.year + 10);

    final actualMinDate = minDate ?? defaultMinDate;
    final actualMaxDate = maxDate ?? defaultMaxDate;

    if (date.isBefore(actualMinDate)) {
      return ValidationResult.error(
        'التاريخ لا يمكن أن يكون قبل ${_formatDate(actualMinDate)}',
      );
    }

    if (date.isAfter(actualMaxDate)) {
      return ValidationResult.error(
        'التاريخ لا يمكن أن يكون بعد ${_formatDate(actualMaxDate)}',
      );
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق من الكميات والمخزون
  // ===============================

  /// التحقق من صحة الكمية
  static ValidationResult validateQuantity(
    double? quantity, {
    bool allowZero = false,
  }) {
    if (quantity == null) {
      return ValidationResult.error('الكمية مطلوبة');
    }

    if (!allowZero && quantity <= 0) {
      return ValidationResult.error('الكمية يجب أن تكون أكبر من صفر');
    }

    if (allowZero && quantity < 0) {
      return ValidationResult.error('الكمية لا يمكن أن تكون سالبة');
    }

    if (quantity > 999999) {
      return ValidationResult.error('الكمية كبيرة جداً');
    }

    return ValidationResult.success();
  }

  // ===============================
  // تنظيف البيانات
  // ===============================

  /// تنظيف النص من الأحرف الخطيرة
  static String sanitizeText(String? input) {
    if (input == null) return '';

    return input
        .replaceAll(
          RegExp(r'[<>"]'),
          '',
        ) // إزالة HTML tags والاقتباسات المزدوجة
        .replaceAll("'", '') // إزالة الاقتباسات المفردة
        .replaceAll(RegExp(r'[;]'), '') // إزالة الفاصلة المنقوطة
        .replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '') // إزالة control characters
        .trim();
  }

  /// تنظيف الأرقام
  static String sanitizeNumber(String? input) {
    if (input == null) return '';

    return input
        .replaceAll(
          RegExp(r'[^\d\.\-\+]'),
          '',
        ) // الاحتفاظ بالأرقام والعلامات فقط
        .trim();
  }

  // ===============================
  // التحقق من القيود المحاسبية
  // ===============================

  /// التحقق من توازن القيد المحاسبي
  static ValidationResult validateJournalEntryBalance(
    List<Map<String, dynamic>> details,
  ) {
    if (details.length < 2) {
      return ValidationResult.error('القيد يجب أن يحتوي على سطرين على الأقل');
    }

    double totalDebit = 0;
    double totalCredit = 0;

    for (final detail in details) {
      final debit = (detail['debit_amount'] as num?)?.toDouble() ?? 0;
      final credit = (detail['credit_amount'] as num?)?.toDouble() ?? 0;

      if (debit > 0 && credit > 0) {
        return ValidationResult.error(
          'لا يمكن أن يكون للسطر مبلغ مدين ودائن معاً',
        );
      }

      if (debit == 0 && credit == 0) {
        return ValidationResult.error(
          'كل سطر يجب أن يحتوي على مبلغ مدين أو دائن',
        );
      }

      totalDebit += debit;
      totalCredit += credit;
    }

    if ((totalDebit - totalCredit).abs() > 0.01) {
      return ValidationResult.error(
        'القيد غير متوازن - مجموع المدين (${totalDebit.toStringAsFixed(2)}) '
        'يجب أن يساوي مجموع الدائن (${totalCredit.toStringAsFixed(2)})',
      );
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة رقم القيد
  static ValidationResult validateJournalEntryNumber(String? entryNumber) {
    if (entryNumber == null || entryNumber.trim().isEmpty) {
      return ValidationResult.error('رقم القيد مطلوب');
    }

    final cleanNumber = entryNumber.trim();

    // رقم القيد يجب أن يكون أرقام فقط أو يبدأ بحروف ويتبعه أرقام
    final regex = RegExp(r'^[A-Z]*[0-9]+$');
    if (!regex.hasMatch(cleanNumber)) {
      return ValidationResult.error(
        'رقم القيد يجب أن يحتوي على أرقام فقط أو يبدأ بحروف',
      );
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق من الفواتير
  // ===============================

  /// التحقق من صحة بيانات الفاتورة
  static ValidationResult validateInvoice({
    required String? invoiceNumber,
    required DateTime? invoiceDate,
    required int? customerId,
    required List<Map<String, dynamic>> items,
  }) {
    // التحقق من رقم الفاتورة
    if (invoiceNumber == null || invoiceNumber.trim().isEmpty) {
      return ValidationResult.error('رقم الفاتورة مطلوب');
    }

    // التحقق من تاريخ الفاتورة
    final dateValidation = validateDate(invoiceDate);
    if (!dateValidation.isValid) {
      return dateValidation;
    }

    // التحقق من العميل
    if (customerId == null || customerId <= 0) {
      return ValidationResult.error('يجب اختيار عميل للفاتورة');
    }

    // التحقق من وجود أصناف
    if (items.isEmpty) {
      return ValidationResult.error(
        'الفاتورة يجب أن تحتوي على صنف واحد على الأقل',
      );
    }

    // التحقق من صحة كل صنف
    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      final itemValidation = _validateInvoiceItem(item, i + 1);
      if (!itemValidation.isValid) {
        return itemValidation;
      }
    }

    return ValidationResult.success();
  }

  /// التحقق من صحة صنف في الفاتورة
  static ValidationResult _validateInvoiceItem(
    Map<String, dynamic> item,
    int lineNumber,
  ) {
    final itemId = item['item_id'] as int?;
    final quantity = (item['quantity'] as num?)?.toDouble();
    final unitPrice = (item['unit_price'] as num?)?.toDouble();

    if (itemId == null || itemId <= 0) {
      return ValidationResult.error('يجب اختيار صنف للسطر رقم $lineNumber');
    }

    final quantityValidation = validateQuantity(quantity);
    if (!quantityValidation.isValid) {
      return ValidationResult.error(
        'السطر $lineNumber: ${quantityValidation.errorMessage}',
      );
    }

    final priceValidation = validateAmount(unitPrice);
    if (!priceValidation.isValid) {
      return ValidationResult.error(
        'السطر $lineNumber: سعر الوحدة ${priceValidation.errorMessage}',
      );
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق المركب للنماذج
  // ===============================

  /// التحقق من صحة بيانات الحساب
  static ValidationResult validateAccount({
    required String? code,
    required String? name,
    required String? type,
    required double? balance,
  }) {
    final codeValidation = validateAccountCode(code);
    if (!codeValidation.isValid) return codeValidation;

    final nameValidation = validateRequired(name, 'اسم الحساب');
    if (!nameValidation.isValid) return nameValidation;

    final lengthValidation = validateLength(
      name,
      'اسم الحساب',
      maxLength: AppConstants.maxAccountNameLength,
    );
    if (!lengthValidation.isValid) return lengthValidation;

    if (type == null || type.isEmpty) {
      return ValidationResult.error('نوع الحساب مطلوب');
    }

    final balanceValidation = validateAmount(balance, allowZero: true);
    if (!balanceValidation.isValid) return balanceValidation;

    return ValidationResult.success();
  }

  /// التحقق من صحة بيانات العميل
  static ValidationResult validateCustomer({
    required String? code,
    required String? name,
    String? phone,
    String? email,
    String? address,
  }) {
    final codeValidation = validateCustomerCode(code);
    if (!codeValidation.isValid) return codeValidation;

    final nameValidation = validateRequired(name, 'اسم العميل');
    if (!nameValidation.isValid) return nameValidation;

    final lengthValidation = validateLength(
      name,
      'اسم العميل',
      maxLength: AppConstants.maxCustomerNameLength,
    );
    if (!lengthValidation.isValid) return lengthValidation;

    final phoneValidation = validatePhoneNumber(phone);
    if (!phoneValidation.isValid) return phoneValidation;

    final emailValidation = validateEmail(email);
    if (!emailValidation.isValid) return emailValidation;

    return ValidationResult.success();
  }

  /// التحقق من صحة بيانات المورد
  static ValidationResult validateSupplier({
    required String? code,
    required String? name,
    String? phone,
    String? email,
    String? address,
  }) {
    final codeValidation = validateSupplierCode(code);
    if (!codeValidation.isValid) return codeValidation;

    final nameValidation = validateRequired(name, 'اسم المورد');
    if (!nameValidation.isValid) return nameValidation;

    final lengthValidation = validateLength(
      name,
      'اسم المورد',
      maxLength: AppConstants.maxSupplierNameLength,
    );
    if (!lengthValidation.isValid) return lengthValidation;

    final phoneValidation = validatePhoneNumber(phone);
    if (!phoneValidation.isValid) return phoneValidation;

    final emailValidation = validateEmail(email);
    if (!emailValidation.isValid) return emailValidation;

    return ValidationResult.success();
  }

  /// التحقق من صحة بيانات الصنف
  static ValidationResult validateItem({
    required String? code,
    required String? name,
    required String? unit,
    required double? costPrice,
    required double? sellingPrice,
    required double? quantity,
    required double? minQuantity,
  }) {
    final codeValidation = validateItemCode(code);
    if (!codeValidation.isValid) return codeValidation;

    final nameValidation = validateRequired(name, 'اسم الصنف');
    if (!nameValidation.isValid) return nameValidation;

    final lengthValidation = validateLength(
      name,
      'اسم الصنف',
      maxLength: AppConstants.maxItemNameLength,
    );
    if (!lengthValidation.isValid) return lengthValidation;

    final unitValidation = validateRequired(unit, 'وحدة القياس');
    if (!unitValidation.isValid) return unitValidation;

    final costPriceValidation = validateAmount(costPrice, allowZero: true);
    if (!costPriceValidation.isValid) return costPriceValidation;

    final sellingPriceValidation = validateAmount(
      sellingPrice,
      allowZero: true,
    );
    if (!sellingPriceValidation.isValid) return sellingPriceValidation;

    final quantityValidation = validateQuantity(quantity, allowZero: true);
    if (!quantityValidation.isValid) return quantityValidation;

    final minQuantityValidation = validateQuantity(
      minQuantity,
      allowZero: true,
    );
    if (!minQuantityValidation.isValid) return minQuantityValidation;

    // التحقق من أن سعر البيع أكبر من سعر التكلفة
    if (costPrice != null &&
        sellingPrice != null &&
        costPrice > 0 &&
        sellingPrice > 0 &&
        sellingPrice < costPrice) {
      return ValidationResult.error(
        'سعر البيع لا يمكن أن يكون أقل من سعر التكلفة',
      );
    }

    return ValidationResult.success();
  }

  // ===============================
  // التحقق من القواعد التجارية
  // ===============================

  /// التحقق من إمكانية حذف الحساب
  static void validateAccountDeletion(
    int accountId, {
    required bool hasJournalEntries,
    required bool hasInvoices,
    required bool hasSubAccounts,
  }) {
    if (hasJournalEntries) {
      throw BusinessRuleException.hasRelatedData('الحساب', 'قيود محاسبية');
    }

    if (hasInvoices) {
      throw BusinessRuleException.hasRelatedData('الحساب', 'فواتير');
    }

    if (hasSubAccounts) {
      throw BusinessRuleException.hasRelatedData('الحساب', 'حسابات فرعية');
    }
  }

  /// التحقق من إمكانية حذف العميل
  static void validateCustomerDeletion(
    int customerId, {
    required bool hasInvoices,
    required bool hasJournalEntries,
  }) {
    if (hasInvoices) {
      throw BusinessRuleException.hasRelatedData('العميل', 'فواتير');
    }

    if (hasJournalEntries) {
      throw BusinessRuleException.hasRelatedData('العميل', 'قيود محاسبية');
    }
  }

  /// التحقق من إمكانية حذف المورد
  static void validateSupplierDeletion(
    int supplierId, {
    required bool hasInvoices,
    required bool hasJournalEntries,
  }) {
    if (hasInvoices) {
      throw BusinessRuleException.hasRelatedData('المورد', 'فواتير');
    }

    if (hasJournalEntries) {
      throw BusinessRuleException.hasRelatedData('المورد', 'قيود محاسبية');
    }
  }

  // ===============================
  // دوال مساعدة
  // ===============================

  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }
}

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  const ValidationResult._(this.isValid, this.errorMessage);

  factory ValidationResult.success() => const ValidationResult._(true, null);
  factory ValidationResult.error(String message) =>
      ValidationResult._(false, message);

  @override
  String toString() => isValid ? 'Valid' : 'Invalid: $errorMessage';
}
