import 'package:flutter/foundation.dart';

/// مستويات التسجيل
enum LogLevel {
  debug,
  info,
  warning,
  error,
  security,
}

/// خدمة التسجيل المتقدمة
/// تدير تسجيل الأحداث والأخطاء بطريقة احترافية
class LoggingService {
  static final List<LogEntry> _logs = [];
  static bool _isEnabled = true;

  /// تفعيل أو إلغاء تفعيل التسجيل
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// تسجيل رسالة عامة
  static void log(
    String message, {
    LogLevel level = LogLevel.info,
    String? category,
    Map<String, dynamic>? data,
  }) {
    if (!_isEnabled) return;

    final logEntry = LogEntry(
      timestamp: DateTime.now(),
      level: level,
      message: message,
      category: category,
      data: data,
    );

    _logs.add(logEntry);

    // في وضع التطوير فقط، اطبع في وحدة التحكم
    if (kDebugMode) {
      _printToConsole(logEntry);
    }

    // احتفظ بآخر 1000 سجل فقط لتوفير الذاكرة
    if (_logs.length > 1000) {
      _logs.removeAt(0);
    }
  }

  /// تسجيل رسالة معلومات
  static void info(String message, {String? category, Map<String, dynamic>? data}) {
    log(message, level: LogLevel.info, category: category, data: data);
  }

  /// تسجيل رسالة تحذير
  static void warning(String message, {String? category, Map<String, dynamic>? data}) {
    log(message, level: LogLevel.warning, category: category, data: data);
  }

  /// تسجيل رسالة خطأ
  static void error(String message, {String? category, Map<String, dynamic>? data}) {
    log(message, level: LogLevel.error, category: category, data: data);
  }

  /// تسجيل حدث أمني
  static void security(String message, {String? category, Map<String, dynamic>? data}) {
    log(message, level: LogLevel.security, category: category, data: data);
  }

  /// تسجيل رسالة تطوير
  static void debug(String message, {String? category, Map<String, dynamic>? data}) {
    log(message, level: LogLevel.debug, category: category, data: data);
  }

  /// الحصول على جميع السجلات
  static List<LogEntry> getAllLogs() {
    return List.unmodifiable(_logs);
  }

  /// الحصول على السجلات حسب المستوى
  static List<LogEntry> getLogsByLevel(LogLevel level) {
    return _logs.where((log) => log.level == level).toList();
  }

  /// الحصول على السجلات حسب الفئة
  static List<LogEntry> getLogsByCategory(String category) {
    return _logs.where((log) => log.category == category).toList();
  }

  /// مسح جميع السجلات
  static void clearLogs() {
    _logs.clear();
  }

  /// طباعة السجل في وحدة التحكم (للتطوير فقط)
  static void _printToConsole(LogEntry entry) {
    final icon = _getLogIcon(entry.level);
    final timestamp = entry.timestamp.toString().substring(11, 19);
    final category = entry.category != null ? '[${entry.category}] ' : '';
    
    // استخدام debugPrint بدلاً من print للتطوير
    debugPrint('$icon [$timestamp] $category${entry.message}');
    
    if (entry.data != null && entry.data!.isNotEmpty) {
      debugPrint('   البيانات: ${entry.data}');
    }
  }

  /// الحصول على أيقونة المستوى
  static String _getLogIcon(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
      case LogLevel.security:
        return '🔒';
    }
  }

  /// تصدير السجلات كنص
  static String exportLogsAsText() {
    final buffer = StringBuffer();
    buffer.writeln('=== سجلات Smart Ledger ===');
    buffer.writeln('تاريخ التصدير: ${DateTime.now()}');
    buffer.writeln('عدد السجلات: ${_logs.length}');
    buffer.writeln('');

    for (final log in _logs) {
      final icon = _getLogIcon(log.level);
      final category = log.category != null ? '[${log.category}] ' : '';
      buffer.writeln('$icon ${log.timestamp} $category${log.message}');
      
      if (log.data != null && log.data!.isNotEmpty) {
        buffer.writeln('   البيانات: ${log.data}');
      }
      buffer.writeln('');
    }

    return buffer.toString();
  }
}

/// نموذج سجل واحد
class LogEntry {
  final DateTime timestamp;
  final LogLevel level;
  final String message;
  final String? category;
  final Map<String, dynamic>? data;

  LogEntry({
    required this.timestamp,
    required this.level,
    required this.message,
    this.category,
    this.data,
  });

  @override
  String toString() {
    final categoryStr = category != null ? '[$category] ' : '';
    return '${timestamp.toString().substring(11, 19)} $categoryStr$message';
  }
}
