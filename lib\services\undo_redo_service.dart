import 'dart:collection';
import 'package:flutter/foundation.dart';

/// خدمة التراجع والإعادة
/// تدير عمليات التراجع والإعادة للعمليات المحاسبية
class UndoRedoService extends ChangeNotifier {
  static final UndoRedoService _instance = UndoRedoService._internal();
  factory UndoRedoService() => _instance;
  UndoRedoService._internal();

  // مكدس العمليات للتراجع
  final Queue<UndoRedoAction> _undoStack = Queue<UndoRedoAction>();

  // مكدس العمليات للإعادة
  final Queue<UndoRedoAction> _redoStack = Queue<UndoRedoAction>();

  // الحد الأقصى لعدد العمليات المحفوظة
  static const int _maxStackSize = 50;

  // ===============================
  // خصائص الحالة
  // ===============================

  /// هل يمكن التراجع؟
  bool get canUndo => _undoStack.isNotEmpty;

  /// هل يمكن الإعادة؟
  bool get canRedo => _redoStack.isNotEmpty;

  /// عدد العمليات المتاحة للتراجع
  int get undoCount => _undoStack.length;

  /// عدد العمليات المتاحة للإعادة
  int get redoCount => _redoStack.length;

  /// آخر عملية في مكدس التراجع
  UndoRedoAction? get lastUndoAction =>
      _undoStack.isNotEmpty ? _undoStack.last : null;

  /// آخر عملية في مكدس الإعادة
  UndoRedoAction? get lastRedoAction =>
      _redoStack.isNotEmpty ? _redoStack.last : null;

  // ===============================
  // إدارة العمليات
  // ===============================

  /// إضافة عملية جديدة
  void addAction(UndoRedoAction action) {
    // إضافة العملية لمكدس التراجع
    _undoStack.addLast(action);

    // تنظيف مكدس الإعادة عند إضافة عملية جديدة
    _redoStack.clear();

    // التأكد من عدم تجاوز الحد الأقصى
    while (_undoStack.length > _maxStackSize) {
      _undoStack.removeFirst();
    }

    notifyListeners();
  }

  /// تنفيذ التراجع
  Future<bool> undo() async {
    if (!canUndo) return false;

    try {
      final action = _undoStack.removeLast();

      // تنفيذ التراجع
      await action.undo();

      // إضافة العملية لمكدس الإعادة
      _redoStack.addLast(action);

      // التأكد من عدم تجاوز الحد الأقصى
      while (_redoStack.length > _maxStackSize) {
        _redoStack.removeFirst();
      }

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('خطأ في التراجع: $e');
      return false;
    }
  }

  /// تنفيذ الإعادة
  Future<bool> redo() async {
    if (!canRedo) return false;

    try {
      final action = _redoStack.removeLast();

      // تنفيذ الإعادة
      await action.redo();

      // إضافة العملية لمكدس التراجع
      _undoStack.addLast(action);

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('خطأ في الإعادة: $e');
      return false;
    }
  }

  /// مسح جميع العمليات
  void clear() {
    _undoStack.clear();
    _redoStack.clear();
    notifyListeners();
  }

  /// مسح مكدس التراجع فقط
  void clearUndoStack() {
    _undoStack.clear();
    notifyListeners();
  }

  /// مسح مكدس الإعادة فقط
  void clearRedoStack() {
    _redoStack.clear();
    notifyListeners();
  }

  // ===============================
  // عمليات مجمعة
  // ===============================

  /// تجميع عدة عمليات في عملية واحدة
  void beginGroup(String groupName) {
    _currentGroup = UndoRedoGroup(groupName);
  }

  /// إنهاء التجميع وإضافة المجموعة
  void endGroup() {
    if (_currentGroup != null && _currentGroup!.actions.isNotEmpty) {
      addAction(_currentGroup!);
      _currentGroup = null;
    }
  }

  /// إلغاء التجميع الحالي
  void cancelGroup() {
    _currentGroup = null;
  }

  UndoRedoGroup? _currentGroup;

  /// إضافة عملية للمجموعة الحالية أو للمكدس مباشرة
  void addActionToGroup(UndoRedoAction action) {
    if (_currentGroup != null) {
      _currentGroup!.addAction(action);
    } else {
      addAction(action);
    }
  }

  // ===============================
  // معلومات التاريخ
  // ===============================

  /// الحصول على قائمة بجميع العمليات في مكدس التراجع
  List<String> getUndoHistory() {
    return _undoStack.map((action) => action.description).toList();
  }

  /// الحصول على قائمة بجميع العمليات في مكدس الإعادة
  List<String> getRedoHistory() {
    return _redoStack.map((action) => action.description).toList();
  }
}

// ===============================
// فئة العملية الأساسية
// ===============================

/// فئة أساسية لعمليات التراجع والإعادة
abstract class UndoRedoAction {
  final String description;
  final DateTime timestamp;

  UndoRedoAction(this.description) : timestamp = DateTime.now();

  /// تنفيذ التراجع
  Future<void> undo();

  /// تنفيذ الإعادة
  Future<void> redo();
}

// ===============================
// مجموعة العمليات
// ===============================

/// مجموعة من العمليات المترابطة
class UndoRedoGroup extends UndoRedoAction {
  final List<UndoRedoAction> actions = [];

  UndoRedoGroup(super.description);

  void addAction(UndoRedoAction action) {
    actions.add(action);
  }

  @override
  Future<void> undo() async {
    // تنفيذ التراجع بالعكس
    for (int i = actions.length - 1; i >= 0; i--) {
      await actions[i].undo();
    }
  }

  @override
  Future<void> redo() async {
    // تنفيذ الإعادة بالترتيب الطبيعي
    for (final action in actions) {
      await action.redo();
    }
  }
}

// ===============================
// عمليات محاسبية محددة
// ===============================

/// عملية إضافة/حذف حساب
class AccountAction extends UndoRedoAction {
  final Map<String, dynamic> accountData;
  final bool isAdd;
  final Function(Map<String, dynamic>) addFunction;
  final Function(int) deleteFunction;

  AccountAction({
    required String description,
    required this.accountData,
    required this.isAdd,
    required this.addFunction,
    required this.deleteFunction,
  }) : super(description);

  @override
  Future<void> undo() async {
    if (isAdd) {
      // إذا كانت العملية إضافة، نحذف عند التراجع
      await deleteFunction(accountData['id']);
    } else {
      // إذا كانت العملية حذف، نضيف عند التراجع
      await addFunction(accountData);
    }
  }

  @override
  Future<void> redo() async {
    if (isAdd) {
      // إعادة الإضافة
      await addFunction(accountData);
    } else {
      // إعادة الحذف
      await deleteFunction(accountData['id']);
    }
  }
}

/// عملية تحديث بيانات
class UpdateAction extends UndoRedoAction {
  final Map<String, dynamic> oldData;
  final Map<String, dynamic> newData;
  final Function(Map<String, dynamic>) updateFunction;

  UpdateAction({
    required String description,
    required this.oldData,
    required this.newData,
    required this.updateFunction,
  }) : super(description);

  @override
  Future<void> undo() async {
    await updateFunction(oldData);
  }

  @override
  Future<void> redo() async {
    await updateFunction(newData);
  }
}

/// عملية قيد محاسبي
class JournalEntryAction extends UndoRedoAction {
  final Map<String, dynamic> journalData;
  final bool isPost;
  final Function(Map<String, dynamic>) postFunction;
  final Function(int) unpostFunction;

  JournalEntryAction({
    required String description,
    required this.journalData,
    required this.isPost,
    required this.postFunction,
    required this.unpostFunction,
  }) : super(description);

  @override
  Future<void> undo() async {
    if (isPost) {
      // إلغاء الترحيل
      await unpostFunction(journalData['id']);
    } else {
      // إعادة الترحيل
      await postFunction(journalData);
    }
  }

  @override
  Future<void> redo() async {
    if (isPost) {
      // إعادة الترحيل
      await postFunction(journalData);
    } else {
      // إلغاء الترحيل
      await unpostFunction(journalData['id']);
    }
  }
}

/// عملية فاتورة
class InvoiceAction extends UndoRedoAction {
  final Map<String, dynamic> invoiceData;
  final String actionType; // 'create', 'update', 'delete', 'post'
  final Function(Map<String, dynamic>) createFunction;
  final Function(Map<String, dynamic>) updateFunction;
  final Function(int) deleteFunction;
  final Function(int) postFunction;
  final Map<String, dynamic>? oldData;

  InvoiceAction({
    required String description,
    required this.invoiceData,
    required this.actionType,
    required this.createFunction,
    required this.updateFunction,
    required this.deleteFunction,
    required this.postFunction,
    this.oldData,
  }) : super(description);

  @override
  Future<void> undo() async {
    switch (actionType) {
      case 'create':
        await deleteFunction(invoiceData['id']);
        break;
      case 'update':
        if (oldData != null) {
          await updateFunction(oldData!);
        }
        break;
      case 'delete':
        await createFunction(invoiceData);
        break;
      case 'post':
        // إلغاء ترحيل الفاتورة
        break;
    }
  }

  @override
  Future<void> redo() async {
    switch (actionType) {
      case 'create':
        await createFunction(invoiceData);
        break;
      case 'update':
        await updateFunction(invoiceData);
        break;
      case 'delete':
        await deleteFunction(invoiceData['id']);
        break;
      case 'post':
        await postFunction(invoiceData['id']);
        break;
    }
  }
}

/// عملية صنف
class ItemAction extends UndoRedoAction {
  final Map<String, dynamic> itemData;
  final String actionType;
  final Function(Map<String, dynamic>) createFunction;
  final Function(Map<String, dynamic>) updateFunction;
  final Function(int) deleteFunction;
  final Map<String, dynamic>? oldData;

  ItemAction({
    required String description,
    required this.itemData,
    required this.actionType,
    required this.createFunction,
    required this.updateFunction,
    required this.deleteFunction,
    this.oldData,
  }) : super(description);

  @override
  Future<void> undo() async {
    switch (actionType) {
      case 'create':
        await deleteFunction(itemData['id']);
        break;
      case 'update':
        if (oldData != null) {
          await updateFunction(oldData!);
        }
        break;
      case 'delete':
        await createFunction(itemData);
        break;
    }
  }

  @override
  Future<void> redo() async {
    switch (actionType) {
      case 'create':
        await createFunction(itemData);
        break;
      case 'update':
        await updateFunction(itemData);
        break;
      case 'delete':
        await deleteFunction(itemData['id']);
        break;
    }
  }
}

/// عملية عميل/مورد
class CustomerSupplierAction extends UndoRedoAction {
  final Map<String, dynamic> data;
  final String actionType;
  final String entityType; // 'customer' or 'supplier'
  final Function(Map<String, dynamic>) createFunction;
  final Function(Map<String, dynamic>) updateFunction;
  final Function(int) deleteFunction;
  final Map<String, dynamic>? oldData;

  CustomerSupplierAction({
    required String description,
    required this.data,
    required this.actionType,
    required this.entityType,
    required this.createFunction,
    required this.updateFunction,
    required this.deleteFunction,
    this.oldData,
  }) : super(description);

  @override
  Future<void> undo() async {
    switch (actionType) {
      case 'create':
        await deleteFunction(data['id']);
        break;
      case 'update':
        if (oldData != null) {
          await updateFunction(oldData!);
        }
        break;
      case 'delete':
        await createFunction(data);
        break;
    }
  }

  @override
  Future<void> redo() async {
    switch (actionType) {
      case 'create':
        await createFunction(data);
        break;
      case 'update':
        await updateFunction(data);
        break;
      case 'delete':
        await deleteFunction(data['id']);
        break;
    }
  }
}

/// عملية حركة مخزون
class InventoryMovementAction extends UndoRedoAction {
  final Map<String, dynamic> movementData;
  final Function(Map<String, dynamic>) addMovementFunction;
  final Function(int) reverseMovementFunction;

  InventoryMovementAction({
    required String description,
    required this.movementData,
    required this.addMovementFunction,
    required this.reverseMovementFunction,
  }) : super(description);

  @override
  Future<void> undo() async {
    // عكس حركة المخزون
    await reverseMovementFunction(movementData['id']);
  }

  @override
  Future<void> redo() async {
    // إعادة حركة المخزون
    await addMovementFunction(movementData);
  }
}
