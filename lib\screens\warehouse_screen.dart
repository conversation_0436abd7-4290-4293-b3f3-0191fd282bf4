/// شاشة المستودع - عرض جميع البضائع للجرد
/// تحتوي على إمكانيات البحث والفلترة والجرد
library;

import 'package:flutter/material.dart';
import '../models/item.dart';
import '../services/item_service.dart';
import '../services/inventory_invoice_integration_service.dart';
import '../services/performance_service.dart';
import '../database/database_helper.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class WarehouseScreen extends StatefulWidget {
  const WarehouseScreen({super.key});

  @override
  State<WarehouseScreen> createState() => _WarehouseScreenState();
}

class _WarehouseScreenState extends State<WarehouseScreen>
    with TickerProviderStateMixin {
  final ItemService _itemService = ItemService();
  final InventoryInvoiceIntegrationService _inventoryIntegration =
      InventoryInvoiceIntegrationService();
  final PerformanceService _performanceService = PerformanceService();
  final TextEditingController _searchController = TextEditingController();

  List<Item> _allItems = [];
  List<Item> _filteredItems = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, low_stock, out_of_stock, available
  String _selectedSort = 'name'; // name, quantity, code, value

  // إحصائيات المستودع
  Map<String, double> _warehouseSummary = {};

  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadWarehouseData();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadWarehouseData() async {
    setState(() => _isLoading = true);

    try {
      // استخدام خدمة الأداء لقياس وقت التحميل
      final items = await _performanceService.measureOperation(
        'load_warehouse_items',
        () => _itemService.getAllItems(),
        category: 'warehouse',
        metadata: {'screen': 'warehouse_screen'},
      );

      final summary = await _performanceService.measureOperation(
        'load_warehouse_summary',
        () => _itemService.getItemsSummary(),
        category: 'warehouse',
        metadata: {'screen': 'warehouse_screen'},
      );

      setState(() {
        _allItems = items;
        _filteredItems = items;
        _warehouseSummary = summary;
        _isLoading = false;
      });

      _applyFilters();

      // تسجيل إحصائيات المستودع
      _performanceService.updateMetric(
        'warehouse_items_count',
        items.length.toDouble(),
      );

      // تسجيل إحصائيات حالة المخزون
      final lowStockItems = items
          .where((item) => item.quantity <= item.minQuantity)
          .length;
      final outOfStockItems = items.where((item) => item.quantity <= 0).length;
      final activeItems = items.where((item) => item.isActive).length;

      _performanceService.updateMetric(
        'low_stock_items_count',
        lowStockItems.toDouble(),
      );
      _performanceService.updateMetric(
        'out_of_stock_items_count',
        outOfStockItems.toDouble(),
      );
      _performanceService.updateMetric(
        'active_items_count',
        activeItems.toDouble(),
      );

      // تسجيل القيمة الإجمالية للمخزون
      if (summary['totalValue'] != null) {
        _performanceService.updateMetric(
          'total_inventory_value',
          (summary['totalValue'] as num).toDouble(),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل بيانات المستودع: $e');
    }
  }

  void _applyFilters() {
    List<Item> filtered = List.from(_allItems);

    // تطبيق البحث النصي
    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      filtered = filtered.where((item) {
        return item.name.toLowerCase().contains(searchTerm) ||
            item.code.toLowerCase().contains(searchTerm) ||
            (item.description?.toLowerCase().contains(searchTerm) ?? false);
      }).toList();
    }

    // تطبيق الفلاتر
    switch (_selectedFilter) {
      case 'low_stock':
        filtered = filtered.where((item) => item.isLowStock).toList();
        break;
      case 'out_of_stock':
        filtered = filtered.where((item) => item.isOutOfStock).toList();
        break;
      case 'available':
        filtered = filtered.where((item) => item.quantity > 0).toList();
        break;
      case 'inactive':
        filtered = filtered.where((item) => !item.isActive).toList();
        break;
    }

    // تطبيق الترتيب
    switch (_selectedSort) {
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'quantity':
        filtered.sort((a, b) => b.quantity.compareTo(a.quantity));
        break;
      case 'code':
        filtered.sort((a, b) => a.code.compareTo(b.code));
        break;
      case 'value':
        filtered.sort(
          (a, b) =>
              (b.quantity * b.costPrice).compareTo(a.quantity * a.costPrice),
        );
        break;
    }

    setState(() {
      _filteredItems = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المستودع والجرد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.inventory),
            onPressed: _showInventoryDialog,
            tooltip: 'جرد المستودع',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printWarehouseReport,
            tooltip: 'طباعة تقرير المستودع',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadWarehouseData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchAndFilters(),
            _buildSummaryCards(),
            Expanded(
              child: _isLoading ? const LoadingWidget() : _buildItemsList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddItemDialog,
        backgroundColor: AppColors.primary,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('إضافة صنف', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الأصناف (الاسم، الكود، الوصف)',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _applyFilters();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (_) => _applyFilters(),
          ),
          ResponsiveSpacing.medium(),

          // فلاتر وترتيب
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: const InputDecoration(
                    labelText: 'فلترة حسب',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الأصناف')),
                    DropdownMenuItem(value: 'available', child: Text('متوفر')),
                    DropdownMenuItem(
                      value: 'low_stock',
                      child: Text('مخزون منخفض'),
                    ),
                    DropdownMenuItem(
                      value: 'out_of_stock',
                      child: Text('نفد المخزون'),
                    ),
                    DropdownMenuItem(value: 'inactive', child: Text('غير نشط')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedFilter = value!);
                    _applyFilters();
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSort,
                  decoration: const InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'name', child: Text('الاسم')),
                    DropdownMenuItem(value: 'code', child: Text('الكود')),
                    DropdownMenuItem(value: 'quantity', child: Text('الكمية')),
                    DropdownMenuItem(value: 'value', child: Text('القيمة')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedSort = value!);
                    _applyFilters();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الأصناف',
              _warehouseSummary['total_items']?.toInt().toString() ?? '0',
              Icons.inventory_2,
              AppColors.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'نفد المخزون',
              _warehouseSummary['out_of_stock']?.toInt().toString() ?? '0',
              Icons.warning,
              AppColors.error,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'مخزون منخفض',
              _warehouseSummary['low_stock']?.toInt().toString() ?? '0',
              Icons.trending_down,
              AppColors.warning,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'القيمة الإجمالية',
              '${(_warehouseSummary['total_cost_value'] ?? 0).toStringAsFixed(0)} ل.س',
              Icons.attach_money,
              AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          ResponsiveText.caption(title, textAlign: TextAlign.center),
          const SizedBox(height: 4),
          ResponsiveText.body(
            value,
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchController.text.isNotEmpty
                  ? 'لا توجد أصناف تطابق البحث'
                  : 'لا توجد أصناف في المستودع',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك إضافة أصناف جديدة باستخدام الزر أدناه',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return _buildItemCard(item);
      },
    );
  }

  Widget _buildItemCard(Item item) {
    final totalValue = item.quantity * item.costPrice;
    final stockColor = _getStockStatusColor(item);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showItemDetails(item),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: الاسم والحالة
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'كود: ${item.code}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: stockColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: stockColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      item.stockStatus,
                      style: TextStyle(
                        color: stockColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثاني: معلومات المخزون
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'الكمية',
                      '${item.displayQuantity} ${item.unit}',
                      Icons.inventory,
                      stockColor,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'سعر التكلفة',
                      '${item.displayCostPrice} ل.س',
                      Icons.price_change,
                      AppColors.info,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'القيمة الإجمالية',
                      '${totalValue.toStringAsFixed(2)} ل.س',
                      Icons.attach_money,
                      AppColors.success,
                    ),
                  ),
                ],
              ),

              if (item.description != null && item.description!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  item.description!,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],

              const SizedBox(height: 12),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _showInventoryAdjustmentDialog(item),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('تعديل الكمية'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _showEditItemDialog(item),
                    icon: const Icon(Icons.edit_note, size: 16),
                    label: const Text('تعديل'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.info,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Color _getStockStatusColor(Item item) {
    if (item.isOutOfStock) {
      return AppColors.error;
    } else if (item.isLowStock) {
      return AppColors.warning;
    } else {
      return AppColors.success;
    }
  }

  // ===============================
  // حوارات ووظائف إضافية
  // ===============================

  void _showItemDetails(Item item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الصنف: ${item.name}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 500,
          child: DefaultTabController(
            length: 2,
            child: Column(
              children: [
                const TabBar(
                  tabs: [
                    Tab(text: 'المعلومات الأساسية'),
                    Tab(text: 'حركات المخزون'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      // تبويب المعلومات الأساسية
                      SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildDetailRow('الكود', item.code),
                            _buildDetailRow('الاسم', item.name),
                            _buildDetailRow(
                              'الوصف',
                              item.description ?? 'غير محدد',
                            ),
                            _buildDetailRow('الوحدة', item.unit),
                            _buildDetailRow(
                              'الكمية المتوفرة',
                              '${item.displayQuantity} ${item.unit}',
                            ),
                            _buildDetailRow(
                              'الحد الأدنى',
                              '${item.minQuantity} ${item.unit}',
                            ),
                            _buildDetailRow(
                              'سعر التكلفة',
                              '${item.displayCostPrice} ل.س',
                            ),
                            _buildDetailRow(
                              'سعر البيع',
                              '${item.displaySellingPrice} ل.س',
                            ),
                            _buildDetailRow(
                              'هامش الربح',
                              '${item.profitMargin.toStringAsFixed(1)}%',
                            ),
                            _buildDetailRow(
                              'القيمة الإجمالية',
                              '${(item.quantity * item.costPrice).toStringAsFixed(2)} ل.س',
                            ),
                            _buildDetailRow('حالة المخزون', item.stockStatus),
                            _buildDetailRow('الحالة', item.statusArabic),
                            _buildDetailRow(
                              'تاريخ الإنشاء',
                              '${item.createdAt.day}/${item.createdAt.month}/${item.createdAt.year}',
                            ),
                          ],
                        ),
                      ),
                      // تبويب حركات المخزون
                      _buildInventoryMovementsTab(item),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showEditItemDialog(item);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildInventoryMovementsTab(Item item) {
    return FutureBuilder(
      future: _inventoryIntegration.getInventoryMovements(item.id!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل حركات المخزون',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  '${snapshot.error}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final movements = snapshot.data ?? [];

        if (movements.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.history, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد حركات مخزون',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر هنا جميع حركات المخزون للصنف',
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: movements.length,
          itemBuilder: (context, index) {
            final movement = movements[index];
            return _buildMovementCard(movement);
          },
        );
      },
    );
  }

  Widget _buildMovementCard(movement) {
    final isIncoming = movement.movementType == 'in';
    final color = isIncoming ? AppColors.success : AppColors.error;
    final icon = isIncoming ? Icons.add_circle : Icons.remove_circle;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          movement.description ?? 'حركة مخزون',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('النوع: ${_getMovementTypeArabic(movement.movementType)}'),
            Text('التاريخ: ${_formatDate(movement.movementDate)}'),
            if (movement.referenceType != null)
              Text(
                'المرجع: ${movement.referenceType} #${movement.referenceId ?? ''}',
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${isIncoming ? '+' : '-'}${movement.quantity}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 16,
              ),
            ),
            Text(
              '${movement.totalCost.toStringAsFixed(2)} ل.س',
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getMovementTypeArabic(String type) {
    switch (type) {
      case 'in':
        return 'وارد';
      case 'out':
        return 'صادر';
      case 'adjustment':
        return 'تعديل';
      default:
        return type;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تسجيل حركة مخزون يدوية
  Future<void> _recordManualInventoryMovement({
    required int itemId,
    required String movementType,
    required double quantity,
    required double unitCost,
    required String description,
  }) async {
    try {
      final db = await DatabaseHelper().database;

      // إنشاء جدول حركات المخزون إذا لم يكن موجوداً
      await db.execute('''
        CREATE TABLE IF NOT EXISTS inventory_movements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          movement_type TEXT NOT NULL,
          quantity REAL NOT NULL,
          unit_cost REAL NOT NULL,
          total_cost REAL NOT NULL,
          reference_type TEXT,
          reference_id INTEGER,
          description TEXT,
          movement_date TEXT NOT NULL,
          created_at TEXT NOT NULL,
          FOREIGN KEY (item_id) REFERENCES items (id)
        )
      ''');

      // إدراج حركة المخزون
      await db.insert('inventory_movements', {
        'item_id': itemId,
        'movement_type': movementType,
        'quantity': quantity,
        'unit_cost': unitCost,
        'total_cost': quantity * unitCost,
        'reference_type': 'adjustment',
        'reference_id': null,
        'description': description,
        'movement_date': DateTime.now().toIso8601String().split('T')[0],
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('خطأ في تسجيل حركة المخزون: $e');
    }
  }

  void _showAddItemDialog() {
    _showItemFormDialog();
  }

  void _showEditItemDialog(Item item) {
    _showItemFormDialog(item: item);
  }

  void _showItemFormDialog({Item? item}) {
    final isEditing = item != null;
    final codeController = TextEditingController(text: item?.code ?? '');
    final nameController = TextEditingController(text: item?.name ?? '');
    final unitController = TextEditingController(text: item?.unit ?? '');
    final quantityController = TextEditingController(
      text: item?.quantity.toString() ?? '0',
    );
    final minQuantityController = TextEditingController(
      text: item?.minQuantity.toString() ?? '0',
    );
    final costPriceController = TextEditingController(
      text: item?.costPrice.toString() ?? '0',
    );
    final sellingPriceController = TextEditingController(
      text: item?.sellingPrice.toString() ?? '0',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'تعديل الصنف' : 'إضافة صنف جديد'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: codeController,
                  decoration: const InputDecoration(
                    labelText: 'كود الصنف',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الصنف',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: unitController,
                  decoration: const InputDecoration(
                    labelText: 'الوحدة',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: quantityController,
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: minQuantityController,
                  decoration: const InputDecoration(
                    labelText: 'الحد الأدنى',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: costPriceController,
                  decoration: const InputDecoration(
                    labelText: 'سعر التكلفة',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: sellingPriceController,
                  decoration: const InputDecoration(
                    labelText: 'سعر البيع',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              if (nameController.text.trim().isEmpty ||
                  codeController.text.trim().isEmpty) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('يرجى ملء جميع الحقول المطلوبة'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              try {
                final newItem = Item(
                  id: item?.id,
                  code: codeController.text.trim(),
                  name: nameController.text.trim(),
                  unit: unitController.text.trim(),
                  quantity: double.tryParse(quantityController.text) ?? 0,
                  minQuantity: double.tryParse(minQuantityController.text) ?? 0,
                  costPrice: double.tryParse(costPriceController.text) ?? 0,
                  sellingPrice:
                      double.tryParse(sellingPriceController.text) ?? 0,
                  currencyId: 1, // الليرة السورية
                );

                if (isEditing) {
                  await _itemService.updateItem(newItem);
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث الصنف بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else {
                  await _itemService.insertItem(newItem);
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة الصنف بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }

                navigator.pop();
                _loadWarehouseData();
              } catch (e) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('حدث خطأ: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text(isEditing ? 'تحديث' : 'إضافة'),
          ),
        ],
      ),
    );
  }

  void _showInventoryAdjustmentDialog(Item item) {
    final quantityController = TextEditingController();
    String adjustmentType = 'add'; // add, subtract, set
    String reason = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('تعديل كمية: ${item.name}'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('الكمية الحالية: ${item.displayQuantity} ${item.unit}'),
                const SizedBox(height: 16),

                DropdownButtonFormField<String>(
                  value: adjustmentType,
                  decoration: const InputDecoration(
                    labelText: 'نوع التعديل',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'add', child: Text('إضافة كمية')),
                    DropdownMenuItem(
                      value: 'subtract',
                      child: Text('خصم كمية'),
                    ),
                    DropdownMenuItem(
                      value: 'set',
                      child: Text('تحديد كمية جديدة'),
                    ),
                  ],
                  onChanged: (value) => setState(() => adjustmentType = value!),
                ),

                const SizedBox(height: 16),

                TextField(
                  controller: quantityController,
                  decoration: InputDecoration(
                    labelText: adjustmentType == 'set'
                        ? 'الكمية الجديدة'
                        : 'الكمية',
                    border: const OutlineInputBorder(),
                    suffixText: item.unit,
                  ),
                  keyboardType: TextInputType.number,
                ),

                const SizedBox(height: 16),

                TextField(
                  decoration: const InputDecoration(
                    labelText: 'سبب التعديل',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => reason = value,
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _processInventoryAdjustment(
                context,
                item,
                quantityController.text,
                adjustmentType,
                reason,
              ),
              child: const Text('تطبيق'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _processInventoryAdjustment(
    BuildContext context,
    Item item,
    String quantityText,
    String adjustmentType,
    String reason,
  ) async {
    final quantity = double.tryParse(quantityText);
    if (quantity == null || quantity <= 0) {
      _showErrorSnackBar('يرجى إدخال كمية صحيحة');
      return;
    }

    try {
      double newQuantity = item.quantity;
      double adjustmentQuantity = 0;
      String movementType = 'adjustment';

      switch (adjustmentType) {
        case 'add':
          newQuantity += quantity;
          adjustmentQuantity = quantity;
          movementType = 'in';
          break;
        case 'subtract':
          newQuantity -= quantity;
          if (newQuantity < 0) {
            _showErrorSnackBar('لا يمكن أن تكون الكمية أقل من الصفر');
            return;
          }
          adjustmentQuantity = quantity;
          movementType = 'out';
          break;
        case 'set':
          adjustmentQuantity = (quantity - item.quantity).abs();
          movementType = quantity > item.quantity ? 'in' : 'out';
          newQuantity = quantity;
          break;
      }

      // تحديث الصنف
      final updatedItem = item.copyWith(
        quantity: newQuantity,
        updatedAt: DateTime.now(),
      );

      await _itemService.updateItem(updatedItem);

      // تسجيل حركة المخزون يدوياً
      await _recordManualInventoryMovement(
        itemId: item.id!,
        movementType: movementType,
        quantity: adjustmentQuantity,
        unitCost: item.costPrice,
        description: reason.isNotEmpty ? reason : 'تعديل يدوي للمخزون',
      );

      if (context.mounted) {
        Navigator.pop(context);
      }

      _showSuccessSnackBar('تم تحديث الكمية وتسجيل الحركة بنجاح');
      _loadWarehouseData();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث الكمية: $e');
    }
  }

  void _showInventoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('جرد المستودع'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.inventory, size: 64, color: AppColors.primary),
            SizedBox(height: 16),
            Text(
              'هل تريد إجراء جرد شامل للمستودع؟',
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'سيتم إنشاء تقرير مفصل بجميع الأصناف وكمياتها',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _generateInventoryReport();
            },
            child: const Text('إجراء الجرد'),
          ),
        ],
      ),
    );
  }

  void _generateInventoryReport() {
    _showInventoryReportDialog();
  }

  void _showInventoryReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تقرير جرد المستودع'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              // إحصائيات سريعة
              Card(
                color: AppColors.primary.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      Text(
                        'إحصائيات المستودع',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Column(
                            children: [
                              Text(
                                '${_allItems.length}',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const Text('إجمالي الأصناف'),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                _warehouseSummary['totalValue']
                                        ?.toStringAsFixed(0) ??
                                    '0',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      color: AppColors.success,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const Text('القيمة الإجمالية'),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                '${_allItems.where((item) => item.quantity <= item.minQuantity).length}',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      color: AppColors.error,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const Text('أصناف منخفضة'),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // قائمة الأصناف
              Expanded(
                child: ListView.builder(
                  itemCount: _allItems.length,
                  itemBuilder: (context, index) {
                    final item = _allItems[index];
                    final isLowStock = item.quantity <= item.minQuantity;
                    final itemValue = item.quantity * item.costPrice;

                    return Card(
                      color: isLowStock
                          ? AppColors.error.withValues(alpha: 0.1)
                          : null,
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isLowStock
                              ? AppColors.error
                              : AppColors.primary,
                          child: Text(
                            item.code.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(
                          item.name,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: isLowStock ? AppColors.error : null,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('الكود: ${item.code}'),
                            Text('الكمية: ${item.quantity} ${item.unit}'),
                            if (isLowStock)
                              Text(
                                'تحذير: الكمية أقل من الحد الأدنى (${item.minQuantity})',
                                style: const TextStyle(
                                  color: AppColors.error,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${itemValue.toStringAsFixed(0)} ل.س',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppColors.success,
                              ),
                            ),
                            Text(
                              '${item.costPrice.toStringAsFixed(0)}/وحدة',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _exportInventoryReport();
            },
            child: const Text('تصدير التقرير'),
          ),
        ],
      ),
    );
  }

  void _exportInventoryReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة تصدير التقرير قريباً'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _printWarehouseReport() {
    _showInventoryReportDialog();
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
