/// نموذج رصيد الصنف في الموقع
/// يتتبع كمية كل صنف في كل موقع مع تفاصيل إضافية
library;

class ItemLocationStock {
  final int? id;
  final int itemId;
  final int warehouseId;
  final int locationId;
  final double quantity;
  final double reservedQuantity; // الكمية المحجوزة
  final double availableQuantity; // الكمية المتاحة
  final double averageCost; // متوسط التكلفة
  final DateTime lastMovementDate; // تاريخ آخر حركة
  final DateTime createdAt;
  final DateTime updatedAt;

  ItemLocationStock({
    this.id,
    required this.itemId,
    required this.warehouseId,
    required this.locationId,
    this.quantity = 0.0,
    this.reservedQuantity = 0.0,
    double? availableQuantity,
    this.averageCost = 0.0,
    DateTime? lastMovementDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : availableQuantity = availableQuantity ?? (quantity - reservedQuantity),
        lastMovementDate = lastMovementDate ?? DateTime.now(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'quantity': quantity,
      'reserved_quantity': reservedQuantity,
      'available_quantity': availableQuantity,
      'average_cost': averageCost,
      'last_movement_date': lastMovementDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory ItemLocationStock.fromMap(Map<String, dynamic> map) {
    return ItemLocationStock(
      id: map['id']?.toInt(),
      itemId: map['item_id']?.toInt() ?? 0,
      warehouseId: map['warehouse_id']?.toInt() ?? 0,
      locationId: map['location_id']?.toInt() ?? 0,
      quantity: map['quantity']?.toDouble() ?? 0.0,
      reservedQuantity: map['reserved_quantity']?.toDouble() ?? 0.0,
      availableQuantity: map['available_quantity']?.toDouble(),
      averageCost: map['average_cost']?.toDouble() ?? 0.0,
      lastMovementDate: DateTime.parse(map['last_movement_date'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// نسخ مع تعديل
  ItemLocationStock copyWith({
    int? id,
    int? itemId,
    int? warehouseId,
    int? locationId,
    double? quantity,
    double? reservedQuantity,
    double? availableQuantity,
    double? averageCost,
    DateTime? lastMovementDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ItemLocationStock(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      quantity: quantity ?? this.quantity,
      reservedQuantity: reservedQuantity ?? this.reservedQuantity,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      averageCost: averageCost ?? this.averageCost,
      lastMovementDate: lastMovementDate ?? this.lastMovementDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ItemLocationStock(itemId: $itemId, locationId: $locationId, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ItemLocationStock && 
           other.itemId == itemId && 
           other.locationId == locationId;
  }

  @override
  int get hashCode {
    return Object.hash(itemId, locationId);
  }

  // خصائص مساعدة
  bool get hasStock {
    return quantity > 0;
  }

  bool get hasAvailableStock {
    return availableQuantity > 0;
  }

  bool get hasReservedStock {
    return reservedQuantity > 0;
  }

  double get totalValue {
    return quantity * averageCost;
  }

  double get availableValue {
    return availableQuantity * averageCost;
  }

  /// حجز كمية
  ItemLocationStock reserveQuantity(double quantityToReserve) {
    if (quantityToReserve > availableQuantity) {
      throw Exception('الكمية المطلوب حجزها أكبر من الكمية المتاحة');
    }
    
    return copyWith(
      reservedQuantity: reservedQuantity + quantityToReserve,
      availableQuantity: availableQuantity - quantityToReserve,
      updatedAt: DateTime.now(),
    );
  }

  /// إلغاء حجز كمية
  ItemLocationStock unreserveQuantity(double quantityToUnreserve) {
    if (quantityToUnreserve > reservedQuantity) {
      throw Exception('الكمية المطلوب إلغاء حجزها أكبر من الكمية المحجوزة');
    }
    
    return copyWith(
      reservedQuantity: reservedQuantity - quantityToUnreserve,
      availableQuantity: availableQuantity + quantityToUnreserve,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث الكمية
  ItemLocationStock updateQuantity(double newQuantity, double newAverageCost) {
    return copyWith(
      quantity: newQuantity,
      availableQuantity: newQuantity - reservedQuantity,
      averageCost: newAverageCost,
      lastMovementDate: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إضافة كمية
  ItemLocationStock addQuantity(double quantityToAdd, double unitCost) {
    final newQuantity = quantity + quantityToAdd;
    final newAverageCost = newQuantity > 0 
        ? ((quantity * averageCost) + (quantityToAdd * unitCost)) / newQuantity
        : unitCost;
    
    return copyWith(
      quantity: newQuantity,
      availableQuantity: availableQuantity + quantityToAdd,
      averageCost: newAverageCost,
      lastMovementDate: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// خصم كمية
  ItemLocationStock subtractQuantity(double quantityToSubtract) {
    if (quantityToSubtract > availableQuantity) {
      throw Exception('الكمية المطلوب خصمها أكبر من الكمية المتاحة');
    }
    
    return copyWith(
      quantity: quantity - quantityToSubtract,
      availableQuantity: availableQuantity - quantityToSubtract,
      lastMovementDate: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}

/// نموذج ملخص المخزون لصنف معين
class ItemStockSummary {
  final int itemId;
  final double totalQuantity;
  final double totalReservedQuantity;
  final double totalAvailableQuantity;
  final double weightedAverageCost;
  final double totalValue;
  final List<ItemLocationStock> locationStocks;

  ItemStockSummary({
    required this.itemId,
    required this.totalQuantity,
    required this.totalReservedQuantity,
    required this.totalAvailableQuantity,
    required this.weightedAverageCost,
    required this.totalValue,
    required this.locationStocks,
  });

  factory ItemStockSummary.fromLocationStocks(int itemId, List<ItemLocationStock> stocks) {
    final totalQuantity = stocks.fold<double>(0, (sum, stock) => sum + stock.quantity);
    final totalReserved = stocks.fold<double>(0, (sum, stock) => sum + stock.reservedQuantity);
    final totalAvailable = stocks.fold<double>(0, (sum, stock) => sum + stock.availableQuantity);
    final totalValue = stocks.fold<double>(0, (sum, stock) => sum + stock.totalValue);
    final weightedAverage = totalQuantity > 0 ? totalValue / totalQuantity : 0.0;

    return ItemStockSummary(
      itemId: itemId,
      totalQuantity: totalQuantity,
      totalReservedQuantity: totalReserved,
      totalAvailableQuantity: totalAvailable,
      weightedAverageCost: weightedAverage,
      totalValue: totalValue,
      locationStocks: stocks,
    );
  }

  bool get hasStock => totalQuantity > 0;
  bool get hasAvailableStock => totalAvailableQuantity > 0;
  bool get hasReservedStock => totalReservedQuantity > 0;
  int get locationCount => locationStocks.length;
}
