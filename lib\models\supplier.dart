class Supplier {
  final int? id;
  final String code;
  final String name;
  final String? phone;
  final String? email;
  final String? address;
  final double balance;
  final int currencyId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Supplier({
    this.id,
    required this.code,
    required this.name,
    this.phone,
    this.email,
    this.address,
    this.balance = 0.0,
    required this.currencyId,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'balance': balance,
      'currency_id': currencyId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      balance: map['balance']?.toDouble() ?? 0.0,
      currencyId: map['currency_id']?.toInt() ?? 1,
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Supplier copyWith({
    int? id,
    String? code,
    String? name,
    String? phone,
    String? email,
    String? address,
    double? balance,
    int? currencyId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Supplier(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      balance: balance ?? this.balance,
      currencyId: currencyId ?? this.currencyId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Supplier(id: $id, code: $code, name: $name, balance: $balance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Supplier && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  String get displayBalance {
    return balance.toStringAsFixed(2);
  }

  bool get hasDebt {
    return balance < 0; // للموردين: الرصيد السالب يعني دين علينا
  }

  bool get hasCredit {
    return balance > 0; // للموردين: الرصيد الموجب يعني دفعنا أكثر من المطلوب
  }

  double get debtAmount {
    return balance < 0 ? balance.abs() : 0.0;
  }

  double get creditAmount {
    return balance > 0 ? balance : 0.0;
  }

  String get balanceStatus {
    if (balance < 0) {
      return 'دائن'; // نحن مدينون للمورد
    } else if (balance > 0) {
      return 'مدين'; // المورد مدين لنا
    } else {
      return 'متوازن';
    }
  }

  String get statusArabic {
    return isActive ? 'نشط' : 'غير نشط';
  }
}
