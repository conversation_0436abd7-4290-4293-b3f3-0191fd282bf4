import 'package:flutter/material.dart';
import 'form_validators.dart';
import '../services/error_message_service.dart';
import '../services/data_protection_service.dart';

/// مساعد النماذج - يوفر دوال مساعدة لإنشاء وإدارة النماذج
class FormHelper {
  // ===============================
  // إنشاء حقول النماذج
  // ===============================

  /// إنشاء حقل نص مع تحقق مدمج
  static Widget buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String? Function(String?) validator,
    String? hintText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconPressed,
    TextInputType keyboardType = TextInputType.text,
    int? maxLength,
    int maxLines = 1,
    bool enabled = true,
    bool obscureText = false,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      maxLength: maxLength,
      maxLines: maxLines,
      enabled: enabled,
      obscureText: obscureText,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: const OutlineInputBorder(),
        prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
        suffixIcon: suffixIcon != null
            ? IconButton(icon: Icon(suffixIcon), onPressed: onSuffixIconPressed)
            : null,
      ),
    );
  }

  /// إنشاء حقل كود مع تحقق مخصص
  static Widget buildCodeField({
    required TextEditingController controller,
    required String codeType,
    required String labelText,
    bool enabled = true,
    VoidCallback? onGeneratePressed,
  }) {
    String? Function(String?) validator;

    switch (codeType.toLowerCase()) {
      case 'account':
        validator = FormValidators.accountCode;
        break;
      case 'customer':
        validator = FormValidators.customerCode;
        break;
      case 'supplier':
        validator = FormValidators.supplierCode;
        break;
      case 'item':
        validator = FormValidators.itemCode;
        break;
      default:
        validator = (value) => FormValidators.required(value, labelText);
    }

    return buildTextFormField(
      controller: controller,
      labelText: labelText,
      validator: validator,
      prefixIcon: Icons.numbers,
      suffixIcon: onGeneratePressed != null ? Icons.auto_awesome : null,
      onSuffixIconPressed: onGeneratePressed,
      enabled: enabled,
      keyboardType: TextInputType.text,
    );
  }

  /// إنشاء حقل مبلغ مالي
  static Widget buildAmountField({
    required TextEditingController controller,
    required String labelText,
    bool allowZero = true,
    bool enabled = true,
    String? hintText,
  }) {
    return buildTextFormField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      validator: (value) => FormValidators.amount(value, allowZero: allowZero),
      prefixIcon: Icons.attach_money,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      enabled: enabled,
    );
  }

  /// إنشاء حقل كمية
  static Widget buildQuantityField({
    required TextEditingController controller,
    required String labelText,
    bool allowZero = false,
    bool enabled = true,
    String? hintText,
  }) {
    return buildTextFormField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      validator: (value) =>
          FormValidators.quantity(value, allowZero: allowZero),
      prefixIcon: Icons.inventory,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      enabled: enabled,
    );
  }

  /// إنشاء حقل هاتف
  static Widget buildPhoneField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return buildTextFormField(
      controller: controller,
      labelText: 'رقم الهاتف',
      hintText: '0933123456',
      validator: FormValidators.phoneNumber,
      prefixIcon: Icons.phone,
      keyboardType: TextInputType.phone,
      enabled: enabled,
    );
  }

  /// إنشاء حقل بريد إلكتروني
  static Widget buildEmailField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return buildTextFormField(
      controller: controller,
      labelText: 'البريد الإلكتروني',
      hintText: '<EMAIL>',
      validator: FormValidators.email,
      prefixIcon: Icons.email,
      keyboardType: TextInputType.emailAddress,
      enabled: enabled,
    );
  }

  // ===============================
  // إدارة النماذج
  // ===============================

  /// التحقق من صحة النموذج وعرض الأخطاء
  static bool validateFormWithFeedback(
    GlobalKey<FormState> formKey,
    BuildContext context,
  ) {
    if (!formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تصحيح الأخطاء في النموذج'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
    return true;
  }

  /// حفظ النموذج مع معالجة الأخطاء
  static Future<bool> saveFormWithErrorHandling(
    BuildContext context,
    Future<void> Function() saveFunction, {
    String? successMessage,
  }) async {
    try {
      await saveFunction();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              successMessage ?? ErrorMessageService.saveSuccessMessage,
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      return true;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(ErrorMessageService.getErrorMessage(e)),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  // ===============================
  // تنظيف البيانات قبل الحفظ
  // ===============================

  /// تنظيف بيانات النموذج قبل الحفظ
  static Map<String, String> sanitizeFormData(
    Map<String, TextEditingController> controllers, {
    ProtectionLevel level = ProtectionLevel.standard,
  }) {
    final sanitizedData = <String, String>{};

    for (final entry in controllers.entries) {
      final fieldName = entry.key;
      final controller = entry.value;

      sanitizedData[fieldName] = DataProtectionService.protectedSanitizeText(
        controller.text,
        fieldName,
        level: level,
      );
    }

    return sanitizedData;
  }

  /// تطبيق البيانات المنظفة على المتحكمات
  static void applySanitizedData(
    Map<String, TextEditingController> controllers,
    Map<String, String> sanitizedData,
  ) {
    for (final entry in sanitizedData.entries) {
      final fieldName = entry.key;
      final sanitizedValue = entry.value;

      if (controllers.containsKey(fieldName)) {
        controllers[fieldName]!.text = sanitizedValue;
      }
    }
  }

  // ===============================
  // دوال مساعدة للتحقق
  // ===============================

  /// التحقق من تطابق كلمتي المرور
  static String? validatePasswordConfirmation(
    String? password,
    String? confirmPassword,
  ) {
    if (password != confirmPassword) {
      return 'كلمتا المرور غير متطابقتان';
    }
    return null;
  }

  /// التحقق من قوة كلمة المرور
  static String? validatePasswordStrength(String? password) {
    if (password == null || password.isEmpty) {
      return ErrorMessageService.getRequiredFieldMessage('كلمة المرور');
    }

    if (password.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }

    return null;
  }

  // ===============================
  // دوال مساعدة للواجهة
  // ===============================

  /// إنشاء مسافة بين الحقول
  static Widget buildFieldSpacing({double height = 16.0}) {
    return SizedBox(height: height);
  }

  /// إنشاء عنوان قسم
  static Widget buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  /// إنشاء بطاقة للقسم
  static Widget buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [buildSectionTitle(title), ...children],
        ),
      ),
    );
  }
}
