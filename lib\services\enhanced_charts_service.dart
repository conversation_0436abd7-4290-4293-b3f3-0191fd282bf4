import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import '../models/report_models.dart';
import '../constants/app_colors.dart';
import '../services/logging_service.dart';

/// خدمة الرسوم البيانية المحسنة
/// توفر رسوم بيانية متقدمة وتفاعلية للتقارير المالية
class EnhancedChartsService {
  /// إنشاء رسم بياني دائري لميزان المراجعة
  static Widget createTrialBalancePieChart(
    List<TrialBalanceItem> data, {
    bool showLegend = true,
    bool showPercentages = true,
    double? width,
    double? height,
  }) {
    try {
      // تجميع البيانات حسب نوع الحساب
      final Map<String, double> accountTypeBalances = {};

      for (final item in data) {
        final balance = item.closingBalance.abs();
        if (balance > 0) {
          final type = _getAccountTypeDisplayName(item.type);
          accountTypeBalances[type] =
              (accountTypeBalances[type] ?? 0) + balance;
        }
      }

      if (accountTypeBalances.isEmpty) {
        return const Center(child: Text('لا توجد بيانات للعرض'));
      }

      final chartData = accountTypeBalances.entries
          .map((entry) => ChartData(entry.key, entry.value))
          .toList();

      return SizedBox(
        width: width,
        height: height ?? 300,
        child: SfCircularChart(
          title: ChartTitle(text: 'توزيع الأرصدة حسب نوع الحساب'),
          legend: Legend(
            isVisible: showLegend,
            position: LegendPosition.bottom,
          ),
          series: <CircularSeries>[
            PieSeries<ChartData, String>(
              dataSource: chartData,
              xValueMapper: (ChartData data, _) => data.category,
              yValueMapper: (ChartData data, _) => data.value,
              dataLabelMapper: (ChartData data, _) => showPercentages
                  ? '${data.category}\n${((data.value / chartData.fold(0.0, (sum, item) => sum + item.value)) * 100).toStringAsFixed(1)}%'
                  : data.category,
              dataLabelSettings: const DataLabelSettings(
                isVisible: true,
                labelPosition: ChartDataLabelPosition.outside,
              ),
              enableTooltip: true,
              pointColorMapper: (ChartData data, index) =>
                  _getChartColor(index),
            ),
          ],
          tooltipBehavior: TooltipBehavior(
            enable: true,
            format: 'point.x: point.y',
          ),
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الرسم البياني الدائري',
        category: 'EnhancedCharts',
        data: {'error': e.toString()},
      );
      return const Center(child: Text('خطأ في عرض الرسم البياني'));
    }
  }

  /// إنشاء رسم بياني عمودي للإيرادات والمصروفات
  static Widget createRevenueExpenseBarChart(
    List<MonthlyFinancialData> data, {
    bool showLegend = true,
    double? width,
    double? height,
  }) {
    try {
      if (data.isEmpty) {
        return const Center(child: Text('لا توجد بيانات للعرض'));
      }

      return SizedBox(
        width: width,
        height: height ?? 400,
        child: SfCartesianChart(
          title: ChartTitle(text: 'الإيرادات والمصروفات الشهرية'),
          legend: Legend(isVisible: showLegend),
          primaryXAxis: CategoryAxis(title: AxisTitle(text: 'الشهر')),
          primaryYAxis: NumericAxis(
            title: AxisTitle(text: 'المبلغ'),
            numberFormat: NumberFormat.currency(locale: 'ar', symbol: ''),
          ),
          series: <CartesianSeries>[
            ColumnSeries<MonthlyFinancialData, String>(
              name: 'الإيرادات',
              dataSource: data,
              xValueMapper: (MonthlyFinancialData data, _) => data.monthName,
              yValueMapper: (MonthlyFinancialData data, _) => data.revenue,
              color: AppColors.success,
              dataLabelSettings: const DataLabelSettings(isVisible: true),
            ),
            ColumnSeries<MonthlyFinancialData, String>(
              name: 'المصروفات',
              dataSource: data,
              xValueMapper: (MonthlyFinancialData data, _) => data.monthName,
              yValueMapper: (MonthlyFinancialData data, _) => data.expense,
              color: AppColors.error,
              dataLabelSettings: const DataLabelSettings(isVisible: true),
            ),
          ],
          tooltipBehavior: TooltipBehavior(enable: true),
          zoomPanBehavior: ZoomPanBehavior(
            enablePinching: true,
            enablePanning: true,
            enableDoubleTapZooming: true,
          ),
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الرسم البياني العمودي',
        category: 'EnhancedCharts',
        data: {'error': e.toString()},
      );
      return const Center(child: Text('خطأ في عرض الرسم البياني'));
    }
  }

  /// إنشاء رسم بياني خطي لتطور الأرباح
  static Widget createProfitTrendLineChart(
    List<MonthlyFinancialData> data, {
    bool showDataPoints = true,
    double? width,
    double? height,
  }) {
    try {
      if (data.isEmpty) {
        return const Center(child: Text('لا توجد بيانات للعرض'));
      }

      return SizedBox(
        width: width,
        height: height ?? 350,
        child: SfCartesianChart(
          title: ChartTitle(text: 'تطور صافي الربح'),
          primaryXAxis: CategoryAxis(title: AxisTitle(text: 'الشهر')),
          primaryYAxis: NumericAxis(
            title: AxisTitle(text: 'صافي الربح'),
            numberFormat: NumberFormat.currency(locale: 'ar', symbol: ''),
          ),
          series: <CartesianSeries>[
            LineSeries<MonthlyFinancialData, String>(
              name: 'صافي الربح',
              dataSource: data,
              xValueMapper: (MonthlyFinancialData data, _) => data.monthName,
              yValueMapper: (MonthlyFinancialData data, _) => data.netProfit,
              color: AppColors.primary,
              width: 3,
              markerSettings: MarkerSettings(
                isVisible: showDataPoints,
                shape: DataMarkerType.circle,
                width: 8,
                height: 8,
              ),
              dataLabelSettings: const DataLabelSettings(isVisible: true),
            ),
          ],
          tooltipBehavior: TooltipBehavior(enable: true),
          zoomPanBehavior: ZoomPanBehavior(
            enablePinching: true,
            enablePanning: true,
            enableDoubleTapZooming: true,
          ),
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الرسم البياني الخطي',
        category: 'EnhancedCharts',
        data: {'error': e.toString()},
      );
      return const Center(child: Text('خطأ في عرض الرسم البياني'));
    }
  }

  /// إنشاء رسم بياني للمخزون
  static Widget createInventoryChart(
    List<InventoryReportItem> data, {
    InventoryChartType chartType = InventoryChartType.stockLevels,
    double? width,
    double? height,
  }) {
    try {
      if (data.isEmpty) {
        return const Center(child: Text('لا توجد بيانات للعرض'));
      }

      switch (chartType) {
        case InventoryChartType.stockLevels:
          return _createStockLevelsChart(data, width, height);
        case InventoryChartType.stockValue:
          return _createStockValueChart(data, width, height);
        case InventoryChartType.lowStock:
          return _createLowStockChart(data, width, height);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء رسم بياني المخزون',
        category: 'EnhancedCharts',
        data: {'error': e.toString()},
      );
      return const Center(child: Text('خطأ في عرض الرسم البياني'));
    }
  }

  /// تحويل الرسم البياني إلى صورة
  static Future<Uint8List?> chartToImage(GlobalKey chartKey) async {
    try {
      final RenderRepaintBoundary boundary =
          chartKey.currentContext!.findRenderObject() as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      return byteData?.buffer.asUint8List();
    } catch (e) {
      LoggingService.error(
        'خطأ في تحويل الرسم البياني إلى صورة',
        category: 'EnhancedCharts',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء رسم بياني لمستويات المخزون
  static Widget _createStockLevelsChart(
    List<InventoryReportItem> data,
    double? width,
    double? height,
  ) {
    final chartData = data
        .take(10)
        .map((item) => ChartData(item.name, item.quantity.toDouble()))
        .toList();

    return SizedBox(
      width: width,
      height: height ?? 350,
      child: SfCartesianChart(
        title: ChartTitle(text: 'مستويات المخزون'),
        primaryXAxis: CategoryAxis(
          title: AxisTitle(text: 'الصنف'),
          labelRotation: -45,
        ),
        primaryYAxis: NumericAxis(title: AxisTitle(text: 'الكمية')),
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            dataSource: chartData,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            color: AppColors.info,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
        tooltipBehavior: TooltipBehavior(enable: true),
      ),
    );
  }

  /// إنشاء رسم بياني لقيمة المخزون
  static Widget _createStockValueChart(
    List<InventoryReportItem> data,
    double? width,
    double? height,
  ) {
    final chartData = data
        .take(10)
        .map((item) => ChartData(item.name, item.totalCostValue))
        .toList();

    return SizedBox(
      width: width,
      height: height ?? 350,
      child: SfCartesianChart(
        title: ChartTitle(text: 'قيمة المخزون'),
        primaryXAxis: CategoryAxis(
          title: AxisTitle(text: 'الصنف'),
          labelRotation: -45,
        ),
        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'القيمة'),
          numberFormat: NumberFormat.currency(locale: 'ar', symbol: ''),
        ),
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            dataSource: chartData,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            color: AppColors.warning,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
        tooltipBehavior: TooltipBehavior(enable: true),
      ),
    );
  }

  /// إنشاء رسم بياني للمخزون المنخفض
  static Widget _createLowStockChart(
    List<InventoryReportItem> data,
    double? width,
    double? height,
  ) {
    final lowStockItems = data
        .where((item) => item.quantity <= item.minQuantity)
        .toList();

    if (lowStockItems.isEmpty) {
      return const Center(child: Text('لا توجد أصناف بمخزون منخفض'));
    }

    final chartData = lowStockItems
        .map((item) => ChartData(item.name, item.quantity.toDouble()))
        .toList();

    return SizedBox(
      width: width,
      height: height ?? 350,
      child: SfCartesianChart(
        title: ChartTitle(text: 'الأصناف ذات المخزون المنخفض'),
        primaryXAxis: CategoryAxis(
          title: AxisTitle(text: 'الصنف'),
          labelRotation: -45,
        ),
        primaryYAxis: NumericAxis(title: AxisTitle(text: 'الكمية')),
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            dataSource: chartData,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            color: AppColors.error,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
        tooltipBehavior: TooltipBehavior(enable: true),
      ),
    );
  }

  /// الحصول على اسم نوع الحساب للعرض
  static String _getAccountTypeDisplayName(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'asset':
        return 'الأصول';
      case 'liability':
        return 'الخصوم';
      case 'equity':
        return 'حقوق الملكية';
      case 'revenue':
        return 'الإيرادات';
      case 'expense':
        return 'المصروفات';
      default:
        return accountType;
    }
  }

  /// الحصول على لون الرسم البياني
  static Color _getChartColor(int index) {
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.info,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[index % colors.length];
  }
}

/// نموذج بيانات الرسم البياني
class ChartData {
  final String category;
  final double value;

  ChartData(this.category, this.value);
}

/// نموذج البيانات المالية الشهرية
class MonthlyFinancialData {
  final String monthName;
  final double revenue;
  final double expense;
  final double netProfit;

  MonthlyFinancialData({
    required this.monthName,
    required this.revenue,
    required this.expense,
    required this.netProfit,
  });
}

/// أنواع رسوم المخزون البيانية
enum InventoryChartType { stockLevels, stockValue, lowStock }
