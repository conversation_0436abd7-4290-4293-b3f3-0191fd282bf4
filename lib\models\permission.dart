/// تعداد الصلاحيات المتاحة في النظام
enum PermissionType {
  // صلاحيات الحسابات
  viewAccounts,
  addAccounts,
  editAccounts,
  deleteAccounts,
  
  // صلاحيات القيود المحاسبية
  viewJournalEntries,
  addJournalEntries,
  editJournalEntries,
  deleteJournalEntries,
  postJournalEntries,
  unpostJournalEntries,
  
  // صلاحيات الفواتير
  viewInvoices,
  addInvoices,
  editInvoices,
  deleteInvoices,
  postInvoices,
  
  // صلاحيات العملاء
  viewCustomers,
  addCustomers,
  editCustomers,
  deleteCustomers,
  
  // صلاحيات الموردين
  viewSuppliers,
  addSuppliers,
  editSuppliers,
  deleteSuppliers,
  
  // صلاحيات الأصناف
  viewItems,
  addItems,
  editItems,
  deleteItems,
  
  // صلاحيات التقارير
  viewReports,
  exportReports,
  viewFinancialReports,
  viewInventoryReports,
  viewCustomerReports,
  viewSupplierReports,
  
  // صلاحيات إدارة النظام
  manageUsers,
  manageRoles,
  managePermissions,
  manageSettings,
  manageBackup,
  viewAuditLog,
  
  // صلاحيات متقدمة
  systemAdmin,
  databaseAdmin,
}

/// نموذج الصلاحية
class Permission {
  final int? id;
  final PermissionType type;
  final String name;
  final String description;
  final String category;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Permission({
    this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.category,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء صلاحية من Map
  factory Permission.fromMap(Map<String, dynamic> map) {
    return Permission(
      id: map['id'] as int?,
      type: PermissionType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => PermissionType.viewAccounts,
      ),
      name: map['name'] as String,
      description: map['description'] as String,
      category: map['category'] as String,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// تحويل الصلاحية إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'name': name,
      'description': description,
      'category': category,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من الصلاحية
  Permission copyWith({
    int? id,
    PermissionType? type,
    String? name,
    String? description,
    String? category,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Permission(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Permission{id: $id, type: $type, name: $name, category: $category}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Permission &&
           other.id == id &&
           other.type == type;
  }

  @override
  int get hashCode => id.hashCode ^ type.hashCode;
}

/// مساعد للحصول على الصلاحيات الافتراضية
class PermissionHelper {
  /// الحصول على جميع الصلاحيات الافتراضية
  static List<Permission> getDefaultPermissions() {
    final now = DateTime.now();
    
    return [
      // صلاحيات الحسابات
      Permission(
        type: PermissionType.viewAccounts,
        name: 'عرض الحسابات',
        description: 'عرض قائمة الحسابات المحاسبية',
        category: 'الحسابات',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.addAccounts,
        name: 'إضافة حسابات',
        description: 'إضافة حسابات محاسبية جديدة',
        category: 'الحسابات',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.editAccounts,
        name: 'تعديل الحسابات',
        description: 'تعديل بيانات الحسابات المحاسبية',
        category: 'الحسابات',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.deleteAccounts,
        name: 'حذف الحسابات',
        description: 'حذف الحسابات المحاسبية',
        category: 'الحسابات',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      
      // صلاحيات القيود المحاسبية
      Permission(
        type: PermissionType.viewJournalEntries,
        name: 'عرض القيود',
        description: 'عرض القيود المحاسبية',
        category: 'القيود المحاسبية',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.addJournalEntries,
        name: 'إضافة قيود',
        description: 'إضافة قيود محاسبية جديدة',
        category: 'القيود المحاسبية',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.editJournalEntries,
        name: 'تعديل القيود',
        description: 'تعديل القيود المحاسبية',
        category: 'القيود المحاسبية',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.deleteJournalEntries,
        name: 'حذف القيود',
        description: 'حذف القيود المحاسبية',
        category: 'القيود المحاسبية',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.postJournalEntries,
        name: 'ترحيل القيود',
        description: 'ترحيل القيود المحاسبية',
        category: 'القيود المحاسبية',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      
      // صلاحيات التقارير
      Permission(
        type: PermissionType.viewReports,
        name: 'عرض التقارير',
        description: 'عرض التقارير العامة',
        category: 'التقارير',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.exportReports,
        name: 'تصدير التقارير',
        description: 'تصدير التقارير إلى ملفات',
        category: 'التقارير',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      
      // صلاحيات إدارة النظام
      Permission(
        type: PermissionType.manageUsers,
        name: 'إدارة المستخدمين',
        description: 'إضافة وتعديل وحذف المستخدمين',
        category: 'إدارة النظام',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.manageSettings,
        name: 'إدارة الإعدادات',
        description: 'تعديل إعدادات النظام',
        category: 'إدارة النظام',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Permission(
        type: PermissionType.systemAdmin,
        name: 'مدير النظام',
        description: 'صلاحيات كاملة لإدارة النظام',
        category: 'إدارة النظام',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// الحصول على الصلاحيات حسب الفئة
  static List<Permission> getPermissionsByCategory(String category) {
    return getDefaultPermissions()
        .where((permission) => permission.category == category)
        .toList();
  }

  /// الحصول على جميع الفئات
  static List<String> getCategories() {
    return getDefaultPermissions()
        .map((permission) => permission.category)
        .toSet()
        .toList();
  }
}
