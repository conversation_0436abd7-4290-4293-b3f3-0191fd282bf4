import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';

class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final bool? isIntegrated; // حالة التكامل (اختيارية)

  const InvoiceCard({
    super.key,
    required this.invoice,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    this.isIntegrated,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة نوع الفاتورة
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getInvoiceTypeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getInvoiceTypeIcon(),
                      color: _getInvoiceTypeColor(),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات الفاتورة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'فاتورة رقم: ${invoice.invoiceNumber}',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.textPrimary,
                                    ),
                              ),
                            ),
                            // مؤشر التكامل
                            if (isIntegrated != null) ...[
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: isIntegrated!
                                      ? AppColors.success.withValues(alpha: 0.1)
                                      : AppColors.warning.withValues(
                                          alpha: 0.1,
                                        ),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Icon(
                                  isIntegrated!
                                      ? Icons.check_circle_outline
                                      : Icons.warning_amber_outlined,
                                  color: isIntegrated!
                                      ? AppColors.success
                                      : AppColors.warning,
                                  size: 14,
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                            // حالة الفاتورة
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor().withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                invoice.statusArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: _getStatusColor(),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'التاريخ: ${DateFormat('dd/MM/yyyy').format(invoice.invoiceDate)}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: AppColors.textSecondary),
                            ),
                            const SizedBox(width: 16),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getInvoiceTypeColor().withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                invoice.typeArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: _getInvoiceTypeColor(),
                                      fontSize: 10,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // المبالغ
              Row(
                children: [
                  Expanded(
                    child: _buildAmountInfo(
                      'المبلغ الإجمالي',
                      invoice.totalAmount,
                      AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAmountInfo(
                      'المبلغ المدفوع',
                      invoice.paidAmount,
                      AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAmountInfo(
                      'المتبقي',
                      invoice.remainingAmount,
                      AppColors.error,
                    ),
                  ),
                ],
              ),
              // معلومات إضافية
              if (invoice.items.isNotEmpty || invoice.notes != null) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (invoice.items.isNotEmpty) ...[
                      Icon(
                        Icons.list,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'عدد الأصناف: ${invoice.items.length}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                    const Spacer(),
                    // مؤشر حالة الدفع
                    if (invoice.isPaid)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 12,
                              color: AppColors.success,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'مدفوعة',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: AppColors.success,
                                    fontSize: 10,
                                  ),
                            ),
                          ],
                        ),
                      )
                    else if (invoice.isPartiallyPaid)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.warning.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.schedule,
                              size: 12,
                              color: AppColors.warning,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'دفع جزئي',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: AppColors.warning,
                                    fontSize: 10,
                                  ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
              // الملاحظات
              if (invoice.notes != null && invoice.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  invoice.notes!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInfo(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
        ),
        Text(
          '${NumberFormat('#,##0.00').format(amount)} ل.س',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Color _getInvoiceTypeColor() {
    switch (invoice.type) {
      case AppConstants.invoiceTypeSale:
        return AppColors.success;
      case AppConstants.invoiceTypePurchase:
        return AppColors.primary;
      case AppConstants.invoiceTypeSaleReturn:
        return AppColors.warning;
      case AppConstants.invoiceTypePurchaseReturn:
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getInvoiceTypeIcon() {
    switch (invoice.type) {
      case AppConstants.invoiceTypeSale:
        return Icons.point_of_sale;
      case AppConstants.invoiceTypePurchase:
        return Icons.shopping_cart;
      case AppConstants.invoiceTypeSaleReturn:
        return Icons.keyboard_return;
      case AppConstants.invoiceTypePurchaseReturn:
        return Icons.undo;
      default:
        return Icons.receipt;
    }
  }

  Color _getStatusColor() {
    switch (invoice.status) {
      case InvoiceStatus.draft:
        return AppColors.textSecondary;
      case InvoiceStatus.confirmed:
        return AppColors.info;
      case InvoiceStatus.fullyPaid:
      case InvoiceStatus.partiallyPaid:
        return AppColors.success;
      case InvoiceStatus.overdue:
        return AppColors.warning;
      case InvoiceStatus.cancelled:
        return AppColors.error;
    }
  }
}
