import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import '../database/database_helper.dart';
import '../models/user.dart';
import '../models/role.dart';
import '../models/permission.dart';
import '../constants/app_constants.dart';
import '../services/audit_service.dart';
import '../services/logging_service.dart';

/// خدمة المصادقة وإدارة المستخدمين
class AuthService {
  static final AuthService _instance = AuthService._internal();
  static User? _currentUser;
  static Role? _currentUserRole;
  static String? _currentSessionId;
  static DateTime? _sessionStartTime;

  AuthService._internal();

  factory AuthService() => _instance;

  /// المستخدم الحالي
  static User? get currentUser => _currentUser;

  /// دور المستخدم الحالي
  static Role? get currentUserRole => _currentUserRole;

  /// معرف الجلسة الحالية
  static String? get currentSessionId => _currentSessionId;

  /// وقت بداية الجلسة
  static DateTime? get sessionStartTime => _sessionStartTime;

  /// التحقق من تسجيل الدخول
  static bool get isLoggedIn => _currentUser != null;

  /// التحقق من انتهاء صلاحية الجلسة
  static bool get isSessionExpired {
    if (_sessionStartTime == null) return true;
    final now = DateTime.now();
    final sessionDuration = now.difference(_sessionStartTime!);
    return sessionDuration.inMinutes > AppConstants.sessionTimeoutMinutes;
  }

  /// تسجيل الدخول
  static Future<AuthResult> login(String username, String password) async {
    try {
      final db = await DatabaseHelper().database;

      // البحث عن المستخدم
      final userResults = await db.query(
        AppConstants.usersTable,
        where: 'username = ? AND is_active = 1',
        whereArgs: [username],
        limit: 1,
      );

      if (userResults.isEmpty) {
        await _logFailedLogin(username, 'مستخدم غير موجود');
        return AuthResult.failure('اسم المستخدم أو كلمة المرور غير صحيحة');
      }

      final userData = userResults.first;
      final user = User.fromMap(userData);

      // التحقق من قفل الحساب
      if (user.isLocked) {
        await _logFailedLogin(username, 'الحساب مقفل');
        return AuthResult.failure('الحساب مقفل. يرجى الاتصال بالمدير');
      }

      // التحقق من كلمة المرور
      final passwordHash = _hashPassword(password);
      if (user.passwordHash != passwordHash) {
        await _incrementFailedAttempts(user.id!);
        await _logFailedLogin(username, 'كلمة مرور خاطئة');
        return AuthResult.failure('اسم المستخدم أو كلمة المرور غير صحيحة');
      }

      // إعادة تعيين محاولات الفشل
      await _resetFailedAttempts(user.id!);

      // تحديث وقت آخر تسجيل دخول
      await _updateLastLogin(user.id!);

      // تحميل دور المستخدم
      final role = await _loadUserRole(user.roleId);

      // إنشاء جلسة جديدة
      final sessionId = _generateSessionId();
      await _createSession(user.id!, sessionId);

      // تعيين المستخدم الحالي
      _currentUser = user;
      _currentUserRole = role;
      _currentSessionId = sessionId;
      _sessionStartTime = DateTime.now();

      // تسجيل في سجل المراجعة
      AuditService.setCurrentUser(
        userId: user.id.toString(),
        userName: user.username,
        sessionId: sessionId,
      );

      await AuditService.logLogin(
        userId: user.id.toString(),
        userName: user.username,
        description: 'تسجيل دخول ناجح',
      );

      LoggingService.security(
        'تم تسجيل دخول المستخدم ${user.username} بنجاح',
        category: 'Authentication',
      );

      return AuthResult.success(user);
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الدخول',
        category: 'Authentication',
        data: {'error': e.toString()},
      );
      return AuthResult.failure('حدث خطأ في تسجيل الدخول');
    }
  }

  /// تسجيل الخروج
  static Future<bool> logout() async {
    try {
      if (_currentUser != null && _currentSessionId != null) {
        // إنهاء الجلسة في قاعدة البيانات
        await _endSession(_currentSessionId!);

        // تسجيل في سجل المراجعة
        await AuditService.logLogout(
          description: 'تسجيل خروج المستخدم ${_currentUser!.username}',
        );

        LoggingService.security(
          'تم تسجيل خروج المستخدم ${_currentUser!.username}',
          category: 'Authentication',
        );

        // مسح بيانات الجلسة
        _clearSession();
      }

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الخروج',
        category: 'Authentication',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من الصلاحية
  static bool hasPermission(PermissionType permission) {
    if (_currentUser == null || _currentUserRole == null) return false;
    
    // المدير العام له جميع الصلاحيات
    if (_currentUser!.isAdmin) return true;
    
    return _currentUserRole!.hasPermission(permission);
  }

  /// التحقق من صلاحيات متعددة (يجب أن تكون جميعها متوفرة)
  static bool hasAllPermissions(List<PermissionType> permissions) {
    return permissions.every((permission) => hasPermission(permission));
  }

  /// التحقق من صلاحيات متعددة (يكفي وجود واحدة منها)
  static bool hasAnyPermission(List<PermissionType> permissions) {
    return permissions.any((permission) => hasPermission(permission));
  }

  /// فرض الصلاحية (يرمي استثناء إذا لم تكن متوفرة)
  static void requirePermission(PermissionType permission) {
    if (!hasPermission(permission)) {
      throw UnauthorizedException('ليس لديك صلاحية لتنفيذ هذه العملية');
    }
  }

  /// تحديث كلمة المرور
  static Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      if (_currentUser == null) return false;

      // التحقق من كلمة المرور القديمة
      final oldPasswordHash = _hashPassword(oldPassword);
      if (_currentUser!.passwordHash != oldPasswordHash) {
        return false;
      }

      // تحديث كلمة المرور
      final newPasswordHash = _hashPassword(newPassword);
      final db = await DatabaseHelper().database;
      
      await db.update(
        AppConstants.usersTable,
        {
          'password_hash': newPasswordHash,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [_currentUser!.id],
      );

      // تحديث المستخدم الحالي
      _currentUser = _currentUser!.copyWith(passwordHash: newPasswordHash);

      await AuditService.log(
        action: 'CHANGE_PASSWORD',
        entityType: 'users',
        entityId: _currentUser!.id,
        description: 'تغيير كلمة المرور',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير كلمة المرور',
        category: 'Authentication',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// تشفير كلمة المرور
  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// إنشاء معرف جلسة عشوائي
  static String _generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// تحميل دور المستخدم
  static Future<Role?> _loadUserRole(int? roleId) async {
    if (roleId == null) return null;

    try {
      final db = await DatabaseHelper().database;
      final roleResults = await db.query(
        AppConstants.rolesTable,
        where: 'id = ? AND is_active = 1',
        whereArgs: [roleId],
        limit: 1,
      );

      if (roleResults.isNotEmpty) {
        return Role.fromMap(roleResults.first);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل دور المستخدم',
        category: 'Authentication',
        data: {'roleId': roleId, 'error': e.toString()},
      );
    }

    return null;
  }

  /// إنشاء جلسة جديدة
  static Future<void> _createSession(int userId, String sessionId) async {
    try {
      final db = await DatabaseHelper().database;
      await db.insert(AppConstants.userSessionsTable, {
        'user_id': userId,
        'session_id': sessionId,
        'session_type': AppConstants.sessionTypeLogin,
        'started_at': DateTime.now().toIso8601String(),
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الجلسة',
        category: 'Authentication',
        data: {'userId': userId, 'error': e.toString()},
      );
    }
  }

  /// إنهاء الجلسة
  static Future<void> _endSession(String sessionId) async {
    try {
      final db = await DatabaseHelper().database;
      await db.update(
        AppConstants.userSessionsTable,
        {
          'ended_at': DateTime.now().toIso8601String(),
          'is_active': 0,
        },
        where: 'session_id = ?',
        whereArgs: [sessionId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنهاء الجلسة',
        category: 'Authentication',
        data: {'sessionId': sessionId, 'error': e.toString()},
      );
    }
  }

  /// مسح بيانات الجلسة
  static void _clearSession() {
    _currentUser = null;
    _currentUserRole = null;
    _currentSessionId = null;
    _sessionStartTime = null;
    AuditService.clearCurrentUser();
  }

  /// زيادة محاولات الفشل
  static Future<void> _incrementFailedAttempts(int userId) async {
    try {
      final db = await DatabaseHelper().database;
      await db.rawUpdate(
        'UPDATE ${AppConstants.usersTable} SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?',
        [userId],
      );

      // قفل الحساب إذا تجاوز الحد الأقصى
      final userResult = await db.query(
        AppConstants.usersTable,
        columns: ['failed_login_attempts'],
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (userResult.isNotEmpty) {
        final attempts = userResult.first['failed_login_attempts'] as int;
        if (attempts >= AppConstants.maxLoginAttempts) {
          await db.update(
            AppConstants.usersTable,
            {
              'locked_until': DateTime.now().add(Duration(hours: 1)).toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [userId],
          );
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في زيادة محاولات الفشل',
        category: 'Authentication',
        data: {'userId': userId, 'error': e.toString()},
      );
    }
  }

  /// إعادة تعيين محاولات الفشل
  static Future<void> _resetFailedAttempts(int userId) async {
    try {
      final db = await DatabaseHelper().database;
      await db.update(
        AppConstants.usersTable,
        {
          'failed_login_attempts': 0,
          'locked_until': null,
        },
        where: 'id = ?',
        whereArgs: [userId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إعادة تعيين محاولات الفشل',
        category: 'Authentication',
        data: {'userId': userId, 'error': e.toString()},
      );
    }
  }

  /// تحديث وقت آخر تسجيل دخول
  static Future<void> _updateLastLogin(int userId) async {
    try {
      final db = await DatabaseHelper().database;
      await db.update(
        AppConstants.usersTable,
        {'last_login_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [userId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث وقت آخر تسجيل دخول',
        category: 'Authentication',
        data: {'userId': userId, 'error': e.toString()},
      );
    }
  }

  /// تسجيل محاولة فاشلة
  static Future<void> _logFailedLogin(String username, String reason) async {
    LoggingService.security(
      'محاولة تسجيل دخول فاشلة للمستخدم $username: $reason',
      category: 'Authentication',
    );
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool success;
  final String? message;
  final User? user;

  AuthResult._(this.success, this.message, this.user);

  factory AuthResult.success(User user) => AuthResult._(true, null, user);
  factory AuthResult.failure(String message) => AuthResult._(false, message, null);
}

/// استثناء عدم وجود صلاحية
class UnauthorizedException implements Exception {
  final String message;
  UnauthorizedException(this.message);

  @override
  String toString() => 'UnauthorizedException: $message';
}

/// إضافة خاصية isLocked للمستخدم
extension UserExtensions on User {
  bool get isLocked {
    if (lastLoginAt == null) return false;
    final lockedUntil = lastLoginAt!.add(Duration(hours: 1));
    return DateTime.now().isBefore(lockedUntil);
  }
}
