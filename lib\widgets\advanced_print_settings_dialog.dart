/// حوار إعدادات الطباعة المتقدمة
/// يوفر واجهة شاملة لتخصيص جميع إعدادات الطباعة المتقدمة
library;

import 'package:flutter/material.dart';
import 'dart:io';
import '../models/advanced_print_settings.dart';
import '../services/template_assets_service.dart';
import '../constants/app_colors.dart';

class AdvancedPrintSettingsDialog extends StatefulWidget {
  final AdvancedPrintSettings initialSettings;
  final Function(AdvancedPrintSettings) onSettingsChanged;

  const AdvancedPrintSettingsDialog({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
  });

  @override
  State<AdvancedPrintSettingsDialog> createState() =>
      _AdvancedPrintSettingsDialogState();
}

class _AdvancedPrintSettingsDialogState
    extends State<AdvancedPrintSettingsDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late AdvancedPrintSettings _settings;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _settings = widget.initialSettings;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _updateSettings(AdvancedPrintSettings newSettings) {
    setState(() {
      _settings = newSettings;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // رأس الحوار
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.settings, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    'إعدادات الطباعة المتقدمة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // شريط التبويبات
            TabBar(
              controller: _tabController,
              labelColor: AppColors.primary,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppColors.primary,
              tabs: const [
                Tab(icon: Icon(Icons.image), text: 'الشعار'),
                Tab(icon: Icon(Icons.draw), text: 'التوقيع'),
                Tab(icon: Icon(Icons.opacity), text: 'العلامة المائية'),
                Tab(icon: Icon(Icons.tune), text: 'عام'),
              ],
            ),

            // محتوى التبويبات
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLogoTab(),
                  _buildSignatureTab(),
                  _buildWatermarkTab(),
                  _buildGeneralTab(),
                ],
              ),
            ),

            // أزرار الإجراءات
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(top: BorderSide(color: Colors.grey[300]!)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      widget.onSettingsChanged(_settings);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('حفظ'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تفعيل/إلغاء تفعيل الشعار
          SwitchListTile(
            title: const Text('عرض الشعار'),
            subtitle: const Text('إظهار شعار الشركة في الفاتورة'),
            value: _settings.logoSettings.showLogo,
            onChanged: (value) {
              _updateSettings(
                _settings.copyWith(
                  logoSettings: _settings.logoSettings.copyWith(
                    showLogo: value,
                  ),
                ),
              );
            },
          ),

          if (_settings.logoSettings.showLogo) ...[
            const Divider(),

            // اختيار الشعار
            ListTile(
              title: const Text('ملف الشعار'),
              subtitle: Text(
                _settings.logoSettings.logoPath?.split('/').last ??
                    'لم يتم اختيار شعار',
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.upload_file),
                    onPressed: _uploadLogo,
                    tooltip: 'رفع شعار',
                  ),
                  if (_settings.logoSettings.logoPath != null)
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: _removeLogo,
                      tooltip: 'حذف الشعار',
                    ),
                ],
              ),
            ),

            // معاينة الشعار
            if (_settings.logoSettings.logoPath != null)
              Container(
                margin: const EdgeInsets.symmetric(vertical: 16),
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Image.file(
                    File(_settings.logoSettings.logoPath!),
                    height: 100,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.broken_image, size: 50);
                    },
                  ),
                ),
              ),

            // موضع الشعار
            const Text(
              'موضع الشعار:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: LogoPosition.values.map((position) {
                return ChoiceChip(
                  label: Text(position.displayName),
                  selected: _settings.logoSettings.position == position,
                  onSelected: (selected) {
                    if (selected) {
                      _updateSettings(
                        _settings.copyWith(
                          logoSettings: _settings.logoSettings.copyWith(
                            position: position,
                          ),
                        ),
                      );
                    }
                  },
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // أبعاد الشعار
            const Text(
              'أبعاد الشعار:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('العرض'),
                      Slider(
                        value: _settings.logoSettings.width,
                        min: 50,
                        max: 300,
                        divisions: 25,
                        label: '${_settings.logoSettings.width.round()}px',
                        onChanged: (value) {
                          _updateSettings(
                            _settings.copyWith(
                              logoSettings: _settings.logoSettings.copyWith(
                                width: value,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('الارتفاع'),
                      Slider(
                        value: _settings.logoSettings.height,
                        min: 30,
                        max: 200,
                        divisions: 17,
                        label: '${_settings.logoSettings.height.round()}px',
                        onChanged: (value) {
                          _updateSettings(
                            _settings.copyWith(
                              logoSettings: _settings.logoSettings.copyWith(
                                height: value,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // شفافية الشعار
            const Text(
              'الشفافية:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Slider(
              value: _settings.logoSettings.opacity,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              label: '${(_settings.logoSettings.opacity * 100).round()}%',
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    logoSettings: _settings.logoSettings.copyWith(
                      opacity: value,
                    ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSignatureTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تفعيل/إلغاء تفعيل التوقيع
          SwitchListTile(
            title: const Text('عرض التوقيع'),
            subtitle: const Text('إظهار توقيع المسؤول في الفاتورة'),
            value: _settings.signatureSettings.showSignature,
            onChanged: (value) {
              _updateSettings(
                _settings.copyWith(
                  signatureSettings: _settings.signatureSettings.copyWith(
                    showSignature: value,
                  ),
                ),
              );
            },
          ),

          if (_settings.signatureSettings.showSignature) ...[
            const Divider(),

            // اختيار التوقيع
            ListTile(
              title: const Text('ملف التوقيع'),
              subtitle: Text(
                _settings.signatureSettings.signaturePath?.split('/').last ??
                    'لم يتم اختيار توقيع',
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.upload_file),
                    onPressed: _uploadSignature,
                    tooltip: 'رفع توقيع',
                  ),
                  if (_settings.signatureSettings.signaturePath != null)
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: _removeSignature,
                      tooltip: 'حذف التوقيع',
                    ),
                ],
              ),
            ),

            // معاينة التوقيع
            if (_settings.signatureSettings.signaturePath != null)
              Container(
                margin: const EdgeInsets.symmetric(vertical: 16),
                height: 100,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Image.file(
                    File(_settings.signatureSettings.signaturePath!),
                    height: 80,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.broken_image, size: 40);
                    },
                  ),
                ),
              ),

            // اسم الموقع
            TextField(
              decoration: const InputDecoration(
                labelText: 'اسم الموقع',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(
                text: _settings.signatureSettings.signerName ?? '',
              ),
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    signatureSettings: _settings.signatureSettings.copyWith(
                      signerName: value,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // منصب الموقع
            TextField(
              decoration: const InputDecoration(
                labelText: 'منصب الموقع',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(
                text: _settings.signatureSettings.signerTitle ?? '',
              ),
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    signatureSettings: _settings.signatureSettings.copyWith(
                      signerTitle: value,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // موضع التوقيع
            const Text(
              'موضع التوقيع:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: SignaturePosition.values.map((position) {
                return ChoiceChip(
                  label: Text(position.displayName),
                  selected: _settings.signatureSettings.position == position,
                  onSelected: (selected) {
                    if (selected) {
                      _updateSettings(
                        _settings.copyWith(
                          signatureSettings: _settings.signatureSettings
                              .copyWith(position: position),
                        ),
                      );
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWatermarkTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تفعيل/إلغاء تفعيل العلامة المائية
          SwitchListTile(
            title: const Text('عرض العلامة المائية'),
            subtitle: const Text('إظهار علامة مائية في خلفية الفاتورة'),
            value: _settings.watermarkSettings.showWatermark,
            onChanged: (value) {
              _updateSettings(
                _settings.copyWith(
                  watermarkSettings: _settings.watermarkSettings.copyWith(
                    showWatermark: value,
                  ),
                ),
              );
            },
          ),

          if (_settings.watermarkSettings.showWatermark) ...[
            const Divider(),

            // نص العلامة المائية
            TextField(
              decoration: const InputDecoration(
                labelText: 'نص العلامة المائية',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(
                text: _settings.watermarkSettings.text,
              ),
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    watermarkSettings: _settings.watermarkSettings.copyWith(
                      text: value,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // شفافية العلامة المائية
            const Text(
              'الشفافية:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Slider(
              value: _settings.watermarkSettings.opacity,
              min: 0.05,
              max: 0.5,
              divisions: 9,
              label: '${(_settings.watermarkSettings.opacity * 100).round()}%',
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    watermarkSettings: _settings.watermarkSettings.copyWith(
                      opacity: value,
                    ),
                  ),
                );
              },
            ),

            // زاوية الدوران
            const Text(
              'زاوية الدوران:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Slider(
              value: _settings.watermarkSettings.rotation,
              min: -90,
              max: 90,
              divisions: 18,
              label: '${_settings.watermarkSettings.rotation.round()}°',
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    watermarkSettings: _settings.watermarkSettings.copyWith(
                      rotation: value,
                    ),
                  ),
                );
              },
            ),

            // حجم الخط
            const Text(
              'حجم الخط:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Slider(
              value: _settings.watermarkSettings.fontSize,
              min: 24,
              max: 120,
              divisions: 16,
              label: '${_settings.watermarkSettings.fontSize.round()}pt',
              onChanged: (value) {
                _updateSettings(
                  _settings.copyWith(
                    watermarkSettings: _settings.watermarkSettings.copyWith(
                      fontSize: value,
                    ),
                  ),
                );
              },
            ),

            // موضع العلامة المائية
            const Text(
              'الموضع:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: WatermarkPosition.values.map((position) {
                return ChoiceChip(
                  label: Text(position.displayName),
                  selected: _settings.watermarkSettings.position == position,
                  onSelected: (selected) {
                    if (selected) {
                      _updateSettings(
                        _settings.copyWith(
                          watermarkSettings: _settings.watermarkSettings
                              .copyWith(position: position),
                        ),
                      );
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGeneralTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إعدادات عامة
          SwitchListTile(
            title: const Text('عرض أرقام الصفحات'),
            value: _settings.showPageNumbers,
            onChanged: (value) {
              _updateSettings(_settings.copyWith(showPageNumbers: value));
            },
          ),

          SwitchListTile(
            title: const Text('عرض تاريخ الطباعة'),
            value: _settings.showPrintDate,
            onChanged: (value) {
              _updateSettings(_settings.copyWith(showPrintDate: value));
            },
          ),

          SwitchListTile(
            title: const Text('عرض رمز QR'),
            subtitle: const Text('رمز QR يحتوي على معلومات الفاتورة'),
            value: _settings.showQRCode,
            onChanged: (value) {
              _updateSettings(_settings.copyWith(showQRCode: value));
            },
          ),

          SwitchListTile(
            title: const Text('تفعيل الطباعة الملونة'),
            value: _settings.enableColorPrinting,
            onChanged: (value) {
              _updateSettings(_settings.copyWith(enableColorPrinting: value));
            },
          ),

          const Divider(),

          // نص التذييل المخصص
          TextField(
            decoration: const InputDecoration(
              labelText: 'نص التذييل المخصص',
              border: OutlineInputBorder(),
              hintText: 'نص اختياري يظهر في تذييل الفاتورة',
            ),
            controller: TextEditingController(
              text: _settings.customFooterText ?? '',
            ),
            maxLines: 2,
            onChanged: (value) {
              _updateSettings(
                _settings.copyWith(
                  customFooterText: value.isEmpty ? null : value,
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // جودة الطباعة
          const Text(
            'جودة الطباعة:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: PrintQuality.values.map((quality) {
              return ChoiceChip(
                label: Text(quality.displayName),
                selected: _settings.quality == quality,
                onSelected: (selected) {
                  if (selected) {
                    _updateSettings(_settings.copyWith(quality: quality));
                  }
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Future<void> _uploadLogo() async {
    try {
      final newLogoPath = await TemplateAssetsService.uploadLogo(
        existingPath: _settings.logoSettings.logoPath,
      );

      if (!mounted) return;

      if (newLogoPath != null) {
        _updateSettings(
          _settings.copyWith(
            logoSettings: _settings.logoSettings.copyWith(
              logoPath: newLogoPath,
            ),
          ),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفع الشعار بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في رفع الشعار: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _removeLogo() {
    _updateSettings(
      _settings.copyWith(
        logoSettings: _settings.logoSettings.copyWith(logoPath: null),
      ),
    );
  }

  Future<void> _uploadSignature() async {
    try {
      final newSignaturePath = await TemplateAssetsService.uploadSignature(
        existingPath: _settings.signatureSettings.signaturePath,
      );

      if (!mounted) return;

      if (newSignaturePath != null) {
        _updateSettings(
          _settings.copyWith(
            signatureSettings: _settings.signatureSettings.copyWith(
              signaturePath: newSignaturePath,
            ),
          ),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفع التوقيع بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في رفع التوقيع: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _removeSignature() {
    _updateSettings(
      _settings.copyWith(
        signatureSettings: _settings.signatureSettings.copyWith(
          signaturePath: null,
        ),
      ),
    );
  }
}
