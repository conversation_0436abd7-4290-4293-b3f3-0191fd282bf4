import 'dart:convert';
import 'dart:io';
import '../database/database_helper.dart';
import '../models/audit_log.dart';
import '../constants/app_constants.dart';
import 'logging_service.dart';

/// خدمة سجل المراجعة
/// تدير تسجيل جميع العمليات الحساسة في النظام
class AuditService {
  static final AuditService _instance = AuditService._internal();
  static String? _currentUserId;
  static String? _currentUserName;
  static String? _currentSessionId;

  AuditService._internal();

  factory AuditService() => _instance;

  /// تعيين معلومات المستخدم الحالي
  static void setCurrentUser({
    required String userId,
    required String userName,
    String? sessionId,
  }) {
    _currentUserId = userId;
    _currentUserName = userName;
    _currentSessionId =
        sessionId ?? DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// مسح معلومات المستخدم الحالي
  static void clearCurrentUser() {
    _currentUserId = null;
    _currentUserName = null;
    _currentSessionId = null;
  }

  /// تسجيل عملية مراجعة
  static Future<bool> log({
    required String action,
    required String entityType,
    int? entityId,
    String? entityName,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    String? description,
    String severity = 'INFO',
    String? category,
    String? referenceType,
    int? referenceId,
  }) async {
    try {
      final db = await DatabaseHelper().database;
      final now = DateTime.now();

      // الحصول على معلومات النظام
      final ipAddress = await _getIpAddress();
      final userAgent = await _getUserAgent();

      final auditLog = AuditLog(
        action: action,
        entityType: entityType,
        entityId: entityId,
        entityName: entityName,
        oldValues: oldValues != null ? jsonEncode(oldValues) : null,
        newValues: newValues != null ? jsonEncode(newValues) : null,
        userId: _currentUserId,
        userName: _currentUserName,
        ipAddress: ipAddress,
        userAgent: userAgent,
        sessionId: _currentSessionId,
        timestamp: now,
        description: description,
        severity: severity,
        category: category,
        referenceType: referenceType,
        referenceId: referenceId,
        createdAt: now,
      );

      await db.insert(
        AppConstants.auditLogTable,
        auditLog.toMap()..remove('id'),
      );

      // تسجيل في LoggingService أيضاً
      LoggingService.info(
        'Audit: ${auditLog.readableDescription}',
        category: 'Audit',
        data: {
          'action': action,
          'entity_type': entityType,
          'entity_id': entityId,
          'user_id': _currentUserId,
        },
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في تسجيل عملية المراجعة',
        category: 'Audit',
        data: {
          'action': action,
          'entity_type': entityType,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// تسجيل عملية إنشاء
  static Future<bool> logCreate({
    required String entityType,
    required int entityId,
    required String entityName,
    required Map<String, dynamic> newValues,
    String? description,
    String? category,
  }) async {
    return await log(
      action: AppConstants.auditActionCreate,
      entityType: entityType,
      entityId: entityId,
      entityName: entityName,
      newValues: newValues,
      description: description ?? 'تم إنشاء $entityName',
      severity: 'SUCCESS',
      category: category,
    );
  }

  /// تسجيل عملية تحديث
  static Future<bool> logUpdate({
    required String entityType,
    required int entityId,
    required String entityName,
    required Map<String, dynamic> oldValues,
    required Map<String, dynamic> newValues,
    String? description,
    String? category,
  }) async {
    return await log(
      action: AppConstants.auditActionUpdate,
      entityType: entityType,
      entityId: entityId,
      entityName: entityName,
      oldValues: oldValues,
      newValues: newValues,
      description: description ?? 'تم تحديث $entityName',
      severity: 'INFO',
      category: category,
    );
  }

  /// تسجيل عملية حذف
  static Future<bool> logDelete({
    required String entityType,
    required int entityId,
    required String entityName,
    required Map<String, dynamic> oldValues,
    String? description,
    String? category,
  }) async {
    return await log(
      action: AppConstants.auditActionDelete,
      entityType: entityType,
      entityId: entityId,
      entityName: entityName,
      oldValues: oldValues,
      description: description ?? 'تم حذف $entityName',
      severity: 'WARNING',
      category: category,
    );
  }

  /// تسجيل عملية تسجيل دخول
  static Future<bool> logLogin({
    required String userId,
    required String userName,
    String? description,
  }) async {
    return await log(
      action: AppConstants.auditActionLogin,
      entityType: AppConstants.auditEntityUser,
      entityId: int.tryParse(userId),
      entityName: userName,
      description: description ?? 'تسجيل دخول المستخدم $userName',
      severity: 'SUCCESS',
      category: 'Authentication',
    );
  }

  /// تسجيل عملية تسجيل خروج
  static Future<bool> logLogout({String? description}) async {
    return await log(
      action: AppConstants.auditActionLogout,
      entityType: AppConstants.auditEntityUser,
      entityId: int.tryParse(_currentUserId ?? ''),
      entityName: _currentUserName,
      description:
          description ??
          'تسجيل خروج المستخدم ${_currentUserName ?? 'غير معروف'}',
      severity: 'INFO',
      category: 'Authentication',
    );
  }

  /// تسجيل عملية تغيير كلمة المرور
  static Future<bool> logPasswordChange({String? description}) async {
    return await log(
      action: AppConstants.auditActionPasswordChange,
      entityType: AppConstants.auditEntityUser,
      entityId: int.tryParse(_currentUserId ?? ''),
      entityName: _currentUserName,
      description:
          description ??
          'تم تغيير كلمة مرور المستخدم ${_currentUserName ?? 'غير معروف'}',
      severity: 'WARNING',
      category: 'Security',
    );
  }

  /// الحصول على عنوان IP (محاكاة)
  static Future<String?> _getIpAddress() async {
    try {
      // في التطبيق الحقيقي، يمكن الحصول على IP من الشبكة
      return '127.0.0.1'; // localhost للتطبيق المحلي
    } catch (e) {
      return null;
    }
  }

  /// الحصول على User Agent (محاكاة)
  static Future<String?> _getUserAgent() async {
    try {
      if (Platform.isAndroid) {
        return 'Smart Ledger Android App';
      } else if (Platform.isWindows) {
        return 'Smart Ledger Windows App';
      } else if (Platform.isIOS) {
        return 'Smart Ledger iOS App';
      } else {
        return 'Smart Ledger App';
      }
    } catch (e) {
      return 'Smart Ledger App';
    }
  }

  /// الحصول على جميع سجلات المراجعة مع الفلترة
  static Future<List<AuditLog>> getAuditLogs({
    String? action,
    String? entityType,
    int? entityId,
    String? userId,
    String? severity,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
    String orderBy = 'timestamp DESC',
  }) async {
    try {
      final db = await DatabaseHelper().database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      // بناء شروط البحث
      if (action != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'action = ?';
        whereArgs.add(action);
      }

      if (entityType != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'entity_type = ?';
        whereArgs.add(entityType);
      }

      if (entityId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'entity_id = ?';
        whereArgs.add(entityId);
      }

      if (userId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'user_id = ?';
        whereArgs.add(userId);
      }

      if (severity != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'severity = ?';
        whereArgs.add(severity);
      }

      if (category != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'category = ?';
        whereArgs.add(category);
      }

      if (startDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'timestamp >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'timestamp <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      // بناء الاستعلام
      String query =
          'SELECT * FROM ${AppConstants.auditLogTable} $whereClause ORDER BY $orderBy';

      if (limit != null) {
        query += ' LIMIT $limit';
        if (offset != null) {
          query += ' OFFSET $offset';
        }
      }

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        query,
        whereArgs,
      );

      return maps.map((map) => AuditLog.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'فشل في جلب سجلات المراجعة',
        category: 'Audit',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على سجل مراجعة واحد بالمعرف
  static Future<AuditLog?> getAuditLogById(int id) async {
    try {
      final db = await DatabaseHelper().database;

      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.auditLogTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return AuditLog.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'فشل في جلب سجل المراجعة',
        category: 'Audit',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// البحث في سجلات المراجعة
  static Future<List<AuditLog>> searchAuditLogs({
    required String searchTerm,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await DatabaseHelper().database;

      const String query =
          '''
        SELECT * FROM ${AppConstants.auditLogTable}
        WHERE entity_name LIKE ?
           OR description LIKE ?
           OR user_name LIKE ?
           OR action LIKE ?
           OR entity_type LIKE ?
        ORDER BY timestamp DESC
      ''';

      final String searchPattern = '%$searchTerm%';
      final List<dynamic> args = [
        searchPattern,
        searchPattern,
        searchPattern,
        searchPattern,
        searchPattern,
      ];

      String finalQuery = query;
      if (limit != null) {
        finalQuery += ' LIMIT $limit';
        if (offset != null) {
          finalQuery += ' OFFSET $offset';
        }
      }

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        finalQuery,
        args,
      );

      return maps.map((map) => AuditLog.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'فشل في البحث في سجلات المراجعة',
        category: 'Audit',
        data: {'search_term': searchTerm, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات سجلات المراجعة
  static Future<Map<String, int>> getAuditStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await DatabaseHelper().database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (startDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'timestamp >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'timestamp <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      // إحصائيات العمليات
      final actionStats = await db.rawQuery('''
        SELECT action, COUNT(*) as count
        FROM ${AppConstants.auditLogTable}
        $whereClause
        GROUP BY action
      ''', whereArgs);

      // إحصائيات الشدة
      final severityStats = await db.rawQuery('''
        SELECT severity, COUNT(*) as count
        FROM ${AppConstants.auditLogTable}
        $whereClause
        GROUP BY severity
      ''', whereArgs);

      // إحصائيات الكيانات
      final entityStats = await db.rawQuery('''
        SELECT entity_type, COUNT(*) as count
        FROM ${AppConstants.auditLogTable}
        $whereClause
        GROUP BY entity_type
      ''', whereArgs);

      Map<String, int> statistics = {};

      // إضافة إحصائيات العمليات
      for (var row in actionStats) {
        statistics['action_${row['action']}'] = row['count'] as int;
      }

      // إضافة إحصائيات الشدة
      for (var row in severityStats) {
        statistics['severity_${row['severity']}'] = row['count'] as int;
      }

      // إضافة إحصائيات الكيانات
      for (var row in entityStats) {
        statistics['entity_${row['entity_type']}'] = row['count'] as int;
      }

      return statistics;
    } catch (e) {
      LoggingService.error(
        'فشل في جلب إحصائيات المراجعة',
        category: 'Audit',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// حذف سجلات المراجعة القديمة
  static Future<bool> cleanupOldAuditLogs({required int daysToKeep}) async {
    try {
      final db = await DatabaseHelper().database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));

      final deletedCount = await db.delete(
        AppConstants.auditLogTable,
        where: 'timestamp < ?',
        whereArgs: [cutoffDate.toIso8601String()],
      );

      LoggingService.info(
        'تم حذف $deletedCount سجل مراجعة قديم',
        category: 'Audit',
        data: {
          'deleted_count': deletedCount,
          'cutoff_date': cutoffDate.toIso8601String(),
        },
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في حذف سجلات المراجعة القديمة',
        category: 'Audit',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على عدد سجلات المراجعة
  static Future<int> getAuditLogCount({
    String? action,
    String? entityType,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await DatabaseHelper().database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (action != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'action = ?';
        whereArgs.add(action);
      }

      if (entityType != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'entity_type = ?';
        whereArgs.add(entityType);
      }

      if (userId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'user_id = ?';
        whereArgs.add(userId);
      }

      if (startDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'timestamp >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'timestamp <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.auditLogTable} $whereClause',
        whereArgs,
      );

      return result.first['count'] as int;
    } catch (e) {
      LoggingService.error(
        'فشل في حساب عدد سجلات المراجعة',
        category: 'Audit',
        data: {'error': e.toString()},
      );
      return 0;
    }
  }
}
