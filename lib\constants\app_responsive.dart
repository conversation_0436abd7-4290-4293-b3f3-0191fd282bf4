import 'package:flutter/material.dart';

/// نظام التخطيط المتجاوب للتطبيق
class AppResponsive {
  // نقاط الكسر للشاشات
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // أحجام الشاشات
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  // الحصول على نوع الجهاز
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) return DeviceType.mobile;
    if (width < tabletBreakpoint) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  // الحصول على عدد الأعمدة المناسب
  static int getGridColumns(
    BuildContext context, {
    int mobileColumns = 1,
    int tabletColumns = 2,
    int desktopColumns = 3,
  }) {
    if (isMobile(context)) return mobileColumns;
    if (isTablet(context)) return tabletColumns;
    return desktopColumns;
  }

  // الحصول على المسافات المناسبة
  static EdgeInsets getPadding(
    BuildContext context, {
    EdgeInsets? mobile,
    EdgeInsets? tablet,
    EdgeInsets? desktop,
  }) {
    if (isMobile(context)) {
      return mobile ?? const EdgeInsets.all(16);
    }
    if (isTablet(context)) {
      return tablet ?? const EdgeInsets.all(24);
    }
    return desktop ?? const EdgeInsets.all(32);
  }

  // الحصول على حجم الخط المناسب
  static double getFontSize(
    BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
    double baseSize = 16,
  }) {
    if (isMobile(context)) {
      return mobile ?? baseSize;
    }
    if (isTablet(context)) {
      return tablet ?? (baseSize * 1.1);
    }
    return desktop ?? (baseSize * 1.2);
  }

  // الحصول على عرض الحاوي المناسب
  static double getContainerWidth(
    BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (isMobile(context)) {
      return mobile ?? screenWidth * 0.95;
    }
    if (isTablet(context)) {
      return tablet ?? screenWidth * 0.85;
    }
    return desktop ?? screenWidth * 0.75;
  }

  // مكون متجاوب عام
  static Widget responsive({
    required BuildContext context,
    Widget? mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    if (isMobile(context) && mobile != null) {
      return mobile;
    }
    if (isTablet(context) && tablet != null) {
      return tablet;
    }
    if (desktop != null) {
      return desktop;
    }

    // العودة إلى الافتراضي
    return mobile ?? tablet ?? desktop ?? const SizedBox.shrink();
  }

  // تخطيط متجاوب للقوائم
  static Widget responsiveGrid({
    required BuildContext context,
    required List<Widget> children,
    int? mobileColumns,
    int? tabletColumns,
    int? desktopColumns,
    double spacing = 16,
    double runSpacing = 16,
  }) {
    final columns = getGridColumns(
      context,
      mobileColumns: mobileColumns ?? 1,
      tabletColumns: tabletColumns ?? 2,
      desktopColumns: desktopColumns ?? 3,
    );

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }

  // تخطيط متجاوب للبطاقات
  static Widget responsiveCardLayout({
    required BuildContext context,
    required List<Widget> cards,
    double spacing = 16,
  }) {
    if (isMobile(context)) {
      return Column(
        children: cards
            .map(
              (card) => Padding(
                padding: EdgeInsets.only(bottom: spacing),
                child: card,
              ),
            )
            .toList(),
      );
    }

    return Wrap(spacing: spacing, runSpacing: spacing, children: cards);
  }

  // شريط تطبيق متجاوب
  static PreferredSizeWidget responsiveAppBar({
    required BuildContext context,
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
  }) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: getFontSize(context, mobile: 18, tablet: 20, desktop: 22),
        ),
      ),
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      toolbarHeight: isMobile(context) ? 56 : 64,
    );
  }

  // حاوي متجاوب مع حد أقصى للعرض
  static Widget responsiveContainer({
    required BuildContext context,
    required Widget child,
    double? maxWidth,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? getContainerWidth(context),
      ),
      padding: padding ?? getPadding(context),
      margin: margin,
      child: child,
    );
  }

  // تخطيط صف متجاوب
  static Widget responsiveRow({
    required BuildContext context,
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    bool forceColumn = false,
  }) {
    if (isMobile(context) || forceColumn) {
      return Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: children,
      );
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }

  // تخطيط للنماذج
  static Widget responsiveForm({
    required BuildContext context,
    required List<Widget> fields,
    double spacing = 16,
  }) {
    if (isMobile(context)) {
      return Column(
        children: fields
            .map(
              (field) => Padding(
                padding: EdgeInsets.only(bottom: spacing),
                child: field,
              ),
            )
            .toList(),
      );
    }

    // للأجهزة الأكبر، نضع الحقول في صفوف
    final rows = <Widget>[];
    for (int i = 0; i < fields.length; i += 2) {
      if (i + 1 < fields.length) {
        rows.add(
          Padding(
            padding: EdgeInsets.only(bottom: spacing),
            child: Row(
              children: [
                Expanded(child: fields[i]),
                SizedBox(width: spacing),
                Expanded(child: fields[i + 1]),
              ],
            ),
          ),
        );
      } else {
        rows.add(
          Padding(
            padding: EdgeInsets.only(bottom: spacing),
            child: fields[i],
          ),
        );
      }
    }

    return Column(children: rows);
  }

  // الحصول على ارتفاع الشاشة
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  // الحصول على عرض الشاشة
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  // التحقق من الاتجاه
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
}

/// أنواع الأجهزة
enum DeviceType { mobile, tablet, desktop }
