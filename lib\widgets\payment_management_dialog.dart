/// حوار إدارة الدفعات للفواتير
/// يتيح إضافة وتعديل وحذف الدفعات مع تتبع المدفوعات الجزئية
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/payment.dart';
import '../models/invoice.dart';
import '../services/payment_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';

class PaymentManagementDialog extends StatefulWidget {
  final Invoice invoice;
  final Function()? onPaymentAdded;

  const PaymentManagementDialog({
    super.key,
    required this.invoice,
    this.onPaymentAdded,
  });

  @override
  State<PaymentManagementDialog> createState() => _PaymentManagementDialogState();
}

class _PaymentManagementDialogState extends State<PaymentManagementDialog> {
  final PaymentService _paymentService = PaymentService();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _referenceController = TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _accountNumberController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  PaymentMethod _selectedMethod = PaymentMethod.cash;
  DateTime _selectedDate = DateTime.now();
  PaymentSummary? _paymentSummary;
  bool _isLoading = true;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _loadPaymentSummary();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _referenceController.dispose();
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadPaymentSummary() async {
    try {
      final summary = await _paymentService.getPaymentSummary(widget.invoice.id!);
      setState(() {
        _paymentSummary = summary;
        _isLoading = false;
        // تعيين المبلغ المتبقي كقيمة افتراضية
        _amountController.text = summary.remainingAmount.toStringAsFixed(2);
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        _showError('خطأ في تحميل بيانات الدفعات: $e');
      }
    }
  }

  Future<void> _addPayment() async {
    if (!_validateInput()) return;

    setState(() => _isSubmitting = true);

    try {
      final amount = double.parse(_amountController.text);
      
      final payment = Payment(
        invoiceId: widget.invoice.id!,
        amount: amount,
        method: _selectedMethod,
        paymentDate: _selectedDate,
        reference: _referenceController.text.isNotEmpty ? _referenceController.text : null,
        bankName: _bankNameController.text.isNotEmpty ? _bankNameController.text : null,
        accountNumber: _accountNumberController.text.isNotEmpty ? _accountNumberController.text : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        status: PaymentStatus.confirmed, // تأكيد مباشر للدفعات
      );

      await _paymentService.addPayment(payment);
      
      // إعادة تحميل البيانات
      await _loadPaymentSummary();
      
      // مسح النموذج
      _clearForm();
      
      // إشعار الوالد
      widget.onPaymentAdded?.call();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة الدفعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('خطأ في إضافة الدفعة: $e');
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  bool _validateInput() {
    final amount = double.tryParse(_amountController.text);
    
    if (amount == null || amount <= 0) {
      _showError('يرجى إدخال مبلغ صحيح');
      return false;
    }

    if (_paymentSummary != null && amount > _paymentSummary!.remainingAmount + 0.01) {
      _showError('المبلغ المدخل أكبر من المبلغ المتبقي');
      return false;
    }

    if (_selectedMethod.requiresAdditionalInfo && _referenceController.text.isEmpty) {
      _showError('يرجى إدخال المرجع لطريقة الدفع المختارة');
      return false;
    }

    return true;
  }

  void _clearForm() {
    _amountController.text = _paymentSummary?.remainingAmount.toStringAsFixed(2) ?? '0.00';
    _referenceController.clear();
    _bankNameController.clear();
    _accountNumberController.clear();
    _notesController.clear();
    _selectedMethod = PaymentMethod.cash;
    _selectedDate = DateTime.now();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            if (_isLoading)
              const Expanded(child: LoadingWidget())
            else ...[
              _buildPaymentSummary(),
              const SizedBox(height: 16),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: _buildPaymentForm(),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 3,
                      child: _buildPaymentsList(),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 16),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.payment, color: AppColors.primary),
        const SizedBox(width: 8),
        Text(
          'إدارة دفعات الفاتورة ${widget.invoice.invoiceNumber}',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildPaymentSummary() {
    if (_paymentSummary == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الدفعات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الفاتورة',
                    _paymentSummary!.totalAmount,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المدفوع',
                    _paymentSummary!.paidAmount,
                    AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المتبقي',
                    _paymentSummary!.remainingAmount,
                    _paymentSummary!.remainingAmount > 0 ? Colors.orange : AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _paymentSummary!.paidPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _paymentSummary!.isFullyPaid ? AppColors.success : Colors.orange,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'نسبة الدفع: ${_paymentSummary!.paidPercentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ل.س',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPaymentForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إضافة دفعة جديدة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              decoration: InputDecoration(
                labelText: 'المبلغ',
                suffixText: 'ل.س',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<PaymentMethod>(
              value: _selectedMethod,
              decoration: InputDecoration(
                labelText: 'طريقة الدفع',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              ),
              items: PaymentMethod.values.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Row(
                    children: [
                      Icon(Icons.payment, size: 20),
                      const SizedBox(width: 8),
                      Text(method.displayName),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (method) {
                setState(() => _selectedMethod = method!);
              },
            ),
            const SizedBox(height: 12),
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'تاريخ الدفع',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, size: 20),
                    const SizedBox(width: 8),
                    Text('${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}'),
                  ],
                ),
              ),
            ),
            if (_selectedMethod.requiresAdditionalInfo) ...[
              const SizedBox(height: 12),
              TextField(
                controller: _referenceController,
                decoration: InputDecoration(
                  labelText: 'المرجع (رقم الشيك/التحويل)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
              if (_selectedMethod == PaymentMethod.bankTransfer) ...[
                const SizedBox(height: 12),
                TextField(
                  controller: _bankNameController,
                  decoration: InputDecoration(
                    labelText: 'اسم البنك',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: _accountNumberController,
                  decoration: InputDecoration(
                    labelText: 'رقم الحساب',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ],
            ],
            const SizedBox(height: 12),
            TextField(
              controller: _notesController,
              maxLines: 2,
              decoration: InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _addPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('إضافة الدفعة'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsList() {
    if (_paymentSummary == null || _paymentSummary!.payments.isEmpty) {
      return Card(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.payment_outlined, size: 48, color: Colors.grey),
              const SizedBox(height: 8),
              Text(
                'لا توجد دفعات مسجلة',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'الدفعات المسجلة (${_paymentSummary!.payments.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _paymentSummary!.payments.length,
              itemBuilder: (context, index) {
                final payment = _paymentSummary!.payments[index];
                return _buildPaymentItem(payment);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentItem(Payment payment) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Color(int.parse('0xFF${payment.status.colorCode.substring(1)}')),
        child: Icon(
          Icons.payment,
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        '${payment.amount.toStringAsFixed(2)} ل.س',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(payment.method.displayName),
          Text('${payment.paymentDate.day}/${payment.paymentDate.month}/${payment.paymentDate.year}'),
          if (payment.reference != null)
            Text('المرجع: ${payment.reference}'),
        ],
      ),
      trailing: Chip(
        label: Text(
          payment.status.displayName,
          style: const TextStyle(fontSize: 12),
        ),
        backgroundColor: Color(int.parse('0xFF${payment.status.colorCode.substring(1)}')).withValues(alpha: 0.2),
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }
}
