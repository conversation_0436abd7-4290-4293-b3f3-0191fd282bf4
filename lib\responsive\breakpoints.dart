/// نقاط الكسر للتصميم المتجاوب
/// تحدد الأحجام المختلفة للشاشات وتصنيف الأجهزة
class Breakpoints {
  /// الحد الأدنى لعرض الأجهزة اللوحية (600px)
  static const double tablet = 600.0;
  
  /// الحد الأدنى لعرض أجهزة سطح المكتب (1200px)
  static const double desktop = 1200.0;
  
  /// الحد الأدنى لعرض الشاشات الكبيرة جداً (1920px)
  static const double largeDesktop = 1920.0;

  /// نقاط كسر إضافية للتحكم الدقيق
  static const double smallMobile = 320.0;
  static const double largeMobile = 480.0;
  static const double smallTablet = 600.0;
  static const double largeTablet = 900.0;
  static const double smallDesktop = 1200.0;
  static const double mediumDesktop = 1440.0;
  static const double largeDesktopExtended = 2560.0;

  /// التحقق من نوع الجهاز بناءً على العرض
  static DeviceType getDeviceType(double width) {
    if (width >= largeDesktop) {
      return DeviceType.largeDesktop;
    } else if (width >= desktop) {
      return DeviceType.desktop;
    } else if (width >= tablet) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }

  /// الحصول على وصف نصي لنوع الجهاز
  static String getDeviceDescription(double width) {
    final deviceType = getDeviceType(width);
    switch (deviceType) {
      case DeviceType.mobile:
        return 'هاتف محمول';
      case DeviceType.tablet:
        return 'جهاز لوحي';
      case DeviceType.desktop:
        return 'سطح مكتب';
      case DeviceType.largeDesktop:
        return 'شاشة كبيرة';
    }
  }

  /// التحقق من كون الشاشة صغيرة
  static bool isSmallScreen(double width) {
    return width < tablet;
  }

  /// التحقق من كون الشاشة متوسطة
  static bool isMediumScreen(double width) {
    return width >= tablet && width < desktop;
  }

  /// التحقق من كون الشاشة كبيرة
  static bool isLargeScreen(double width) {
    return width >= desktop;
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridColumns(double width) {
    if (width >= largeDesktop) {
      return 6; // 6 أعمدة للشاشات الكبيرة جداً
    } else if (width >= desktop) {
      return 4; // 4 أعمدة لأجهزة سطح المكتب
    } else if (width >= largeTablet) {
      return 3; // 3 أعمدة للأجهزة اللوحية الكبيرة
    } else if (width >= tablet) {
      return 2; // عمودان للأجهزة اللوحية
    } else {
      return 1; // عمود واحد للهواتف
    }
  }

  /// الحصول على نسبة العرض إلى الارتفاع للبطاقات
  static double getCardAspectRatio(double width) {
    if (width >= desktop) {
      return 1.5; // نسبة أوسع لأجهزة سطح المكتب
    } else if (width >= tablet) {
      return 1.3; // نسبة متوسطة للأجهزة اللوحية
    } else {
      return 1.2; // نسبة أضيق للهواتف
    }
  }

  /// الحصول على حجم الخط المناسب للعناوين
  static double getTitleFontSize(double width) {
    if (width >= largeDesktop) {
      return 32.0;
    } else if (width >= desktop) {
      return 28.0;
    } else if (width >= tablet) {
      return 24.0;
    } else {
      return 20.0;
    }
  }

  /// الحصول على حجم الخط المناسب للنصوص العادية
  static double getBodyFontSize(double width) {
    if (width >= largeDesktop) {
      return 18.0;
    } else if (width >= desktop) {
      return 16.0;
    } else if (width >= tablet) {
      return 15.0;
    } else {
      return 14.0;
    }
  }

  /// الحصول على المسافة المناسبة بين العناصر
  static double getSpacing(double width) {
    if (width >= largeDesktop) {
      return 24.0;
    } else if (width >= desktop) {
      return 20.0;
    } else if (width >= tablet) {
      return 16.0;
    } else {
      return 12.0;
    }
  }

  /// الحصول على الحشو المناسب للحاويات
  static double getPadding(double width) {
    if (width >= largeDesktop) {
      return 32.0;
    } else if (width >= desktop) {
      return 24.0;
    } else if (width >= tablet) {
      return 20.0;
    } else {
      return 16.0;
    }
  }

  /// الحصول على ارتفاع شريط التطبيق
  static double getAppBarHeight(double width) {
    if (width >= desktop) {
      return 64.0;
    } else {
      return 56.0;
    }
  }

  /// الحصول على عرض الشريط الجانبي
  static double getSidebarWidth(double width) {
    if (width >= largeDesktop) {
      return 300.0;
    } else if (width >= desktop) {
      return 250.0;
    } else {
      return 200.0;
    }
  }
}

/// تعداد أنواع الأجهزة
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop;

  /// إنشاء نوع الجهاز من العرض
  static DeviceType fromWidth(double width) {
    return Breakpoints.getDeviceType(width);
  }

  /// التحقق من كون الجهاز محمول
  bool get isMobile => this == DeviceType.mobile;

  /// التحقق من كون الجهاز لوحي
  bool get isTablet => this == DeviceType.tablet;

  /// التحقق من كون الجهاز سطح مكتب
  bool get isDesktop => this == DeviceType.desktop;

  /// التحقق من كون الجهاز شاشة كبيرة
  bool get isLargeDesktop => this == DeviceType.largeDesktop;

  /// التحقق من كون الجهاز شاشة صغيرة (هاتف)
  bool get isSmallScreen => isMobile;

  /// التحقق من كون الجهاز شاشة متوسطة (لوحي)
  bool get isMediumScreen => isTablet;

  /// التحقق من كون الجهاز شاشة كبيرة (سطح مكتب أو أكبر)
  bool get isLargeScreen => isDesktop || isLargeDesktop;

  /// الحصول على الوصف النصي
  String get description {
    switch (this) {
      case DeviceType.mobile:
        return 'هاتف محمول';
      case DeviceType.tablet:
        return 'جهاز لوحي';
      case DeviceType.desktop:
        return 'سطح مكتب';
      case DeviceType.largeDesktop:
        return 'شاشة كبيرة';
    }
  }
}
