import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../models/interactive_report_models.dart';
import 'cache_service.dart';
import 'logging_service.dart';

/// خدمة التخزين المؤقت للتقارير
/// توفر تخزين ذكي للتقارير مع إدارة متقدمة للذاكرة
class ReportCacheService {
  static final ReportCacheService _instance = ReportCacheService._internal();
  factory ReportCacheService() => _instance;
  ReportCacheService._internal();

  final CacheService _cacheService = CacheService();

  // إعدادات التخزين المؤقت للتقارير
  static const Duration _defaultReportTtl = Duration(minutes: 15);
  static const Duration _largeReportTtl = Duration(minutes: 5);
  static const Duration _realTimeReportTtl = Duration(minutes: 1);

  // حدود حجم التقارير
  static const int _smallReportThreshold = 100; // عدد السجلات
  static const int _largeReportThreshold = 1000;

  /// حفظ تقرير في التخزين المؤقت
  Future<void> cacheReport({
    required String reportType,
    required Map<String, dynamic> filters,
    required dynamic reportData,
    ReportConfiguration? configuration,
  }) async {
    try {
      final cacheKey = _generateCacheKey(reportType, filters, configuration);
      final reportSize = _calculateReportSize(reportData);
      final ttl = _determineTtl(reportType, reportSize);

      final cacheEntry = ReportCacheEntry(
        reportType: reportType,
        filters: filters,
        data: reportData,
        configuration: configuration,
        cachedAt: DateTime.now(),
        size: reportSize,
      );

      _cacheService.put(
        cacheKey,
        cacheEntry,
        ttl: ttl,
        priority: _determinePriority(reportType, reportSize),
        tags: _generateTags(reportType, filters),
      );

      LoggingService.info(
        'تم حفظ التقرير في التخزين المؤقت',
        category: 'ReportCache',
        data: {
          'reportType': reportType,
          'cacheKey': cacheKey,
          'size': reportSize,
          'ttl': ttl.inMinutes,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ التقرير في التخزين المؤقت',
        category: 'ReportCache',
        data: {'error': e.toString()},
      );
    }
  }

  /// استرجاع تقرير من التخزين المؤقت
  Future<ReportCacheEntry?> getCachedReport({
    required String reportType,
    required Map<String, dynamic> filters,
    ReportConfiguration? configuration,
  }) async {
    try {
      final cacheKey = _generateCacheKey(reportType, filters, configuration);
      final cached = _cacheService.get<ReportCacheEntry>(cacheKey);

      if (cached != null) {
        LoggingService.info(
          'تم استرجاع التقرير من التخزين المؤقت',
          category: 'ReportCache',
          data: {
            'reportType': reportType,
            'cacheKey': cacheKey,
            'age': DateTime.now().difference(cached.cachedAt).inMinutes,
          },
        );
      }

      return cached;
    } catch (e) {
      LoggingService.error(
        'خطأ في استرجاع التقرير من التخزين المؤقت',
        category: 'ReportCache',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// استرجاع تقرير أو إنشاؤه إذا لم يوجد
  Future<T> getOrGenerateReport<T>({
    required String reportType,
    required Map<String, dynamic> filters,
    required Future<T> Function() generator,
    ReportConfiguration? configuration,
  }) async {
    final cached = await getCachedReport(
      reportType: reportType,
      filters: filters,
      configuration: configuration,
    );

    if (cached != null) {
      return cached.data as T;
    }

    final data = await generator();

    await cacheReport(
      reportType: reportType,
      filters: filters,
      reportData: data,
      configuration: configuration,
    );

    return data;
  }

  /// إلغاء تخزين التقارير المرتبطة بجداول معينة
  Future<void> invalidateReportsForTables(List<String> tableNames) async {
    try {
      final tags = tableNames.map((table) => 'table:$table').toList();

      _cacheService.removeByTags(tags);

      LoggingService.info(
        'تم إلغاء تخزين التقارير للجداول المحدثة',
        category: 'ReportCache',
        data: {'tables': tableNames},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء تخزين التقارير',
        category: 'ReportCache',
        data: {'error': e.toString()},
      );
    }
  }

  /// إلغاء تخزين تقرير محدد
  Future<void> invalidateReport({
    required String reportType,
    required Map<String, dynamic> filters,
    ReportConfiguration? configuration,
  }) async {
    try {
      final cacheKey = _generateCacheKey(reportType, filters, configuration);
      _cacheService.remove(cacheKey);

      LoggingService.info(
        'تم إلغاء تخزين التقرير',
        category: 'ReportCache',
        data: {'reportType': reportType, 'cacheKey': cacheKey},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء تخزين التقرير',
        category: 'ReportCache',
        data: {'error': e.toString()},
      );
    }
  }

  /// إلغاء جميع التقارير المخزنة
  Future<void> clearAllReports() async {
    try {
      _cacheService.removeByTags(['report']);

      LoggingService.info(
        'تم إلغاء جميع التقارير المخزنة',
        category: 'ReportCache',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء جميع التقارير',
        category: 'ReportCache',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على إحصائيات التخزين المؤقت للتقارير
  ReportCacheStatistics getStatistics() {
    final cacheStats = _cacheService.getStatistics();

    return ReportCacheStatistics(
      totalCachedReports: cacheStats.totalEntries,
      cacheHitRate: cacheStats.averageAccessCount,
      totalMemoryUsage: cacheStats.memoryUsageEstimate,
      averageReportSize:
          cacheStats.memoryUsageEstimate /
          (cacheStats.totalEntries > 0 ? cacheStats.totalEntries : 1),
    );
  }

  /// توليد مفتاح التخزين المؤقت
  String _generateCacheKey(
    String reportType,
    Map<String, dynamic> filters,
    ReportConfiguration? configuration,
  ) {
    final keyData = {
      'type': reportType,
      'filters': filters,
      'config': configuration?.toMap() ?? {},
    };

    final keyString = json.encode(keyData);
    final bytes = utf8.encode(keyString);
    final digest = sha256.convert(bytes);

    return 'report:${digest.toString().substring(0, 16)}';
  }

  /// حساب حجم التقرير
  int _calculateReportSize(dynamic reportData) {
    if (reportData is List) {
      return reportData.length;
    } else if (reportData is Map) {
      return reportData.length;
    } else if (reportData is InteractiveReportResult) {
      return reportData.data is List ? (reportData.data as List).length : 1;
    }
    return 1;
  }

  /// تحديد مدة التخزين المؤقت
  Duration _determineTtl(String reportType, int size) {
    // التقارير الفورية (مثل الأرصدة الحالية)
    if (reportType.contains('current') || reportType.contains('balance')) {
      return _realTimeReportTtl;
    }

    // التقارير الكبيرة
    if (size > _largeReportThreshold) {
      return _largeReportTtl;
    }

    return _defaultReportTtl;
  }

  /// تحديد أولوية التخزين المؤقت
  CachePriority _determinePriority(String reportType, int size) {
    // التقارير المهمة
    if ([
      'trial_balance',
      'profit_loss',
      'balance_sheet',
    ].contains(reportType)) {
      return CachePriority.high;
    }

    // التقارير الكبيرة لها أولوية منخفضة
    if (size > _largeReportThreshold) {
      return CachePriority.low;
    }

    // التقارير الصغيرة لها أولوية عالية (سريعة التحميل)
    if (size <= _smallReportThreshold) {
      return CachePriority.high;
    }

    return CachePriority.normal;
  }

  /// توليد علامات للتصنيف
  List<String> _generateTags(String reportType, Map<String, dynamic> filters) {
    final tags = <String>['report', 'type:$reportType'];

    // إضافة علامات للجداول المستخدمة
    switch (reportType) {
      case 'trial_balance':
      case 'profit_loss':
      case 'balance_sheet':
        tags.addAll(['table:accounts', 'table:journal_entries']);
        break;
      case 'inventory_report':
        tags.add('table:items');
        break;
      case 'customer_aging':
        tags.addAll(['table:customers', 'table:journal_entries']);
        break;
      case 'sales_analysis':
        tags.add('table:invoices');
        break;
    }

    return tags;
  }
}

/// نموذج إدخال التخزين المؤقت للتقرير
class ReportCacheEntry {
  final String reportType;
  final Map<String, dynamic> filters;
  final dynamic data;
  final ReportConfiguration? configuration;
  final DateTime cachedAt;
  final int size;

  const ReportCacheEntry({
    required this.reportType,
    required this.filters,
    required this.data,
    required this.configuration,
    required this.cachedAt,
    required this.size,
  });

  Map<String, dynamic> toMap() {
    return {
      'reportType': reportType,
      'filters': filters,
      'configuration': configuration?.toMap(),
      'cachedAt': cachedAt.toIso8601String(),
      'size': size,
    };
  }
}

/// إحصائيات التخزين المؤقت للتقارير
class ReportCacheStatistics {
  final int totalCachedReports;
  final double cacheHitRate;
  final int totalMemoryUsage;
  final double averageReportSize;

  const ReportCacheStatistics({
    required this.totalCachedReports,
    required this.cacheHitRate,
    required this.totalMemoryUsage,
    required this.averageReportSize,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalCachedReports': totalCachedReports,
      'cacheHitRate': cacheHitRate,
      'totalMemoryUsage': totalMemoryUsage,
      'averageReportSize': averageReportSize,
    };
  }
}
