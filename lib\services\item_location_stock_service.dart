/// خدمة إدارة رصيد الأصناف في المواقع
/// تدير كميات الأصناف في مواقع المستودعات المختلفة
library;

import '../database/database_helper.dart';
import '../models/item_location_stock.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';
import '../exceptions/validation_exception.dart';

class ItemLocationStockService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جدول رصيد الأصناف في المواقع
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول رصيد الأصناف في المواقع
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS item_location_stock (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          warehouse_id INTEGER NOT NULL,
          location_id INTEGER NOT NULL,
          quantity REAL NOT NULL DEFAULT 0,
          reserved_quantity REAL NOT NULL DEFAULT 0,
          available_quantity REAL NOT NULL DEFAULT 0,
          average_cost REAL NOT NULL DEFAULT 0,
          last_movement_date TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id),
          FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
          FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
          UNIQUE(item_id, location_id)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_item_location_stock_item_id ON item_location_stock(item_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_item_location_stock_location_id ON item_location_stock(location_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_item_location_stock_warehouse_id ON item_location_stock(warehouse_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_item_location_stock_quantity ON item_location_stock(quantity)',
      );
    });

    LoggingService.info(
      'تم إنشاء جدول رصيد الأصناف في المواقع',
      category: 'ItemLocationStockService',
    );
  }

  /// الحصول على رصيد صنف في موقع معين
  Future<ItemLocationStock?> getItemLocationStock(
    int itemId,
    int locationId,
  ) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'item_location_stock',
        where: 'item_id = ? AND location_id = ?',
        whereArgs: [itemId, locationId],
      );

      if (result.isEmpty) return null;

      return ItemLocationStock.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على رصيد الصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      return null;
    }
  }

  /// الحصول على جميع مواقع صنف معين
  Future<List<ItemLocationStock>> getItemStockLocations(int itemId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT ils.*, wl.name as location_name, w.name as warehouse_name
        FROM item_location_stock ils
        JOIN warehouse_locations wl ON ils.location_id = wl.id
        JOIN warehouses w ON ils.warehouse_id = w.id
        WHERE ils.item_id = ?
        ORDER BY w.name ASC, wl.name ASC
      ''',
        [itemId],
      );

      return result.map((map) => ItemLocationStock.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على مواقع الصنف',
        category: 'ItemLocationStockService',
        data: {'item_id': itemId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على ملخص مخزون صنف
  Future<ItemStockSummary> getItemStockSummary(int itemId) async {
    try {
      final stocks = await getItemStockLocations(itemId);
      return ItemStockSummary.fromLocationStocks(itemId, stocks);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على ملخص مخزون الصنف',
        category: 'ItemLocationStockService',
        data: {'item_id': itemId, 'error': e.toString()},
      );
      return ItemStockSummary.fromLocationStocks(itemId, []);
    }
  }

  /// الحصول على أصناف موقع معين
  Future<List<ItemLocationStock>> getLocationItems(int locationId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT ils.*, i.name as item_name, i.code as item_code, i.unit
        FROM item_location_stock ils
        JOIN ${AppConstants.itemsTable} i ON ils.item_id = i.id
        WHERE ils.location_id = ? AND ils.quantity > 0
        ORDER BY i.name ASC
      ''',
        [locationId],
      );

      return result.map((map) => ItemLocationStock.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على أصناف الموقع',
        category: 'ItemLocationStockService',
        data: {'location_id': locationId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث رصيد صنف في موقع
  Future<void> updateItemLocationStock({
    required int itemId,
    required int warehouseId,
    required int locationId,
    required double quantity,
    required double unitCost,
    String movementType = 'adjustment',
    String? description,
  }) async {
    try {
      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // الحصول على الرصيد الحالي أو إنشاء جديد
        final existingStock = await getItemLocationStock(itemId, locationId);

        if (existingStock == null) {
          // إنشاء رصيد جديد
          final newStock = ItemLocationStock(
            itemId: itemId,
            warehouseId: warehouseId,
            locationId: locationId,
            quantity: quantity,
            averageCost: unitCost,
          );

          await txn.insert('item_location_stock', newStock.toMap());
        } else {
          // تحديث الرصيد الموجود
          final updatedStock = existingStock.updateQuantity(quantity, unitCost);

          await txn.update(
            'item_location_stock',
            updatedStock.toMap(),
            where: 'id = ?',
            whereArgs: [existingStock.id],
          );
        }
      });

      await AuditService.log(
        action: 'stock_updated',
        entityType: 'item_location_stock',
        entityId: itemId,
        description: description ?? 'تم تحديث رصيد الصنف في الموقع',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم تحديث رصيد الصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantity,
          'movement_type': movementType,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث رصيد الصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إضافة كمية لصنف في موقع
  Future<void> addItemToLocation({
    required int itemId,
    required int warehouseId,
    required int locationId,
    required double quantity,
    required double unitCost,
    String? description,
  }) async {
    try {
      if (quantity <= 0) {
        throw ValidationException('الكمية يجب أن تكون أكبر من الصفر');
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        final existingStock = await getItemLocationStock(itemId, locationId);

        if (existingStock == null) {
          // إنشاء رصيد جديد
          final newStock = ItemLocationStock(
            itemId: itemId,
            warehouseId: warehouseId,
            locationId: locationId,
            quantity: quantity,
            averageCost: unitCost,
          );

          await txn.insert('item_location_stock', newStock.toMap());
        } else {
          // إضافة للرصيد الموجود
          final updatedStock = existingStock.addQuantity(quantity, unitCost);

          await txn.update(
            'item_location_stock',
            updatedStock.toMap(),
            where: 'id = ?',
            whereArgs: [existingStock.id],
          );
        }
      });

      await AuditService.log(
        action: 'stock_added',
        entityType: 'item_location_stock',
        entityId: itemId,
        description: description ?? 'تم إضافة كمية للصنف في الموقع',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم إضافة كمية للصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantity,
          'unit_cost': unitCost,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة كمية للصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// خصم كمية من صنف في موقع
  Future<void> subtractItemFromLocation({
    required int itemId,
    required int locationId,
    required double quantity,
    String? description,
  }) async {
    try {
      if (quantity <= 0) {
        throw ValidationException('الكمية يجب أن تكون أكبر من الصفر');
      }

      final existingStock = await getItemLocationStock(itemId, locationId);

      if (existingStock == null) {
        throw ValidationException('الصنف غير موجود في هذا الموقع');
      }

      if (quantity > existingStock.availableQuantity) {
        throw ValidationException(
          'الكمية المطلوب خصمها أكبر من الكمية المتاحة',
        );
      }

      final db = await _databaseHelper.database;

      final updatedStock = existingStock.subtractQuantity(quantity);

      await db.update(
        'item_location_stock',
        updatedStock.toMap(),
        where: 'id = ?',
        whereArgs: [existingStock.id],
      );

      await AuditService.log(
        action: 'stock_subtracted',
        entityType: 'item_location_stock',
        entityId: itemId,
        description: description ?? 'تم خصم كمية من الصنف في الموقع',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم خصم كمية من الصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantity,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في خصم كمية من الصنف في الموقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// نقل كمية بين مواقع
  Future<void> transferItemBetweenLocations({
    required int itemId,
    required int fromLocationId,
    required int toLocationId,
    required int toWarehouseId,
    required double quantity,
    String? description,
  }) async {
    try {
      if (quantity <= 0) {
        throw ValidationException('الكمية يجب أن تكون أكبر من الصفر');
      }

      if (fromLocationId == toLocationId) {
        throw ValidationException('لا يمكن النقل إلى نفس الموقع');
      }

      final fromStock = await getItemLocationStock(itemId, fromLocationId);

      if (fromStock == null) {
        throw ValidationException('الصنف غير موجود في الموقع المصدر');
      }

      if (quantity > fromStock.availableQuantity) {
        throw ValidationException(
          'الكمية المطلوب نقلها أكبر من الكمية المتاحة',
        );
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // خصم من الموقع المصدر
        final updatedFromStock = fromStock.subtractQuantity(quantity);
        await txn.update(
          'item_location_stock',
          updatedFromStock.toMap(),
          where: 'id = ?',
          whereArgs: [fromStock.id],
        );

        // إضافة للموقع الهدف
        final toStock = await getItemLocationStock(itemId, toLocationId);

        if (toStock == null) {
          // إنشاء رصيد جديد في الموقع الهدف
          final newStock = ItemLocationStock(
            itemId: itemId,
            warehouseId: toWarehouseId,
            locationId: toLocationId,
            quantity: quantity,
            averageCost: fromStock.averageCost,
          );

          await txn.insert('item_location_stock', newStock.toMap());
        } else {
          // إضافة للرصيد الموجود
          final updatedToStock = toStock.addQuantity(
            quantity,
            fromStock.averageCost,
          );

          await txn.update(
            'item_location_stock',
            updatedToStock.toMap(),
            where: 'id = ?',
            whereArgs: [toStock.id],
          );
        }
      });

      await AuditService.log(
        action: 'stock_transferred',
        entityType: 'item_location_stock',
        entityId: itemId,
        description: description ?? 'تم نقل كمية بين المواقع',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم نقل كمية بين المواقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'from_location_id': fromLocationId,
          'to_location_id': toLocationId,
          'quantity': quantity,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في نقل كمية بين المواقع',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'from_location_id': fromLocationId,
          'to_location_id': toLocationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حجز كمية من صنف في موقع
  Future<void> reserveItemQuantity({
    required int itemId,
    required int locationId,
    required double quantity,
    String? description,
  }) async {
    try {
      if (quantity <= 0) {
        throw ValidationException('الكمية يجب أن تكون أكبر من الصفر');
      }

      final stock = await getItemLocationStock(itemId, locationId);

      if (stock == null) {
        throw ValidationException('الصنف غير موجود في هذا الموقع');
      }

      if (quantity > stock.availableQuantity) {
        throw ValidationException(
          'الكمية المطلوب حجزها أكبر من الكمية المتاحة',
        );
      }

      final db = await _databaseHelper.database;

      final updatedStock = stock.reserveQuantity(quantity);

      await db.update(
        'item_location_stock',
        updatedStock.toMap(),
        where: 'id = ?',
        whereArgs: [stock.id],
      );

      await AuditService.log(
        action: 'stock_reserved',
        entityType: 'item_location_stock',
        entityId: itemId,
        description: description ?? 'تم حجز كمية من الصنف',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم حجز كمية من الصنف',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantity,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حجز كمية من الصنف',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إلغاء حجز كمية من صنف في موقع
  Future<void> unreserveItemQuantity({
    required int itemId,
    required int locationId,
    required double quantity,
    String? description,
  }) async {
    try {
      if (quantity <= 0) {
        throw ValidationException('الكمية يجب أن تكون أكبر من الصفر');
      }

      final stock = await getItemLocationStock(itemId, locationId);

      if (stock == null) {
        throw ValidationException('الصنف غير موجود في هذا الموقع');
      }

      if (quantity > stock.reservedQuantity) {
        throw ValidationException(
          'الكمية المطلوب إلغاء حجزها أكبر من الكمية المحجوزة',
        );
      }

      final db = await _databaseHelper.database;

      final updatedStock = stock.unreserveQuantity(quantity);

      await db.update(
        'item_location_stock',
        updatedStock.toMap(),
        where: 'id = ?',
        whereArgs: [stock.id],
      );

      await AuditService.log(
        action: 'stock_unreserved',
        entityType: 'item_location_stock',
        entityId: itemId,
        description: description ?? 'تم إلغاء حجز كمية من الصنف',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم إلغاء حجز كمية من الصنف',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'quantity': quantity,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء حجز كمية من الصنف',
        category: 'ItemLocationStockService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات المخزون
  Future<Map<String, dynamic>> getInventoryStatistics({
    int? warehouseId,
    int? locationId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += 'WHERE warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (locationId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'location_id = ?';
        whereArgs.add(locationId);
      }

      final result = await db.rawQuery('''
        SELECT
          COUNT(*) as total_items,
          COUNT(CASE WHEN quantity > 0 THEN 1 END) as items_in_stock,
          COUNT(CASE WHEN quantity = 0 THEN 1 END) as items_out_of_stock,
          COUNT(CASE WHEN reserved_quantity > 0 THEN 1 END) as items_with_reservations,
          SUM(quantity) as total_quantity,
          SUM(reserved_quantity) as total_reserved_quantity,
          SUM(available_quantity) as total_available_quantity,
          SUM(quantity * average_cost) as total_value
        FROM item_location_stock
        $whereClause
      ''', whereArgs);

      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'total_items': row['total_items'] ?? 0,
          'items_in_stock': row['items_in_stock'] ?? 0,
          'items_out_of_stock': row['items_out_of_stock'] ?? 0,
          'items_with_reservations': row['items_with_reservations'] ?? 0,
          'total_quantity': (row['total_quantity'] as num?)?.toDouble() ?? 0.0,
          'total_reserved_quantity':
              (row['total_reserved_quantity'] as num?)?.toDouble() ?? 0.0,
          'total_available_quantity':
              (row['total_available_quantity'] as num?)?.toDouble() ?? 0.0,
          'total_value': (row['total_value'] as num?)?.toDouble() ?? 0.0,
        };
      }

      return {};
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات المخزون',
        category: 'ItemLocationStockService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// البحث عن الأصناف في المواقع
  Future<List<ItemLocationStock>> searchItemsInLocations({
    String? searchTerm,
    int? warehouseId,
    int? locationId,
    bool? hasStock,
    bool? hasReservations,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      // بناء شروط البحث
      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += '(i.name LIKE ? OR i.code LIKE ?)';
        whereArgs.addAll(['%$searchTerm%', '%$searchTerm%']);
      }

      if (warehouseId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'ils.warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (locationId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'ils.location_id = ?';
        whereArgs.add(locationId);
      }

      if (hasStock == true) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'ils.quantity > 0';
      } else if (hasStock == false) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'ils.quantity = 0';
      }

      if (hasReservations == true) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'ils.reserved_quantity > 0';
      } else if (hasReservations == false) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'ils.reserved_quantity = 0';
      }

      String limitClause = '';
      if (limit != null) {
        limitClause = 'LIMIT $limit';
        if (offset != null) {
          limitClause += ' OFFSET $offset';
        }
      }

      final result = await db.rawQuery('''
        SELECT ils.*,
               i.name as item_name,
               i.code as item_code,
               i.unit,
               wl.name as location_name,
               w.name as warehouse_name
        FROM item_location_stock ils
        JOIN ${AppConstants.itemsTable} i ON ils.item_id = i.id
        JOIN warehouse_locations wl ON ils.location_id = wl.id
        JOIN warehouses w ON ils.warehouse_id = w.id
        $whereClause
        ORDER BY w.name ASC, wl.name ASC, i.name ASC
        $limitClause
      ''', whereArgs);

      return result.map((map) => ItemLocationStock.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث عن الأصناف في المواقع',
        category: 'ItemLocationStockService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }
}
