/// بطاقة الفاتورة المتكررة
/// تعرض معلومات الفاتورة المتكررة مع الإجراءات المتاحة
library;

import 'package:flutter/material.dart';
import '../models/recurring_invoice.dart';
import '../constants/app_colors.dart';

class RecurringInvoiceCard extends StatelessWidget {
  final RecurringInvoice recurringInvoice;
  final VoidCallback? onTap;
  final VoidCallback? onToggleStatus;
  final VoidCallback? onGenerateInvoice;
  final VoidCallback? onDelete;

  const RecurringInvoiceCard({
    super.key,
    required this.recurringInvoice,
    this.onTap,
    this.onToggleStatus,
    this.onGenerateInvoice,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              _buildDetails(),
              const SizedBox(height: 12),
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                recurringInvoice.templateName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                recurringInvoice.clientName,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        _buildStatusChip(),
      ],
    );
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String displayText;
    IconData icon;

    if (!recurringInvoice.isActive) {
      backgroundColor = Colors.grey[100]!;
      textColor = Colors.grey[700]!;
      displayText = 'معطلة';
      icon = Icons.pause_circle;
    } else if (recurringInvoice.isDue) {
      backgroundColor = Colors.orange[100]!;
      textColor = Colors.orange[700]!;
      displayText = 'مستحقة';
      icon = Icons.schedule;
    } else if (recurringInvoice.isExpired) {
      backgroundColor = Colors.red[100]!;
      textColor = Colors.red[700]!;
      displayText = 'منتهية';
      icon = Icons.cancel;
    } else {
      backgroundColor = Colors.green[100]!;
      textColor = Colors.green[700]!;
      displayText = 'مفعلة';
      icon = Icons.check_circle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: textColor),
          const SizedBox(width: 4),
          Text(
            displayText,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetails() {
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.repeat,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'التكرار: ${recurringInvoice.displayFrequency}',
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              Icons.schedule,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'التاريخ التالي: ${_formatDate(recurringInvoice.nextGenerationDate)}',
                style: TextStyle(
                  fontSize: 14,
                  color: recurringInvoice.isDue ? Colors.orange : null,
                  fontWeight: recurringInvoice.isDue ? FontWeight.w500 : null,
                ),
              ),
            ),
          ],
        ),
        if (recurringInvoice.lastGeneratedDate != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.history,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'آخر إنشاء: ${_formatDate(recurringInvoice.lastGeneratedDate!)}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ],
        if (recurringInvoice.endDate != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.event_busy,
                size: 16,
                color: recurringInvoice.isExpired ? Colors.red : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'تاريخ الانتهاء: ${_formatDate(recurringInvoice.endDate!)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: recurringInvoice.isExpired ? Colors.red : null,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (recurringInvoice.isActive && recurringInvoice.shouldGenerate) ...[
          TextButton.icon(
            onPressed: onGenerateInvoice,
            icon: const Icon(Icons.add_circle, size: 16),
            label: const Text('إنشاء فاتورة'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.success,
            ),
          ),
          const SizedBox(width: 8),
        ],
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility, size: 16),
                  SizedBox(width: 8),
                  Text('عرض التفاصيل'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'toggle_status',
              child: Row(
                children: [
                  Icon(
                    recurringInvoice.isActive ? Icons.pause : Icons.play_arrow,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(recurringInvoice.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                ],
              ),
            ),
            if (recurringInvoice.isActive)
              const PopupMenuItem(
                value: 'generate',
                child: Row(
                  children: [
                    Icon(Icons.add_circle, size: 16),
                    SizedBox(width: 8),
                    Text('إنشاء فاتورة'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'view':
        onTap?.call();
        break;
      case 'toggle_status':
        onToggleStatus?.call();
        break;
      case 'generate':
        onGenerateInvoice?.call();
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
