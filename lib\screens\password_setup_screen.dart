import 'package:flutter/material.dart';
import '../services/encryption_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_theme.dart';
import 'home_screen.dart';

/// شاشة إعداد كلمة مرور قاعدة البيانات
class PasswordSetupScreen extends StatefulWidget {
  final bool isFirstTime;

  const PasswordSetupScreen({super.key, this.isFirstTime = true});

  @override
  State<PasswordSetupScreen> createState() => _PasswordSetupScreenState();
}

class _PasswordSetupScreenState extends State<PasswordSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _oldPasswordController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isOldPasswordVisible = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // الشعار والعنوان
              _buildHeader(),

              const SizedBox(height: 40),

              // النموذج
              _buildForm(),

              const SizedBox(height: 24),

              // متطلبات كلمة المرور
              _buildPasswordRequirements(),

              const SizedBox(height: 32),

              // زر الحفظ
              _buildSaveButton(),

              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                _buildErrorMessage(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(Icons.security, size: 80, color: AppTheme.primaryColor),
        const SizedBox(height: 16),
        Text(
          widget.isFirstTime
              ? 'إعداد كلمة مرور قاعدة البيانات'
              : 'تغيير كلمة المرور',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.textColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          widget.isFirstTime
              ? 'لحماية بياناتك، يرجى إنشاء كلمة مرور قوية'
              : 'أدخل كلمة مرور جديدة لحماية بياناتك',
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.textColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // كلمة المرور القديمة (فقط عند تغيير كلمة المرور)
          if (!widget.isFirstTime) ...[
            TextFormField(
              controller: _oldPasswordController,
              obscureText: !_isOldPasswordVisible,
              decoration: InputDecoration(
                labelText: 'كلمة المرور الحالية',
                prefixIcon: const Icon(Icons.lock_clock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _isOldPasswordVisible
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _isOldPasswordVisible = !_isOldPasswordVisible;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'كلمة المرور الحالية مطلوبة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
          ],

          // كلمة المرور الجديدة
          TextFormField(
            controller: _passwordController,
            obscureText: !_isPasswordVisible,
            decoration: InputDecoration(
              labelText: widget.isFirstTime
                  ? 'كلمة المرور'
                  : 'كلمة المرور الجديدة',
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'كلمة المرور مطلوبة';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // تأكيد كلمة المرور
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: !_isConfirmPasswordVisible,
            decoration: InputDecoration(
              labelText: widget.isFirstTime
                  ? 'تأكيد كلمة المرور'
                  : 'تأكيد كلمة المرور الجديدة',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(
                  _isConfirmPasswordVisible
                      ? Icons.visibility_off
                      : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                  });
                },
              ),
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'تأكيد كلمة المرور مطلوب';
              }
              if (value != _passwordController.text) {
                return 'كلمات المرور غير متطابقة';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordRequirements() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'متطلبات كلمة المرور:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            EncryptionService.getPasswordRequirements(),
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textColor.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _handleSave,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              widget.isFirstTime ? 'إنشاء كلمة المرور' : 'تغيير كلمة المرور',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final password = _passwordController.text;

      bool success;
      if (widget.isFirstTime) {
        success = await EncryptionService.setupDatabasePassword(password);
      } else {
        // تغيير كلمة المرور باستخدام كلمة المرور القديمة
        final oldPassword = _oldPasswordController.text;
        success = await EncryptionService.changeDatabasePassword(
          oldPassword,
          password,
        );
      }

      if (success) {
        LoggingService.security(
          'تم ${widget.isFirstTime ? "إعداد" : "تغيير"} كلمة مرور قاعدة البيانات',
          category: 'PasswordSetup',
        );

        // تسجيل العملية في سجل المراجعة
        if (widget.isFirstTime) {
          await AuditService.log(
            action: 'PASSWORD_SETUP',
            entityType: 'SYSTEM',
            description: 'تم إعداد كلمة مرور قاعدة البيانات لأول مرة',
            severity: 'SUCCESS',
            category: 'Security',
          );
        } else {
          await AuditService.logPasswordChange(
            description: 'تم تغيير كلمة مرور قاعدة البيانات',
          );
        }

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      } else {
        setState(() {
          if (widget.isFirstTime) {
            _errorMessage =
                'فشل في إعداد كلمة المرور. تأكد من قوة كلمة المرور.';
          } else {
            _errorMessage =
                'فشل في تغيير كلمة المرور. تأكد من صحة كلمة المرور الحالية وقوة كلمة المرور الجديدة.';
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ غير متوقع: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
