import 'dart:async';
import 'dart:collection';
import 'package:sqflite_sqlcipher/sqflite.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';

/// خدمة مجموعة اتصالات قاعدة البيانات
/// توفر إدارة محسنة لاتصالات قاعدة البيانات لتحسين الأداء
class ConnectionPoolService {
  static final ConnectionPoolService _instance =
      ConnectionPoolService._internal();
  factory ConnectionPoolService() => _instance;
  ConnectionPoolService._internal();

  final Queue<PooledConnection> _availableConnections =
      Queue<PooledConnection>();
  final Set<PooledConnection> _activeConnections = <PooledConnection>{};

  // إعدادات المجموعة
  static const int minPoolSize = 2;
  static const int maxPoolSize = 10;
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration idleTimeout = Duration(minutes: 5);
  static const Duration maxConnectionAge = Duration(hours: 1);

  bool _isInitialized = false;
  Timer? _cleanupTimer;

  /// تهيئة مجموعة الاتصالات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info(
        'بدء تهيئة مجموعة اتصالات قاعدة البيانات',
        category: 'ConnectionPool',
      );

      // إنشاء الحد الأدنى من الاتصالات
      for (int i = 0; i < minPoolSize; i++) {
        final connection = await _createConnection();
        _availableConnections.add(connection);
      }

      // بدء مؤقت التنظيف
      _startCleanupTimer();

      _isInitialized = true;

      LoggingService.info(
        'تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح',
        category: 'ConnectionPool',
        data: {
          'minPoolSize': minPoolSize,
          'maxPoolSize': maxPoolSize,
          'initialConnections': _availableConnections.length,
        },
      );
    } catch (e) {
      LoggingService.error(
        'فشل في تهيئة مجموعة اتصالات قاعدة البيانات',
        category: 'ConnectionPool',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على اتصال من المجموعة
  Future<PooledConnection> getConnection() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // البحث عن اتصال متاح
      if (_availableConnections.isNotEmpty) {
        final connection = _availableConnections.removeFirst();

        // التحقق من صحة الاتصال
        if (await _isConnectionValid(connection)) {
          _activeConnections.add(connection);
          connection._markAsActive();

          LoggingService.debug(
            'تم استرجاع اتصال من المجموعة',
            category: 'ConnectionPool',
            data: {
              'connectionId': connection.id,
              'activeConnections': _activeConnections.length,
              'availableConnections': _availableConnections.length,
            },
          );

          return connection;
        } else {
          // الاتصال غير صالح، إنشاء اتصال جديد
          await connection._close();
        }
      }

      // إنشاء اتصال جديد إذا لم نصل للحد الأقصى
      if (_getTotalConnections() < maxPoolSize) {
        final connection = await _createConnection();
        _activeConnections.add(connection);
        connection._markAsActive();

        LoggingService.debug(
          'تم إنشاء اتصال جديد',
          category: 'ConnectionPool',
          data: {
            'connectionId': connection.id,
            'totalConnections': _getTotalConnections(),
          },
        );

        return connection;
      }

      // انتظار توفر اتصال
      LoggingService.warning(
        'وصلت مجموعة الاتصالات للحد الأقصى، انتظار توفر اتصال',
        category: 'ConnectionPool',
        data: {'maxPoolSize': maxPoolSize},
      );

      return await _waitForAvailableConnection();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على اتصال من المجموعة',
        category: 'ConnectionPool',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إرجاع اتصال إلى المجموعة
  void returnConnection(PooledConnection connection) {
    try {
      if (!_activeConnections.contains(connection)) {
        LoggingService.warning(
          'محاولة إرجاع اتصال غير نشط',
          category: 'ConnectionPool',
          data: {'connectionId': connection.id},
        );
        return;
      }

      _activeConnections.remove(connection);

      // التحقق من صحة الاتصال قبل الإرجاع
      if (connection._isValid()) {
        connection._markAsIdle();
        _availableConnections.add(connection);

        LoggingService.debug(
          'تم إرجاع اتصال إلى المجموعة',
          category: 'ConnectionPool',
          data: {
            'connectionId': connection.id,
            'activeConnections': _activeConnections.length,
            'availableConnections': _availableConnections.length,
          },
        );
      } else {
        // الاتصال غير صالح، إغلاقه
        connection._close();
        LoggingService.debug(
          'تم إغلاق اتصال غير صالح',
          category: 'ConnectionPool',
          data: {'connectionId': connection.id},
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إرجاع اتصال إلى المجموعة',
        category: 'ConnectionPool',
        data: {'connectionId': connection.id, 'error': e.toString()},
      );
    }
  }

  /// تنفيذ عملية مع اتصال من المجموعة
  Future<T> executeWithConnection<T>(
    Future<T> Function(Database db) operation,
  ) async {
    final connection = await getConnection();
    try {
      return await operation(connection.database);
    } finally {
      returnConnection(connection);
    }
  }

  /// تنفيذ معاملة مع اتصال من المجموعة
  Future<T> executeTransaction<T>(
    Future<T> Function(Transaction txn) operation,
  ) async {
    final connection = await getConnection();
    try {
      return await connection.database.transaction(operation);
    } finally {
      returnConnection(connection);
    }
  }

  /// إغلاق جميع الاتصالات
  Future<void> closeAll() async {
    try {
      LoggingService.info(
        'بدء إغلاق جميع اتصالات قاعدة البيانات',
        category: 'ConnectionPool',
      );

      // إيقاف مؤقت التنظيف
      _cleanupTimer?.cancel();

      // إغلاق الاتصالات النشطة
      for (final connection in _activeConnections) {
        await connection._close();
      }
      _activeConnections.clear();

      // إغلاق الاتصالات المتاحة
      for (final connection in _availableConnections) {
        await connection._close();
      }
      _availableConnections.clear();

      _isInitialized = false;

      LoggingService.info(
        'تم إغلاق جميع اتصالات قاعدة البيانات',
        category: 'ConnectionPool',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إغلاق اتصالات قاعدة البيانات',
        category: 'ConnectionPool',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء اتصال جديد
  Future<PooledConnection> _createConnection() async {
    final database = await DatabaseHelper().database;
    return PooledConnection._(database);
  }

  /// التحقق من صحة الاتصال
  Future<bool> _isConnectionValid(PooledConnection connection) async {
    try {
      // تنفيذ استعلام بسيط للتحقق من الاتصال
      await connection.database.rawQuery('SELECT 1');
      return connection._isValid();
    } catch (e) {
      return false;
    }
  }

  /// انتظار توفر اتصال
  Future<PooledConnection> _waitForAvailableConnection() async {
    final completer = Completer<PooledConnection>();

    Timer.periodic(Duration(milliseconds: 100), (timer) {
      if (_availableConnections.isNotEmpty) {
        timer.cancel();
        final connection = _availableConnections.removeFirst();
        _activeConnections.add(connection);
        connection._markAsActive();
        completer.complete(connection);
      }
    });

    // مهلة زمنية للانتظار
    Timer(connectionTimeout, () {
      if (!completer.isCompleted) {
        completer.completeError(
          TimeoutException(
            'انتهت مهلة انتظار اتصال قاعدة البيانات',
            connectionTimeout,
          ),
        );
      }
    });

    return completer.future;
  }

  /// بدء مؤقت التنظيف
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      _cleanupIdleConnections();
    });
  }

  /// تنظيف الاتصالات الخاملة
  void _cleanupIdleConnections() {
    try {
      final now = DateTime.now();
      final connectionsToRemove = <PooledConnection>[];

      // البحث عن الاتصالات المنتهية الصلاحية أو الخاملة
      for (final connection in _availableConnections) {
        if (now.difference(connection._createdAt) > maxConnectionAge ||
            now.difference(connection._lastUsedAt) > idleTimeout) {
          connectionsToRemove.add(connection);
        }
      }

      // إزالة الاتصالات المنتهية الصلاحية
      for (final connection in connectionsToRemove) {
        _availableConnections.remove(connection);
        connection._close();
      }

      // التأكد من وجود الحد الأدنى من الاتصالات
      final connectionsNeeded = minPoolSize - _availableConnections.length;
      if (connectionsNeeded > 0) {
        _ensureMinimumConnections(connectionsNeeded);
      }

      if (connectionsToRemove.isNotEmpty) {
        LoggingService.debug(
          'تم تنظيف الاتصالات الخاملة',
          category: 'ConnectionPool',
          data: {
            'removedConnections': connectionsToRemove.length,
            'remainingConnections': _availableConnections.length,
          },
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف الاتصالات الخاملة',
        category: 'ConnectionPool',
        data: {'error': e.toString()},
      );
    }
  }

  /// ضمان وجود الحد الأدنى من الاتصالات
  void _ensureMinimumConnections(int needed) async {
    try {
      for (int i = 0; i < needed; i++) {
        final connection = await _createConnection();
        _availableConnections.add(connection);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء اتصالات إضافية',
        category: 'ConnectionPool',
        data: {'needed': needed, 'error': e.toString()},
      );
    }
  }

  /// الحصول على العدد الإجمالي للاتصالات
  int _getTotalConnections() {
    return _activeConnections.length + _availableConnections.length;
  }

  /// الحصول على إحصائيات المجموعة
  ConnectionPoolStatistics getStatistics() {
    return ConnectionPoolStatistics(
      totalConnections: _getTotalConnections(),
      activeConnections: _activeConnections.length,
      availableConnections: _availableConnections.length,
      maxPoolSize: maxPoolSize,
      minPoolSize: minPoolSize,
      isInitialized: _isInitialized,
    );
  }
}

/// اتصال مجمع
class PooledConnection {
  final Database database;
  final String id;
  final DateTime _createdAt;
  DateTime _lastUsedAt;
  bool _isActive;

  PooledConnection._(this.database)
    : id = DateTime.now().millisecondsSinceEpoch.toString(),
      _createdAt = DateTime.now(),
      _lastUsedAt = DateTime.now(),
      _isActive = false;

  /// تحديد الاتصال كنشط
  void _markAsActive() {
    _isActive = true;
    _lastUsedAt = DateTime.now();
  }

  /// تحديد الاتصال كخامل
  void _markAsIdle() {
    _isActive = false;
    _lastUsedAt = DateTime.now();
  }

  /// التحقق من صحة الاتصال
  bool _isValid() {
    final now = DateTime.now();
    return now.difference(_createdAt) < ConnectionPoolService.maxConnectionAge;
  }

  /// إغلاق الاتصال
  Future<void> _close() async {
    try {
      await database.close();
    } catch (e) {
      // تجاهل أخطاء الإغلاق
    }
  }

  @override
  String toString() {
    return 'PooledConnection(id: $id, active: $_isActive, age: ${DateTime.now().difference(_createdAt).inMinutes}min)';
  }
}

/// إحصائيات مجموعة الاتصالات
class ConnectionPoolStatistics {
  final int totalConnections;
  final int activeConnections;
  final int availableConnections;
  final int maxPoolSize;
  final int minPoolSize;
  final bool isInitialized;

  const ConnectionPoolStatistics({
    required this.totalConnections,
    required this.activeConnections,
    required this.availableConnections,
    required this.maxPoolSize,
    required this.minPoolSize,
    required this.isInitialized,
  });

  /// نسبة الاستخدام
  double get utilizationRatio =>
      totalConnections > 0 ? activeConnections / totalConnections : 0.0;

  /// هل المجموعة ممتلئة؟
  bool get isFull => totalConnections >= maxPoolSize;

  /// هل المجموعة في الحد الأدنى؟
  bool get isAtMinimum => totalConnections <= minPoolSize;

  @override
  String toString() {
    return 'ConnectionPoolStatistics(total: $totalConnections, active: $activeConnections, available: $availableConnections, utilization: ${(utilizationRatio * 100).toStringAsFixed(1)}%)';
  }
}
