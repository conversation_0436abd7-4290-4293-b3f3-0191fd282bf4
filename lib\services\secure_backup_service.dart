import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite_sqlcipher/sqflite.dart';
import 'package:smart_ledger/database/database_helper.dart';
import 'package:smart_ledger/constants/app_constants.dart';
import 'package:smart_ledger/services/logging_service.dart';
import 'package:smart_ledger/services/audit_service.dart';

/// خدمة النسخ الاحتياطي المشفر
/// تدير إنشاء واستعادة النسخ الاحتياطية المشفرة لقاعدة البيانات
class SecureBackupService {
  static const String _backupPasswordKey = 'backup_password_hash';
  static const String _backupSaltKey = 'backup_salt';
  static const String _lastBackupKey = 'last_backup_date';
  static const String _autoBackupEnabledKey = 'auto_backup_enabled';
  static const String _backupIntervalKey = 'backup_interval_hours';
  static const String _maxBackupsKey = 'max_backups_count';
  static const String _backupLocationKey = 'backup_location';

  // إعدادات افتراضية
  static const int _defaultBackupInterval = 24; // 24 ساعة
  static const int _defaultMaxBackups = 10;
  static const String _backupFileExtension = '.slb'; // Smart Ledger Backup

  /// إعداد كلمة مرور النسخ الاحتياطي
  static Future<bool> setupBackupPassword(String password) async {
    try {
      // التحقق من قوة كلمة المرور
      if (!_isPasswordStrong(password)) {
        LoggingService.warning(
          'كلمة مرور النسخ الاحتياطي ضعيفة',
          category: 'Backup',
        );
        return false;
      }

      // إنشاء salt عشوائي
      final salt = _generateSalt();

      // تشفير كلمة المرور
      final hashedPassword = _hashPassword(password, salt);

      // حفظ البيانات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backupPasswordKey, hashedPassword);
      await prefs.setString(_backupSaltKey, salt);

      LoggingService.security(
        'تم إعداد كلمة مرور النسخ الاحتياطي بنجاح',
        category: 'Backup',
      );

      await AuditService.logCreate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'إعداد النسخ الاحتياطي',
        newValues: {'action': 'setup_backup_password'},
        description: 'تم إعداد كلمة مرور النسخ الاحتياطي',
        category: 'Security',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في إعداد كلمة مرور النسخ الاحتياطي',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من كلمة مرور النسخ الاحتياطي
  static Future<bool> verifyBackupPassword(String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedHash = prefs.getString(_backupPasswordKey);
      final salt = prefs.getString(_backupSaltKey);

      if (storedHash == null || salt == null) {
        LoggingService.warning(
          'كلمة مرور النسخ الاحتياطي غير محددة',
          category: 'Backup',
        );
        return false;
      }

      final hashedPassword = _hashPassword(password, salt);
      return hashedPassword == storedHash;
    } catch (e) {
      LoggingService.error(
        'فشل في التحقق من كلمة مرور النسخ الاحتياطي',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// إنشاء نسخة احتياطية مشفرة
  static Future<String?> createBackup({
    required String backupPassword,
    String? customPath,
    String? description,
  }) async {
    try {
      // التحقق من كلمة مرور النسخ الاحتياطي
      if (!await verifyBackupPassword(backupPassword)) {
        LoggingService.error(
          'كلمة مرور النسخ الاحتياطي غير صحيحة',
          category: 'Backup',
        );
        return null;
      }

      LoggingService.info('بدء إنشاء النسخة الاحتياطية', category: 'Backup');

      // الحصول على قاعدة البيانات
      final db = await DatabaseHelper().database;

      // تصدير البيانات
      final backupData = await _exportDatabaseData(db);

      // تشفير البيانات
      final encryptedData = await _encryptBackupData(
        backupData,
        backupPassword,
      );

      // إنشاء ملف النسخة الاحتياطية
      final backupPath = await _createBackupFile(encryptedData, customPath);

      if (backupPath != null) {
        // تحديث تاريخ آخر نسخة احتياطية
        await _updateLastBackupDate();

        // تنظيف النسخ القديمة
        await _cleanupOldBackups();

        LoggingService.info(
          'تم إنشاء النسخة الاحتياطية بنجاح',
          category: 'Backup',
          data: {'path': backupPath},
        );

        await AuditService.logCreate(
          entityType: AppConstants.auditEntitySystem,
          entityId: 0,
          entityName: 'نسخة احتياطية',
          newValues: {
            'path': backupPath,
            'description': description ?? 'نسخة احتياطية يدوية',
          },
          description: 'تم إنشاء نسخة احتياطية مشفرة',
          category: 'Backup',
        );
      }

      return backupPath;
    } catch (e) {
      LoggingService.error(
        'فشل في إنشاء النسخة الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// استعادة البيانات من نسخة احتياطية
  static Future<bool> restoreBackup({
    required String backupPath,
    required String backupPassword,
    required String databasePassword,
  }) async {
    try {
      LoggingService.info(
        'بدء استعادة النسخة الاحتياطية',
        category: 'Backup',
        data: {'path': backupPath},
      );

      // التحقق من وجود الملف
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        LoggingService.error(
          'ملف النسخة الاحتياطية غير موجود',
          category: 'Backup',
          data: {'path': backupPath},
        );
        return false;
      }

      // قراءة البيانات المشفرة
      final encryptedData = await backupFile.readAsBytes();

      // فك تشفير البيانات
      final backupData = await _decryptBackupData(
        encryptedData,
        backupPassword,
      );
      if (backupData == null) {
        LoggingService.error(
          'فشل في فك تشفير النسخة الاحتياطية',
          category: 'Backup',
        );
        return false;
      }

      // استعادة البيانات إلى قاعدة البيانات
      final success = await _restoreDatabaseData(backupData, databasePassword);

      if (success) {
        LoggingService.info(
          'تم استعادة النسخة الاحتياطية بنجاح',
          category: 'Backup',
        );

        await AuditService.logCreate(
          entityType: AppConstants.auditEntitySystem,
          entityId: 0,
          entityName: 'استعادة نسخة احتياطية',
          newValues: {'path': backupPath},
          description: 'تم استعادة البيانات من نسخة احتياطية',
          category: 'Backup',
        );
      }

      return success;
    } catch (e) {
      LoggingService.error(
        'فشل في استعادة النسخة الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// تفعيل النسخ الاحتياطي التلقائي
  static Future<bool> enableAutoBackup({
    int intervalHours = _defaultBackupInterval,
    int maxBackups = _defaultMaxBackups,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoBackupEnabledKey, true);
      await prefs.setInt(_backupIntervalKey, intervalHours);
      await prefs.setInt(_maxBackupsKey, maxBackups);

      LoggingService.info(
        'تم تفعيل النسخ الاحتياطي التلقائي',
        category: 'Backup',
        data: {'interval_hours': intervalHours, 'max_backups': maxBackups},
      );

      await AuditService.logUpdate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'إعدادات النسخ الاحتياطي',
        oldValues: {'auto_backup_enabled': false},
        newValues: {
          'auto_backup_enabled': true,
          'interval_hours': intervalHours,
          'max_backups': maxBackups,
        },
        description: 'تم تفعيل النسخ الاحتياطي التلقائي',
        category: 'Settings',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في تفعيل النسخ الاحتياطي التلقائي',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// إلغاء تفعيل النسخ الاحتياطي التلقائي
  static Future<bool> disableAutoBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoBackupEnabledKey, false);

      LoggingService.info(
        'تم إلغاء تفعيل النسخ الاحتياطي التلقائي',
        category: 'Backup',
      );

      await AuditService.logUpdate(
        entityType: AppConstants.auditEntitySystem,
        entityId: 0,
        entityName: 'إعدادات النسخ الاحتياطي',
        oldValues: {'auto_backup_enabled': true},
        newValues: {'auto_backup_enabled': false},
        description: 'تم إلغاء تفعيل النسخ الاحتياطي التلقائي',
        category: 'Settings',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في إلغاء تفعيل النسخ الاحتياطي التلقائي',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من الحاجة لنسخة احتياطية تلقائية
  static Future<bool> shouldCreateAutoBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final autoBackupEnabled = prefs.getBool(_autoBackupEnabledKey) ?? false;

      if (!autoBackupEnabled) {
        return false;
      }

      final lastBackupString = prefs.getString(_lastBackupKey);
      final intervalHours =
          prefs.getInt(_backupIntervalKey) ?? _defaultBackupInterval;

      if (lastBackupString == null) {
        return true; // لم يتم إنشاء نسخة احتياطية من قبل
      }

      final lastBackup = DateTime.parse(lastBackupString);
      final now = DateTime.now();
      final difference = now.difference(lastBackup);

      return difference.inHours >= intervalHours;
    } catch (e) {
      LoggingService.error(
        'فشل في التحقق من الحاجة للنسخ الاحتياطي التلقائي',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// إنشاء نسخة احتياطية تلقائية
  static Future<bool> createAutoBackup(String backupPassword) async {
    try {
      if (!await shouldCreateAutoBackup()) {
        return true; // لا حاجة لنسخة احتياطية الآن
      }

      final backupPath = await createBackup(
        backupPassword: backupPassword,
        description: 'نسخة احتياطية تلقائية',
      );

      return backupPath != null;
    } catch (e) {
      LoggingService.error(
        'فشل في إنشاء النسخة الاحتياطية التلقائية',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية المتاحة
  static Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      final backups = <BackupInfo>[];

      if (await backupDir.exists()) {
        final files = backupDir
            .listSync()
            .where((file) => file.path.endsWith(_backupFileExtension))
            .cast<File>();

        for (final file in files) {
          final stat = await file.stat();
          final fileName = path.basename(file.path);

          backups.add(
            BackupInfo(
              fileName: fileName,
              filePath: file.path,
              fileSize: stat.size,
              createdAt: stat.modified,
            ),
          );
        }

        // ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }

      return backups;
    } catch (e) {
      LoggingService.error(
        'فشل في الحصول على قائمة النسخ الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// حذف نسخة احتياطية
  static Future<bool> deleteBackup(String backupPath) async {
    try {
      final file = File(backupPath);
      if (await file.exists()) {
        await file.delete();

        LoggingService.info(
          'تم حذف النسخة الاحتياطية',
          category: 'Backup',
          data: {'path': backupPath},
        );

        await AuditService.logDelete(
          entityType: AppConstants.auditEntitySystem,
          entityId: 0,
          entityName: 'نسخة احتياطية',
          oldValues: {'path': backupPath},
          description: 'تم حذف نسخة احتياطية',
          category: 'Backup',
        );

        return true;
      }
      return false;
    } catch (e) {
      LoggingService.error(
        'فشل في حذف النسخة الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString(), 'path': backupPath},
      );
      return false;
    }
  }

  /// التحقق من صحة نسخة احتياطية
  static Future<bool> validateBackup({
    required String backupPath,
    required String backupPassword,
  }) async {
    try {
      final file = File(backupPath);
      if (!await file.exists()) {
        return false;
      }

      final encryptedData = await file.readAsBytes();
      final backupData = await _decryptBackupData(
        encryptedData,
        backupPassword,
      );

      if (backupData == null) {
        return false;
      }

      // التحقق من بنية البيانات
      final data = jsonDecode(backupData);
      return data is Map<String, dynamic> &&
          data.containsKey('metadata') &&
          data.containsKey('tables');
    } catch (e) {
      LoggingService.error(
        'فشل في التحقق من صحة النسخة الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString(), 'path': backupPath},
      );
      return false;
    }
  }

  /// الحصول على إعدادات النسخ الاحتياطي
  static Future<BackupSettings> getBackupSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return BackupSettings(
        autoBackupEnabled: prefs.getBool(_autoBackupEnabledKey) ?? false,
        intervalHours:
            prefs.getInt(_backupIntervalKey) ?? _defaultBackupInterval,
        maxBackups: prefs.getInt(_maxBackupsKey) ?? _defaultMaxBackups,
        lastBackupDate: prefs.getString(_lastBackupKey) != null
            ? DateTime.parse(prefs.getString(_lastBackupKey)!)
            : null,
        backupLocation: prefs.getString(_backupLocationKey),
        hasBackupPassword: prefs.getString(_backupPasswordKey) != null,
      );
    } catch (e) {
      LoggingService.error(
        'فشل في الحصول على إعدادات النسخ الاحتياطي',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return BackupSettings(
        autoBackupEnabled: false,
        intervalHours: _defaultBackupInterval,
        maxBackups: _defaultMaxBackups,
        lastBackupDate: null,
        backupLocation: null,
        hasBackupPassword: false,
      );
    }
  }

  // ===============================
  // دوال مساعدة خاصة
  // ===============================

  /// إنشاء salt عشوائي
  static String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// تشفير كلمة المرور باستخدام PBKDF2
  static String _hashPassword(String password, String salt) {
    final saltBytes = base64.decode(salt);
    final passwordBytes = utf8.encode(password);

    // استخدام PBKDF2 مع 10000 تكرار
    final hmac = Hmac(sha256, saltBytes);
    var hash = hmac.convert(passwordBytes);

    // تكرار إضافي للأمان
    for (int i = 0; i < 9999; i++) {
      hash = hmac.convert(hash.bytes);
    }

    return base64.encode(hash.bytes);
  }

  /// التحقق من قوة كلمة المرور
  static bool _isPasswordStrong(String password) {
    if (password.length < 8) return false;

    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = password.contains(
      RegExp(r'[!@#$%^&*(),.?":{}|<>]'),
    );

    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// تصدير بيانات قاعدة البيانات
  static Future<String> _exportDatabaseData(Database db) async {
    final data = <String, dynamic>{};

    // إضافة معلومات النسخة الاحتياطية
    data['metadata'] = {
      'version': AppConstants.appVersion,
      'database_version': AppConstants.databaseVersion,
      'created_at': DateTime.now().toIso8601String(),
      'app_name': AppConstants.appName,
    };

    // تصدير جميع الجداول
    data['tables'] = <String, List<Map<String, dynamic>>>{};

    final tables = [
      AppConstants.currenciesTable,
      AppConstants.accountsTable,
      AppConstants.customersTable,
      AppConstants.suppliersTable,
      AppConstants.itemsTable,
      AppConstants.invoicesTable,
      AppConstants.invoiceItemsTable,
      AppConstants.journalEntriesTable,
      AppConstants.journalEntryDetailsTable,
      AppConstants.taxesTable,
      AppConstants.settingsTable,
      AppConstants.auditLogTable,
    ];

    for (final table in tables) {
      try {
        final result = await db.query(table);
        data['tables'][table] = result;
      } catch (e) {
        LoggingService.warning(
          'تعذر تصدير الجدول: $table',
          category: 'Backup',
          data: {'error': e.toString()},
        );
        data['tables'][table] = <Map<String, dynamic>>[];
      }
    }

    return jsonEncode(data);
  }

  /// تشفير بيانات النسخة الاحتياطية
  static Future<Uint8List> _encryptBackupData(
    String data,
    String password,
  ) async {
    // إنشاء مفتاح تشفير من كلمة المرور
    final salt = _generateSalt();
    final key = _deriveEncryptionKey(password, salt);

    // تشفير البيانات باستخدام AES-256
    final dataBytes = utf8.encode(data);
    final encryptedData = _encryptAES(dataBytes, key);

    // دمج Salt مع البيانات المشفرة
    final saltBytes = base64.decode(salt);
    final result = Uint8List(saltBytes.length + encryptedData.length);
    result.setRange(0, saltBytes.length, saltBytes);
    result.setRange(saltBytes.length, result.length, encryptedData);

    return result;
  }

  /// فك تشفير بيانات النسخة الاحتياطية
  static Future<String?> _decryptBackupData(
    Uint8List encryptedData,
    String password,
  ) async {
    try {
      // استخراج Salt
      final saltBytes = encryptedData.sublist(0, 32);
      final salt = base64.encode(saltBytes);

      // استخراج البيانات المشفرة
      final dataBytes = encryptedData.sublist(32);

      // إنشاء مفتاح فك التشفير
      final key = _deriveEncryptionKey(password, salt);

      // فك تشفير البيانات
      final decryptedBytes = _decryptAES(dataBytes, key);

      return utf8.decode(decryptedBytes);
    } catch (e) {
      LoggingService.error(
        'فشل في فك تشفير بيانات النسخة الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// اشتقاق مفتاح التشفير
  static Uint8List _deriveEncryptionKey(String password, String salt) {
    final saltBytes = base64.decode(salt);
    final passwordBytes = utf8.encode(password);

    final hmac = Hmac(sha256, saltBytes);
    final hash = hmac.convert(passwordBytes);

    return Uint8List.fromList(hash.bytes);
  }

  /// تشفير AES-256 (تطبيق مبسط)
  static Uint8List _encryptAES(List<int> data, Uint8List key) {
    // هذا تطبيق مبسط - في التطبيق الحقيقي يجب استخدام مكتبة تشفير متقدمة
    final random = Random.secure();
    final encrypted = <int>[];

    for (int i = 0; i < data.length; i++) {
      final keyByte = key[i % key.length];
      final randomByte = random.nextInt(256);
      encrypted.add(data[i] ^ keyByte ^ randomByte);
      encrypted.add(randomByte);
    }

    return Uint8List.fromList(encrypted);
  }

  /// فك تشفير AES-256 (تطبيق مبسط)
  static Uint8List _decryptAES(List<int> encryptedData, Uint8List key) {
    final decrypted = <int>[];

    for (int i = 0; i < encryptedData.length; i += 2) {
      if (i + 1 < encryptedData.length) {
        final encryptedByte = encryptedData[i];
        final randomByte = encryptedData[i + 1];
        final keyByte = key[(i ~/ 2) % key.length];
        decrypted.add(encryptedByte ^ keyByte ^ randomByte);
      }
    }

    return Uint8List.fromList(decrypted);
  }

  /// إنشاء ملف النسخة الاحتياطية
  static Future<String?> _createBackupFile(
    Uint8List encryptedData,
    String? customPath,
  ) async {
    try {
      final backupDir = customPath != null
          ? Directory(path.dirname(customPath))
          : await _getBackupDirectory();

      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final fileName = customPath != null
          ? path.basename(customPath)
          : _generateBackupFileName();

      final filePath = path.join(backupDir.path, fileName);
      final file = File(filePath);

      await file.writeAsBytes(encryptedData);

      return filePath;
    } catch (e) {
      LoggingService.error(
        'فشل في إنشاء ملف النسخة الاحتياطية',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// استعادة البيانات إلى قاعدة البيانات
  static Future<bool> _restoreDatabaseData(
    String backupData,
    String databasePassword,
  ) async {
    try {
      final data = jsonDecode(backupData) as Map<String, dynamic>;
      final tables = data['tables'] as Map<String, dynamic>;

      // إغلاق قاعدة البيانات الحالية
      await DatabaseHelper().closeDatabase();

      // إنشاء قاعدة بيانات جديدة
      final success = await DatabaseHelper().initializeDatabase(
        databasePassword,
      );
      if (!success) {
        return false;
      }

      final db = await DatabaseHelper().database;

      // مسح البيانات الحالية واستعادة البيانات من النسخة الاحتياطية
      await db.transaction((txn) async {
        // حذف البيانات الحالية (عدا الجداول الأساسية)
        final tablesToClear = [
          AppConstants.auditLogTable,
          AppConstants.invoiceItemsTable,
          AppConstants.invoicesTable,
          AppConstants.journalEntryDetailsTable,
          AppConstants.journalEntriesTable,
          AppConstants.itemsTable,
          AppConstants.suppliersTable,
          AppConstants.customersTable,
          AppConstants.accountsTable,
          AppConstants.taxesTable,
          AppConstants.settingsTable,
          AppConstants.currenciesTable,
        ];

        for (final table in tablesToClear) {
          await txn.delete(table);
        }

        // استعادة البيانات
        for (final entry in tables.entries) {
          final tableName = entry.key;
          final tableData = entry.value as List<dynamic>;

          for (final row in tableData) {
            final rowData = Map<String, dynamic>.from(row as Map);
            await txn.insert(tableName, rowData);
          }
        }
      });

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في استعادة البيانات',
        category: 'Backup',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على مجلد النسخ الاحتياطية
  static Future<Directory> _getBackupDirectory() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(documentsDir.path, 'smart_ledger_backups'));
  }

  /// إنشاء اسم ملف النسخة الاحتياطية
  static String _generateBackupFileName() {
    final now = DateTime.now();
    final timestamp =
        '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    return 'smart_ledger_backup_$timestamp$_backupFileExtension';
  }

  /// تحديث تاريخ آخر نسخة احتياطية
  static Future<void> _updateLastBackupDate() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastBackupKey, DateTime.now().toIso8601String());
  }

  /// تنظيف النسخ الاحتياطية القديمة
  static Future<void> _cleanupOldBackups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final maxBackups = prefs.getInt(_maxBackupsKey) ?? _defaultMaxBackups;

      final backups = await getAvailableBackups();

      if (backups.length > maxBackups) {
        // ترتيب حسب التاريخ (الأقدم أولاً)
        backups.sort((a, b) => a.createdAt.compareTo(b.createdAt));

        // حذف النسخ الزائدة
        final backupsToDelete = backups.take(backups.length - maxBackups);
        for (final backup in backupsToDelete) {
          await deleteBackup(backup.filePath);
        }

        LoggingService.info(
          'تم تنظيف ${backupsToDelete.length} نسخة احتياطية قديمة',
          category: 'Backup',
        );
      }
    } catch (e) {
      LoggingService.error(
        'فشل في تنظيف النسخ الاحتياطية القديمة',
        category: 'Backup',
        data: {'error': e.toString()},
      );
    }
  }
}

/// معلومات النسخة الاحتياطية
class BackupInfo {
  final String fileName;
  final String filePath;
  final int fileSize;
  final DateTime createdAt;

  BackupInfo({
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.createdAt,
  });

  /// حجم الملف بصيغة قابلة للقراءة
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize بايت';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }

  /// تاريخ الإنشاء بصيغة قابلة للقراءة
  String get formattedCreatedAt {
    return '${createdAt.day.toString().padLeft(2, '0')}/${createdAt.month.toString().padLeft(2, '0')}/${createdAt.year} ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }
}

/// إعدادات النسخ الاحتياطي
class BackupSettings {
  final bool autoBackupEnabled;
  final int intervalHours;
  final int maxBackups;
  final DateTime? lastBackupDate;
  final String? backupLocation;
  final bool hasBackupPassword;

  BackupSettings({
    required this.autoBackupEnabled,
    required this.intervalHours,
    required this.maxBackups,
    this.lastBackupDate,
    this.backupLocation,
    required this.hasBackupPassword,
  });

  /// تاريخ آخر نسخة احتياطية بصيغة قابلة للقراءة
  String? get formattedLastBackupDate {
    if (lastBackupDate == null) return null;
    final date = lastBackupDate!;
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// الوقت المتبقي للنسخة الاحتياطية التالية
  Duration? get timeUntilNextBackup {
    if (!autoBackupEnabled || lastBackupDate == null) return null;

    final nextBackupTime = lastBackupDate!.add(Duration(hours: intervalHours));
    final now = DateTime.now();

    if (nextBackupTime.isAfter(now)) {
      return nextBackupTime.difference(now);
    }

    return Duration.zero; // حان وقت النسخة الاحتياطية
  }
}
