// اختبارات واجهة المستخدم لتطبيق Smart Ledger
// تحتوي على اختبارات أساسية للتأكد من عمل التطبيق بشكل صحيح

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/main.dart';

void main() {
  group('Smart Ledger App Tests', () {
    testWidgets('App should start and show home screen', (
      WidgetTester tester,
    ) async {
      // بناء التطبيق وتشغيل إطار
      await tester.pumpWidget(const SmartLedgerApp());

      // التحقق من أن التطبيق يبدأ بشكل صحيح
      expect(find.byType(MaterialApp), findsOneWidget);

      // انتظار تحميل الشاشة الرئيسية
      await tester.pumpAndSettle();

      // التحقق من وجود عناصر الشاشة الرئيسية
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('App should have correct title', (WidgetTester tester) async {
      await tester.pumpWidget(const SmartLedgerApp());

      // التحقق من عنوان التطبيق
      final MaterialApp app = tester.widget(find.byType(MaterialApp));
      expect(app.title, equals('Smart Ledger'));
    });

    testWidgets('App should use RTL direction', (WidgetTester tester) async {
      await tester.pumpWidget(const SmartLedgerApp());

      // التحقق من اتجاه النص من اليمين إلى اليسار
      final MaterialApp app = tester.widget(find.byType(MaterialApp));
      expect(app.locale, equals(const Locale('ar', 'SY')));
    });
  });
}
