import 'dart:async';
import '../services/cache_service.dart';
import '../services/connection_pool_service.dart';
import '../services/memory_management_service.dart';
import '../services/logging_service.dart';

/// خدمة تحسين الأداء الشاملة
/// تدير وتنسق جميع تحسينات الأداء في التطبيق
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final Map<String, PerformanceMetric> _metrics = {};
  final List<PerformanceEvent> _events = [];
  Timer? _reportingTimer;
  
  bool _isInitialized = false;
  bool _isOptimizationEnabled = true;
  
  // إعدادات التحسين
  static const Duration reportingInterval = Duration(minutes: 5);
  static const int maxEvents = 1000;
  static const int maxMetrics = 100;

  /// تهيئة خدمة تحسين الأداء
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info(
        'بدء تهيئة خدمة تحسين الأداء',
        category: 'Performance',
      );

      // تهيئة الخدمات الفرعية
      await _initializeSubServices();

      // بدء مراقبة الأداء
      _startPerformanceMonitoring();

      // بدء التقارير الدورية
      _startPerformanceReporting();

      _isInitialized = true;

      LoggingService.info(
        'تم تهيئة خدمة تحسين الأداء بنجاح',
        category: 'Performance',
      );
    } catch (e) {
      LoggingService.error(
        'فشل في تهيئة خدمة تحسين الأداء',
        category: 'Performance',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تهيئة الخدمات الفرعية
  Future<void> _initializeSubServices() async {
    // تهيئة مجموعة اتصالات قاعدة البيانات
    await ConnectionPoolService().initialize();

    // بدء مراقبة الذاكرة
    MemoryManagementService().startMonitoring();

    LoggingService.debug(
      'تم تهيئة الخدمات الفرعية لتحسين الأداء',
      category: 'Performance',
    );
  }

  /// بدء مراقبة الأداء
  void _startPerformanceMonitoring() {
    // تسجيل بداية التطبيق
    recordEvent(PerformanceEvent(
      name: 'app_start',
      timestamp: DateTime.now(),
      duration: Duration.zero,
      category: 'lifecycle',
    ));

    // مراقبة استهلاك الذاكرة
    _monitorMemoryUsage();

    // مراقبة أداء قاعدة البيانات
    _monitorDatabasePerformance();
  }

  /// بدء التقارير الدورية
  void _startPerformanceReporting() {
    _reportingTimer = Timer.periodic(reportingInterval, (timer) {
      _generatePerformanceReport();
    });
  }

  /// مراقبة استهلاك الذاكرة
  void _monitorMemoryUsage() {
    final memoryService = MemoryManagementService();
    
    // تسجيل مقياس استهلاك الذاكرة
    Timer.periodic(Duration(minutes: 1), (timer) {
      final stats = memoryService.getStatistics();
      
      updateMetric('memory_usage_mb', stats.currentUsageMB);
      updateMetric('memory_pressure_level', stats.pressureLevel.index.toDouble());
      updateMetric('cache_size', stats.cacheSize.toDouble());
    });
  }

  /// مراقبة أداء قاعدة البيانات
  void _monitorDatabasePerformance() {
    final poolService = ConnectionPoolService();
    
    // تسجيل مقاييس مجموعة الاتصالات
    Timer.periodic(Duration(minutes: 1), (timer) {
      final stats = poolService.getStatistics();
      
      updateMetric('db_total_connections', stats.totalConnections.toDouble());
      updateMetric('db_active_connections', stats.activeConnections.toDouble());
      updateMetric('db_utilization_ratio', stats.utilizationRatio);
    });
  }

  /// تسجيل حدث أداء
  void recordEvent(PerformanceEvent event) {
    try {
      _events.add(event);

      // الاحتفاظ بعدد محدود من الأحداث
      if (_events.length > maxEvents) {
        _events.removeAt(0);
      }

      LoggingService.debug(
        'تسجيل حدث أداء: ${event.name}',
        category: 'Performance',
        data: {
          'duration': event.duration.inMilliseconds,
          'category': event.category,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل حدث الأداء',
        category: 'Performance',
        data: {'event': event.name, 'error': e.toString()},
      );
    }
  }

  /// تحديث مقياس أداء
  void updateMetric(String name, double value) {
    try {
      final now = DateTime.now();
      
      if (_metrics.containsKey(name)) {
        _metrics[name]!.update(value, now);
      } else {
        _metrics[name] = PerformanceMetric(
          name: name,
          value: value,
          lastUpdated: now,
        );
      }

      // الاحتفاظ بعدد محدود من المقاييس
      if (_metrics.length > maxMetrics) {
        final oldestKey = _metrics.entries
            .reduce((a, b) => a.value.lastUpdated.isBefore(b.value.lastUpdated) ? a : b)
            .key;
        _metrics.remove(oldestKey);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث مقياس الأداء',
        category: 'Performance',
        data: {'metric': name, 'value': value, 'error': e.toString()},
      );
    }
  }

  /// قياس وقت تنفيذ عملية
  Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    String category = 'general',
    Map<String, dynamic>? metadata,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();

      // تسجيل حدث الأداء
      recordEvent(PerformanceEvent(
        name: operationName,
        timestamp: DateTime.now(),
        duration: stopwatch.elapsed,
        category: category,
        metadata: metadata,
      ));

      return result;
    } catch (e) {
      stopwatch.stop();
      
      // تسجيل حدث فشل
      recordEvent(PerformanceEvent(
        name: '${operationName}_error',
        timestamp: DateTime.now(),
        duration: stopwatch.elapsed,
        category: category,
        metadata: {
          ...?metadata,
          'error': e.toString(),
        },
      ));
      
      rethrow;
    }
  }

  /// قياس وقت تنفيذ عملية متزامنة
  T measureSyncOperation<T>(
    String operationName,
    T Function() operation, {
    String category = 'general',
    Map<String, dynamic>? metadata,
  }) {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = operation();
      stopwatch.stop();

      // تسجيل حدث الأداء
      recordEvent(PerformanceEvent(
        name: operationName,
        timestamp: DateTime.now(),
        duration: stopwatch.elapsed,
        category: category,
        metadata: metadata,
      ));

      return result;
    } catch (e) {
      stopwatch.stop();
      
      // تسجيل حدث فشل
      recordEvent(PerformanceEvent(
        name: '${operationName}_error',
        timestamp: DateTime.now(),
        duration: stopwatch.elapsed,
        category: category,
        metadata: {
          ...?metadata,
          'error': e.toString(),
        },
      ));
      
      rethrow;
    }
  }

  /// تحسين الأداء التلقائي
  Future<void> optimizePerformance() async {
    if (!_isOptimizationEnabled) return;

    try {
      LoggingService.info(
        'بدء تحسين الأداء التلقائي',
        category: 'Performance',
      );

      // تحسين التخزين المؤقت
      await _optimizeCache();

      // تحسين الذاكرة
      await _optimizeMemory();

      // تحسين قاعدة البيانات
      await _optimizeDatabase();

      LoggingService.info(
        'تم تحسين الأداء التلقائي بنجاح',
        category: 'Performance',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحسين الأداء التلقائي',
        category: 'Performance',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحسين التخزين المؤقت
  Future<void> _optimizeCache() async {
    final cacheService = CacheService();
    final stats = cacheService.getStatistics();

    // تنظيف القيم المنتهية الصلاحية
    cacheService.removeExpired();

    LoggingService.debug(
      'تحسين التخزين المؤقت',
      category: 'Performance',
      data: {
        'totalEntries': stats.totalEntries,
        'expiredEntries': stats.expiredEntries,
        'memoryUsageKB': stats.memoryUsageKB,
      },
    );
  }

  /// تحسين الذاكرة
  Future<void> _optimizeMemory() async {
    final memoryService = MemoryManagementService();
    final stats = memoryService.getStatistics();

    // تنفيذ تحسين الذاكرة حسب مستوى الضغط
    if (stats.pressureLevel != MemoryPressureLevel.normal) {
      // سيتم التحسين تلقائياً بواسطة MemoryManagementService
    }

    LoggingService.debug(
      'تحسين الذاكرة',
      category: 'Performance',
      data: {
        'currentUsageMB': stats.currentUsageMB,
        'pressureLevel': stats.pressureLevel.name,
        'cacheSize': stats.cacheSize,
      },
    );
  }

  /// تحسين قاعدة البيانات
  Future<void> _optimizeDatabase() async {
    final poolService = ConnectionPoolService();
    final stats = poolService.getStatistics();

    LoggingService.debug(
      'تحسين قاعدة البيانات',
      category: 'Performance',
      data: {
        'totalConnections': stats.totalConnections,
        'utilizationRatio': stats.utilizationRatio,
        'isFull': stats.isFull,
      },
    );
  }

  /// إنشاء تقرير أداء
  void _generatePerformanceReport() {
    try {
      final report = PerformanceReport(
        timestamp: DateTime.now(),
        metrics: Map.from(_metrics),
        recentEvents: _events.where((e) => 
          DateTime.now().difference(e.timestamp) < Duration(hours: 1)
        ).toList(),
        memoryStats: MemoryManagementService().getStatistics(),
        cacheStats: CacheService().getStatistics(),
        dbStats: ConnectionPoolService().getStatistics(),
      );

      LoggingService.info(
        'تقرير الأداء',
        category: 'Performance',
        data: report.toMap(),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الأداء',
        category: 'Performance',
        data: {'error': e.toString()},
      );
    }
  }

  /// تفعيل/إلغاء تفعيل التحسين التلقائي
  void setOptimizationEnabled(bool enabled) {
    _isOptimizationEnabled = enabled;
    LoggingService.info(
      'تغيير حالة التحسين التلقائي',
      category: 'Performance',
      data: {'enabled': enabled},
    );
  }

  /// الحصول على إحصائيات الأداء
  PerformanceStatistics getStatistics() {
    return PerformanceStatistics(
      totalEvents: _events.length,
      totalMetrics: _metrics.length,
      isOptimizationEnabled: _isOptimizationEnabled,
      isInitialized: _isInitialized,
      averageEventDuration: _calculateAverageEventDuration(),
      memoryStats: MemoryManagementService().getStatistics(),
      cacheStats: CacheService().getStatistics(),
      dbStats: ConnectionPoolService().getStatistics(),
    );
  }

  /// حساب متوسط مدة الأحداث
  Duration _calculateAverageEventDuration() {
    if (_events.isEmpty) return Duration.zero;
    
    final totalMilliseconds = _events.fold<int>(
      0, 
      (sum, event) => sum + event.duration.inMilliseconds,
    );
    
    return Duration(milliseconds: totalMilliseconds ~/ _events.length);
  }

  /// إيقاف خدمة تحسين الأداء
  Future<void> shutdown() async {
    try {
      LoggingService.info(
        'بدء إيقاف خدمة تحسين الأداء',
        category: 'Performance',
      );

      // إيقاف التقارير الدورية
      _reportingTimer?.cancel();

      // إيقاف مراقبة الذاكرة
      MemoryManagementService().stopMonitoring();

      // إغلاق مجموعة اتصالات قاعدة البيانات
      await ConnectionPoolService().closeAll();

      _isInitialized = false;

      LoggingService.info(
        'تم إيقاف خدمة تحسين الأداء',
        category: 'Performance',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إيقاف خدمة تحسين الأداء',
        category: 'Performance',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على الأحداث الأخيرة
  List<PerformanceEvent> get recentEvents => List.unmodifiable(_events);

  /// الحصول على المقاييس الحالية
  Map<String, PerformanceMetric> get currentMetrics => Map.unmodifiable(_metrics);

  /// هل الخدمة مهيأة؟
  bool get isInitialized => _isInitialized;

  /// هل التحسين التلقائي مفعل؟
  bool get isOptimizationEnabled => _isOptimizationEnabled;
}

/// حدث أداء
class PerformanceEvent {
  final String name;
  final DateTime timestamp;
  final Duration duration;
  final String category;
  final Map<String, dynamic>? metadata;

  const PerformanceEvent({
    required this.name,
    required this.timestamp,
    required this.duration,
    required this.category,
    this.metadata,
  });

  @override
  String toString() {
    return 'PerformanceEvent(name: $name, duration: ${duration.inMilliseconds}ms, category: $category)';
  }
}

/// مقياس أداء
class PerformanceMetric {
  final String name;
  double value;
  DateTime lastUpdated;
  final List<double> _history = [];

  PerformanceMetric({
    required this.name,
    required this.value,
    required this.lastUpdated,
  }) {
    _history.add(value);
  }

  /// تحديث المقياس
  void update(double newValue, DateTime timestamp) {
    value = newValue;
    lastUpdated = timestamp;
    _history.add(newValue);

    // الاحتفاظ بآخر 100 قيمة
    if (_history.length > 100) {
      _history.removeAt(0);
    }
  }

  /// متوسط القيم
  double get average => _history.isNotEmpty 
      ? _history.reduce((a, b) => a + b) / _history.length 
      : 0;

  /// أقصى قيمة
  double get maximum => _history.isNotEmpty 
      ? _history.reduce((a, b) => a > b ? a : b) 
      : 0;

  /// أدنى قيمة
  double get minimum => _history.isNotEmpty 
      ? _history.reduce((a, b) => a < b ? a : b) 
      : 0;

  @override
  String toString() {
    return 'PerformanceMetric(name: $name, value: $value, avg: ${average.toStringAsFixed(2)})';
  }
}

/// تقرير أداء
class PerformanceReport {
  final DateTime timestamp;
  final Map<String, PerformanceMetric> metrics;
  final List<PerformanceEvent> recentEvents;
  final MemoryStatistics memoryStats;
  final CacheStatistics cacheStats;
  final ConnectionPoolStatistics dbStats;

  const PerformanceReport({
    required this.timestamp,
    required this.metrics,
    required this.recentEvents,
    required this.memoryStats,
    required this.cacheStats,
    required this.dbStats,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'metricsCount': metrics.length,
      'eventsCount': recentEvents.length,
      'memoryUsageMB': memoryStats.currentUsageMB,
      'cacheSize': cacheStats.totalEntries,
      'dbConnections': dbStats.totalConnections,
    };
  }
}

/// إحصائيات الأداء
class PerformanceStatistics {
  final int totalEvents;
  final int totalMetrics;
  final bool isOptimizationEnabled;
  final bool isInitialized;
  final Duration averageEventDuration;
  final MemoryStatistics memoryStats;
  final CacheStatistics cacheStats;
  final ConnectionPoolStatistics dbStats;

  const PerformanceStatistics({
    required this.totalEvents,
    required this.totalMetrics,
    required this.isOptimizationEnabled,
    required this.isInitialized,
    required this.averageEventDuration,
    required this.memoryStats,
    required this.cacheStats,
    required this.dbStats,
  });

  @override
  String toString() {
    return 'PerformanceStatistics(events: $totalEvents, metrics: $totalMetrics, avgDuration: ${averageEventDuration.inMilliseconds}ms)';
  }
}
