import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/screens/add_invoice_screen.dart';
import 'package:smart_ledger/constants/app_constants.dart';

void main() {
  group('AddInvoiceScreen Tests', () {
    testWidgets('should display correct title for sales invoice', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      expect(find.text('فاتورة مبيعات جديدة'), findsOneWidget);
    });

    testWidgets('should display correct title for purchase invoice', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypePurchase),
        ),
      );

      expect(find.text('فاتورة مشتريات جديدة'), findsOneWidget);
    });

    testWidgets('should display correct title for sales return', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSaleReturn),
        ),
      );

      expect(find.text('مردود مبيعات جديد'), findsOneWidget);
    });

    testWidgets('should display correct title for purchase return', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypePurchaseReturn),
        ),
      );

      expect(find.text('مردود مشتريات جديد'), findsOneWidget);
    });

    testWidgets('should show add item button', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('إضافة صنف'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsAtLeast(1));
    });

    testWidgets('should show save button with tooltip', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.save), findsOneWidget);
      
      // التحقق من tooltip
      final saveButton = find.byIcon(Icons.save);
      await tester.longPress(saveButton);
      await tester.pumpAndSettle();
      expect(find.text('حفظ (Ctrl+S)'), findsOneWidget);
    });

    testWidgets('should show empty items message when no items added', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('لم يتم إضافة أصناف بعد'), findsOneWidget);
      expect(find.byIcon(Icons.inventory_2_outlined), findsOneWidget);
    });

    testWidgets('should handle keyboard shortcuts', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      // اختبار اختصار Ctrl+S للحفظ
      await tester.sendKeyDownEvent(LogicalKeyboardKey.control);
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyS);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyS);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.control);
      await tester.pumpAndSettle();

      // يجب أن يحاول حفظ الفاتورة (قد يفشل بسبب عدم وجود بيانات)
      // هذا الاختبار يتحقق من أن الاختصار يعمل
    });

    testWidgets('should handle Ctrl+N shortcut for adding item', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      // اختبار اختصار Ctrl+N لإضافة صنف
      await tester.sendKeyDownEvent(LogicalKeyboardKey.control);
      await tester.sendKeyDownEvent(LogicalKeyboardKey.keyN);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.keyN);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.control);
      await tester.pumpAndSettle();

      // يجب أن يفتح حوار اختيار الأصناف
      // هذا الاختبار يتطلب mock للخدمات
    });

    testWidgets('should handle Escape key for cancel', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddInvoiceScreen(
                      invoiceType: AppConstants.invoiceTypeSale,
                    ),
                  ),
                ),
                child: const Text('Open Invoice'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Invoice'));
      await tester.pumpAndSettle();

      expect(find.byType(AddInvoiceScreen), findsOneWidget);

      // اختبار اختصار Escape للإلغاء
      await tester.sendKeyDownEvent(LogicalKeyboardKey.escape);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // يجب أن يعود للشاشة السابقة
      expect(find.byType(AddInvoiceScreen), findsNothing);
    });

    testWidgets('should show totals section', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('الإجماليات'), findsOneWidget);
    });

    testWidgets('should show notes section', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('ملاحظات'), findsOneWidget);
    });

    testWidgets('should show customer section for sales invoice', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('العميل'), findsOneWidget);
    });

    testWidgets('should show supplier section for purchase invoice', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypePurchase),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('المورد'), findsOneWidget);
    });

    testWidgets('should show invoice header with date and number fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('رقم الفاتورة'), findsOneWidget);
      expect(find.text('تاريخ الفاتورة'), findsOneWidget);
    });
  });

  group('Invoice Item Management Tests', () {
    testWidgets('should show item details when item is added', (WidgetTester tester) async {
      // هذا الاختبار يتطلب محاكاة إضافة صنف
      // يمكن تطويره مع إضافة mock للخدمات
    });

    testWidgets('should allow editing item details', (WidgetTester tester) async {
      // هذا الاختبار يتطلب محاكاة وجود أصناف في الفاتورة
      // يمكن تطويره مع إضافة mock للخدمات
    });

    testWidgets('should allow removing items', (WidgetTester tester) async {
      // هذا الاختبار يتطلب محاكاة وجود أصناف في الفاتورة
      // يمكن تطويره مع إضافة mock للخدمات
    });

    testWidgets('should update totals when items are modified', (WidgetTester tester) async {
      // هذا الاختبار يتطلب محاكاة تعديل الأصناف
      // يمكن تطويره مع إضافة mock للخدمات
    });
  });

  group('Validation Tests', () {
    testWidgets('should validate required fields before saving', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AddInvoiceScreen(invoiceType: AppConstants.invoiceTypeSale),
        ),
      );

      await tester.pumpAndSettle();

      // محاولة الحفظ بدون بيانات
      await tester.tap(find.byIcon(Icons.save));
      await tester.pumpAndSettle();

      // يجب أن تظهر رسائل خطأ للحقول المطلوبة
      // هذا الاختبار يتطلب تحديد رسائل الخطأ المحددة
    });
  });
}
