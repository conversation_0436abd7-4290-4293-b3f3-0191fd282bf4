import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/audit_log.dart';
import '../services/audit_service.dart';
import '../constants/app_theme.dart';
import '../constants/app_constants.dart';

/// شاشة سجل المراجعة
/// تعرض جميع عمليات المراجعة مع إمكانيات البحث والفلترة
class AuditLogScreen extends StatefulWidget {
  const AuditLogScreen({super.key});

  @override
  State<AuditLogScreen> createState() => _AuditLogScreenState();
}

class _AuditLogScreenState extends State<AuditLogScreen> {
  final _searchController = TextEditingController();
  List<AuditLog> _auditLogs = [];
  List<AuditLog> _filteredLogs = [];
  bool _isLoading = true;
  String? _selectedAction;
  String? _selectedEntityType;
  String? _selectedSeverity;
  DateTime? _startDate;
  DateTime? _endDate;

  final List<String> _actions = [
    AppConstants.auditActionCreate,
    AppConstants.auditActionUpdate,
    AppConstants.auditActionDelete,
    AppConstants.auditActionLogin,
    AppConstants.auditActionLogout,
    AppConstants.auditActionPasswordChange,
  ];

  final List<String> _entityTypes = [
    AppConstants.auditEntityAccount,
    AppConstants.auditEntityCustomer,
    AppConstants.auditEntitySupplier,
    AppConstants.auditEntityItem,
    AppConstants.auditEntityInvoice,
    AppConstants.auditEntityJournalEntry,
    AppConstants.auditEntityUser,
    AppConstants.auditEntitySystem,
  ];

  final List<String> _severities = ['INFO', 'SUCCESS', 'WARNING', 'ERROR'];

  @override
  void initState() {
    super.initState();
    _loadAuditLogs();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAuditLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final logs = await AuditService.getAuditLogs(
        action: _selectedAction,
        entityType: _selectedEntityType,
        severity: _selectedSeverity,
        startDate: _startDate,
        endDate: _endDate,
        limit: 1000, // تحديد عدد السجلات
      );

      setState(() {
        _auditLogs = logs;
        _filteredLogs = logs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل سجلات المراجعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterLogs() {
    final searchTerm = _searchController.text.toLowerCase();
    setState(() {
      _filteredLogs = _auditLogs.where((log) {
        final matchesSearch = searchTerm.isEmpty ||
            log.entityName?.toLowerCase().contains(searchTerm) == true ||
            log.description?.toLowerCase().contains(searchTerm) == true ||
            log.userName?.toLowerCase().contains(searchTerm) == true;
        return matchesSearch;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('سجل المراجعة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAuditLogs,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),
          
          // إحصائيات سريعة
          _buildQuickStats(),
          
          // قائمة السجلات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredLogs.isEmpty
                    ? _buildEmptyState()
                    : _buildLogsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppTheme.cardColor,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في سجلات المراجعة...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _filterLogs();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onChanged: (_) => _filterLogs(),
      ),
    );
  }

  Widget _buildQuickStats() {
    final totalLogs = _auditLogs.length;
    final successLogs = _auditLogs.where((log) => log.severity == 'SUCCESS').length;
    final warningLogs = _auditLogs.where((log) => log.severity == 'WARNING').length;
    final errorLogs = _auditLogs.where((log) => log.severity == 'ERROR').length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard('المجموع', totalLogs.toString(), Colors.blue),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard('نجح', successLogs.toString(), Colors.green),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard('تحذير', warningLogs.toString(), Colors.orange),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard('خطأ', errorLogs.toString(), Colors.red),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList() {
    return ListView.builder(
      itemCount: _filteredLogs.length,
      itemBuilder: (context, index) {
        final log = _filteredLogs[index];
        return _buildLogCard(log);
      },
    );
  }

  Widget _buildLogCard(AuditLog log) {
    final dateFormat = DateFormat('yyyy/MM/dd HH:mm:ss');
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getSeverityColor(log.severity),
          child: Text(
            log.actionIcon,
            style: const TextStyle(fontSize: 16),
          ),
        ),
        title: Text(
          log.readableDescription,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (log.description != null) ...[
              Text(log.description!),
              const SizedBox(height: 4),
            ],
            Row(
              children: [
                Icon(Icons.person, size: 14, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  log.userName ?? 'غير معروف',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  dateFormat.format(log.timestamp),
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getSeverityColor(log.severity),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getSeverityText(log.severity),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () => _showLogDetails(log),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد سجلات مراجعة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر سجلات المراجعة هنا عند تنفيذ العمليات',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity.toUpperCase()) {
      case 'ERROR':
        return Colors.red;
      case 'WARNING':
        return Colors.orange;
      case 'SUCCESS':
        return Colors.green;
      case 'INFO':
      default:
        return Colors.blue;
    }
  }

  String _getSeverityText(String severity) {
    switch (severity.toUpperCase()) {
      case 'ERROR':
        return 'خطأ';
      case 'WARNING':
        return 'تحذير';
      case 'SUCCESS':
        return 'نجح';
      case 'INFO':
      default:
        return 'معلومات';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة السجلات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // فلتر العملية
              DropdownButtonFormField<String>(
                value: _selectedAction,
                decoration: const InputDecoration(labelText: 'العملية'),
                items: [
                  const DropdownMenuItem(value: null, child: Text('جميع العمليات')),
                  ..._actions.map((action) => DropdownMenuItem(
                        value: action,
                        child: Text(_getActionText(action)),
                      )),
                ],
                onChanged: (value) => setState(() => _selectedAction = value),
              ),
              const SizedBox(height: 16),
              
              // فلتر نوع الكيان
              DropdownButtonFormField<String>(
                value: _selectedEntityType,
                decoration: const InputDecoration(labelText: 'نوع الكيان'),
                items: [
                  const DropdownMenuItem(value: null, child: Text('جميع الأنواع')),
                  ..._entityTypes.map((type) => DropdownMenuItem(
                        value: type,
                        child: Text(_getEntityTypeText(type)),
                      )),
                ],
                onChanged: (value) => setState(() => _selectedEntityType = value),
              ),
              const SizedBox(height: 16),
              
              // فلتر الشدة
              DropdownButtonFormField<String>(
                value: _selectedSeverity,
                decoration: const InputDecoration(labelText: 'الشدة'),
                items: [
                  const DropdownMenuItem(value: null, child: Text('جميع المستويات')),
                  ..._severities.map((severity) => DropdownMenuItem(
                        value: severity,
                        child: Text(_getSeverityText(severity)),
                      )),
                ],
                onChanged: (value) => setState(() => _selectedSeverity = value),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedAction = null;
                _selectedEntityType = null;
                _selectedSeverity = null;
                _startDate = null;
                _endDate = null;
              });
              Navigator.of(context).pop();
              _loadAuditLogs();
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadAuditLogs();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _showLogDetails(AuditLog log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(log.readableDescription),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('العملية', log.action),
              _buildDetailRow('نوع الكيان', log.entityType),
              if (log.entityName != null) _buildDetailRow('اسم الكيان', log.entityName!),
              if (log.userName != null) _buildDetailRow('المستخدم', log.userName!),
              _buildDetailRow('الوقت', DateFormat('yyyy/MM/dd HH:mm:ss').format(log.timestamp)),
              _buildDetailRow('الشدة', _getSeverityText(log.severity)),
              if (log.description != null) _buildDetailRow('الوصف', log.description!),
              if (log.category != null) _buildDetailRow('الفئة', log.category!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getActionText(String action) {
    switch (action) {
      case 'CREATE':
        return 'إنشاء';
      case 'UPDATE':
        return 'تحديث';
      case 'DELETE':
        return 'حذف';
      case 'LOGIN':
        return 'تسجيل دخول';
      case 'LOGOUT':
        return 'تسجيل خروج';
      case 'PASSWORD_CHANGE':
        return 'تغيير كلمة المرور';
      default:
        return action;
    }
  }

  String _getEntityTypeText(String entityType) {
    switch (entityType) {
      case 'ACCOUNT':
        return 'حساب';
      case 'CUSTOMER':
        return 'عميل';
      case 'SUPPLIER':
        return 'مورد';
      case 'ITEM':
        return 'صنف';
      case 'INVOICE':
        return 'فاتورة';
      case 'JOURNAL_ENTRY':
        return 'قيد محاسبي';
      case 'USER':
        return 'مستخدم';
      case 'SYSTEM':
        return 'نظام';
      default:
        return entityType;
    }
  }
}
