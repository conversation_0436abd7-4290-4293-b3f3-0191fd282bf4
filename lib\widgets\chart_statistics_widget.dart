import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../models/interactive_report_models.dart';
import '../models/report_models.dart';

/// مكون إحصائيات الرسم البياني
class ChartStatisticsWidget extends StatelessWidget {
  final String reportType;
  final dynamic data;

  const ChartStatisticsWidget({
    super.key,
    required this.reportType,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.analytics, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'إحصائيات البيانات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Statistics based on report type
          _buildStatistics(),
        ],
      ),
    );
  }

  Widget _buildStatistics() {
    switch (reportType) {
      case 'trial_balance':
        return _buildTrialBalanceStatistics();
      case 'profit_loss':
        return _buildProfitLossStatistics();
      case 'balance_sheet':
        return _buildBalanceSheetStatistics();
      case 'customer_aging':
        return _buildCustomerAgingStatistics();
      case 'sales_analysis':
        return _buildSalesAnalysisStatistics();
      case 'purchase_analysis':
        return _buildPurchaseAnalysisStatistics();
      case 'inventory_report':
        return _buildInventoryStatistics();
      default:
        return _buildDefaultStatistics();
    }
  }

  Widget _buildTrialBalanceStatistics() {
    if (data is! List<TrialBalanceItem>) {
      return const Text('بيانات غير صحيحة');
    }

    final items = data as List<TrialBalanceItem>;
    final totalDebit = items.fold<double>(
      0,
      (sum, item) => sum + item.totalDebit,
    );
    final totalCredit = items.fold<double>(
      0,
      (sum, item) => sum + item.totalCredit,
    );
    final positiveBalances = items
        .where(
          (item) =>
              (item.openingBalance + item.totalDebit - item.totalCredit) > 0,
        )
        .length;
    final negativeBalances = items
        .where(
          (item) =>
              (item.openingBalance + item.totalDebit - item.totalCredit) < 0,
        )
        .length;

    return Column(
      children: [
        _buildStatisticRow('إجمالي المدين', totalDebit, AppColors.success),
        _buildStatisticRow('إجمالي الدائن', totalCredit, AppColors.error),
        _buildStatisticRow(
          'الفرق',
          (totalDebit - totalCredit).abs(),
          AppColors.info,
        ),
        const Divider(),
        _buildCountRow('أرصدة موجبة', positiveBalances, AppColors.success),
        _buildCountRow('أرصدة سالبة', negativeBalances, AppColors.error),
        _buildCountRow('إجمالي الحسابات', items.length, AppColors.primary),
      ],
    );
  }

  Widget _buildProfitLossStatistics() {
    if (data is! ProfitLossReportData) {
      return const Text('بيانات غير صحيحة');
    }

    final profitLoss = data as ProfitLossReportData;
    final profitMargin = profitLoss.totalRevenue > 0
        ? (profitLoss.netProfit / profitLoss.totalRevenue) * 100
        : 0;

    return Column(
      children: [
        _buildStatisticRow(
          'إجمالي الإيرادات',
          profitLoss.totalRevenue,
          AppColors.success,
        ),
        _buildStatisticRow(
          'إجمالي المصروفات',
          profitLoss.totalExpense,
          AppColors.error,
        ),
        _buildStatisticRow(
          'صافي الربح',
          profitLoss.netProfit,
          profitLoss.netProfit >= 0 ? AppColors.success : AppColors.error,
        ),
        const Divider(),
        _buildPercentageRow(
          'هامش الربح',
          profitMargin.toDouble(),
          profitMargin >= 0 ? AppColors.success : AppColors.error,
        ),
        _buildCountRow(
          'عدد حسابات الإيرادات',
          profitLoss.revenueItems.length,
          AppColors.info,
        ),
        _buildCountRow(
          'عدد حسابات المصروفات',
          profitLoss.expenseItems.length,
          AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildBalanceSheetStatistics() {
    if (data is! BalanceSheetReportData) {
      return const Text('بيانات غير صحيحة');
    }

    final balanceSheet = data as BalanceSheetReportData;
    final totalLiabilitiesAndEquity =
        balanceSheet.totalLiabilities + balanceSheet.totalEquity;
    final balanceDifference =
        balanceSheet.totalAssets - totalLiabilitiesAndEquity;

    return Column(
      children: [
        _buildStatisticRow(
          'إجمالي الأصول',
          balanceSheet.totalAssets,
          AppColors.primary,
        ),
        _buildStatisticRow(
          'إجمالي الخصوم',
          balanceSheet.totalLiabilities,
          AppColors.error,
        ),
        _buildStatisticRow(
          'إجمالي حقوق الملكية',
          balanceSheet.totalEquity,
          AppColors.success,
        ),
        const Divider(),
        _buildStatisticRow(
          'الفرق في الميزانية',
          balanceDifference.abs(),
          balanceDifference == 0 ? AppColors.success : AppColors.warning,
        ),
        _buildCountRow(
          'عدد الأصول',
          balanceSheet.assets.length,
          AppColors.info,
        ),
        _buildCountRow(
          'عدد الخصوم',
          balanceSheet.liabilities.length,
          AppColors.warning,
        ),
        _buildCountRow(
          'عدد حقوق الملكية',
          balanceSheet.equity.length,
          AppColors.secondary,
        ),
      ],
    );
  }

  Widget _buildCustomerAgingStatistics() {
    if (data is! List<CustomerAgingItem>) {
      return const Text('بيانات غير صحيحة');
    }

    final items = data as List<CustomerAgingItem>;
    final totalBalance = items.fold<double>(
      0,
      (sum, item) => sum + item.balance,
    );
    final currentTotal = items.fold<double>(
      0,
      (sum, item) => sum + item.currentAmount,
    );
    final overdueTotal = totalBalance - currentTotal;

    return Column(
      children: [
        _buildStatisticRow('إجمالي الأرصدة', totalBalance, AppColors.primary),
        _buildStatisticRow('المبالغ الجارية', currentTotal, AppColors.success),
        _buildStatisticRow('المبالغ المتأخرة', overdueTotal, AppColors.error),
        const Divider(),
        _buildPercentageRow(
          'نسبة التأخير',
          totalBalance > 0 ? (overdueTotal / totalBalance) * 100 : 0,
          AppColors.warning,
        ),
        _buildCountRow('عدد العملاء', items.length, AppColors.info),
      ],
    );
  }

  Widget _buildSalesAnalysisStatistics() {
    if (data is! List<SalesAnalysisItem>) {
      return const Text('بيانات غير صحيحة');
    }

    final items = data as List<SalesAnalysisItem>;
    final totalSales = items.fold<double>(
      0,
      (sum, item) => sum + item.totalSales,
    );
    final totalInvoices = items.fold<int>(
      0,
      (sum, item) => sum + item.invoiceCount,
    );
    final averageDailySales = items.isNotEmpty ? totalSales / items.length : 0;
    final averageInvoiceValue = totalInvoices > 0
        ? totalSales / totalInvoices
        : 0;

    return Column(
      children: [
        _buildStatisticRow('إجمالي المبيعات', totalSales, AppColors.success),
        _buildStatisticRow(
          'متوسط المبيعات اليومية',
          averageDailySales.toDouble(),
          AppColors.primary,
        ),
        _buildStatisticRow(
          'متوسط قيمة الفاتورة',
          averageInvoiceValue.toDouble(),
          AppColors.info,
        ),
        const Divider(),
        _buildCountRow('إجمالي الفواتير', totalInvoices, AppColors.secondary),
        _buildCountRow('عدد الأيام', items.length, AppColors.warning),
      ],
    );
  }

  Widget _buildPurchaseAnalysisStatistics() {
    if (data is! List<PurchaseAnalysisItem>) {
      return const Text('بيانات غير صحيحة');
    }

    final items = data as List<PurchaseAnalysisItem>;
    final totalPurchases = items.fold<double>(
      0,
      (sum, item) => sum + item.totalPurchases,
    );
    final totalInvoices = items.fold<int>(
      0,
      (sum, item) => sum + item.invoiceCount,
    );
    final averageDailyPurchases = items.isNotEmpty
        ? totalPurchases / items.length
        : 0;
    final averageInvoiceValue = totalInvoices > 0
        ? totalPurchases / totalInvoices
        : 0;

    return Column(
      children: [
        _buildStatisticRow('إجمالي المشتريات', totalPurchases, AppColors.error),
        _buildStatisticRow(
          'متوسط المشتريات اليومية',
          averageDailyPurchases.toDouble(),
          AppColors.primary,
        ),
        _buildStatisticRow(
          'متوسط قيمة الفاتورة',
          averageInvoiceValue.toDouble(),
          AppColors.info,
        ),
        const Divider(),
        _buildCountRow('إجمالي الفواتير', totalInvoices, AppColors.secondary),
        _buildCountRow('عدد الأيام', items.length, AppColors.warning),
      ],
    );
  }

  Widget _buildInventoryStatistics() {
    if (data is! List<InventoryReportItem>) {
      return const Text('بيانات غير صحيحة');
    }

    final items = data as List<InventoryReportItem>;
    final totalValue = items.fold<double>(
      0,
      (sum, item) => sum + item.totalCostValue,
    );
    final outOfStock = items
        .where((item) => item.stockStatus == 'out_of_stock')
        .length;
    final lowStock = items
        .where((item) => item.stockStatus == 'low_stock')
        .length;
    final inStock = items
        .where((item) => item.stockStatus == 'in_stock')
        .length;

    return Column(
      children: [
        _buildStatisticRow(
          'إجمالي قيمة المخزون',
          totalValue,
          AppColors.primary,
        ),
        const Divider(),
        _buildCountRow('نفد المخزون', outOfStock, AppColors.error),
        _buildCountRow('مخزون منخفض', lowStock, AppColors.warning),
        _buildCountRow('مخزون متوفر', inStock, AppColors.success),
        _buildCountRow('إجمالي الأصناف', items.length, AppColors.info),
      ],
    );
  }

  Widget _buildDefaultStatistics() {
    return const Text('إحصائيات غير متوفرة لهذا النوع من التقارير');
  }

  Widget _buildStatisticRow(String label, double value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(
            value.toStringAsFixed(2),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountRow(String label, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPercentageRow(String label, double percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
