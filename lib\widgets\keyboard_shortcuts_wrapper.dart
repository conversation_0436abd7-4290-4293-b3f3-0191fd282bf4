import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/keyboard_shortcuts_service.dart';
import '../services/keyboard_intents.dart';
import '../services/undo_redo_service.dart';

/// Widget لتطبيق اختصارات لوحة المفاتيح
class KeyboardShortcutsWrapper extends StatefulWidget {
  final Widget child;
  final String screenType;
  final VoidCallback? onNew;
  final VoidCallback? onSave;
  final VoidCallback? onOpen;
  final VoidCallback? onPrint;
  final VoidCallback? onSearch;
  final VoidCallback? onRefresh;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onHelp;
  final Map<LogicalKeySet, VoidCallback>? customShortcuts;

  const KeyboardShortcutsWrapper({
    super.key,
    required this.child,
    this.screenType = 'global',
    this.onNew,
    this.onSave,
    this.onOpen,
    this.onPrint,
    this.onSearch,
    this.onRefresh,
    this.onEdit,
    this.onDelete,
    this.onHelp,
    this.customShortcuts,
  });

  @override
  State<KeyboardShortcutsWrapper> createState() =>
      _KeyboardShortcutsWrapperState();
}

class _KeyboardShortcutsWrapperState extends State<KeyboardShortcutsWrapper> {
  late UndoRedoService _undoRedoService;

  @override
  void initState() {
    super.initState();
    _undoRedoService = UndoRedoService();
  }

  @override
  Widget build(BuildContext context) {
    final shortcuts = KeyboardShortcutsService.getShortcutsForScreen(
      widget.screenType,
    );

    return Shortcuts(
      shortcuts: shortcuts,
      child: Actions(
        actions: _buildActions(),
        child: Focus(autofocus: true, child: widget.child),
      ),
    );
  }

  Map<Type, Action<Intent>> _buildActions() {
    return {
      // اختصارات الملف
      NewIntent: CallbackAction<NewIntent>(
        onInvoke: (_) => _handleAction(widget.onNew, 'جديد'),
      ),
      SaveIntent: CallbackAction<SaveIntent>(
        onInvoke: (_) => _handleAction(widget.onSave, 'حفظ'),
      ),
      OpenIntent: CallbackAction<OpenIntent>(
        onInvoke: (_) => _handleAction(widget.onOpen, 'فتح'),
      ),
      PrintIntent: CallbackAction<PrintIntent>(
        onInvoke: (_) => _handleAction(widget.onPrint, 'طباعة'),
      ),

      // اختصارات التحرير
      UndoIntent: CallbackAction<UndoIntent>(onInvoke: (_) => _handleUndo()),
      RedoIntent: CallbackAction<RedoIntent>(onInvoke: (_) => _handleRedo()),
      EditIntent: CallbackAction<EditIntent>(
        onInvoke: (_) => _handleAction(widget.onEdit, 'تحرير'),
      ),
      DeleteIntent: CallbackAction<DeleteIntent>(
        onInvoke: (_) => _handleAction(widget.onDelete, 'حذف'),
      ),

      // اختصارات البحث والتنقل
      SearchIntent: CallbackAction<SearchIntent>(
        onInvoke: (_) => _handleAction(widget.onSearch, 'بحث'),
      ),
      RefreshIntent: CallbackAction<RefreshIntent>(
        onInvoke: (_) => _handleAction(widget.onRefresh, 'تحديث'),
      ),

      // اختصارات المساعدة
      HelpIntent: CallbackAction<HelpIntent>(onInvoke: (_) => _handleHelp()),
      CancelIntent: CallbackAction<CancelIntent>(
        onInvoke: (_) => _handleCancel(),
      ),

      // اختصارات محاسبية
      NewJournalEntryIntent: CallbackAction<NewJournalEntryIntent>(
        onInvoke: (_) => _handleNewJournalEntry(),
      ),
      NewInvoiceIntent: CallbackAction<NewInvoiceIntent>(
        onInvoke: (_) => _handleNewInvoice(),
      ),
      BalanceSheetIntent: CallbackAction<BalanceSheetIntent>(
        onInvoke: (_) => _handleBalanceSheet(),
      ),
      IncomeStatementIntent: CallbackAction<IncomeStatementIntent>(
        onInvoke: (_) => _handleIncomeStatement(),
      ),
      TrialBalanceIntent: CallbackAction<TrialBalanceIntent>(
        onInvoke: (_) => _handleTrialBalance(),
      ),

      // اختصارات التنقل
      NextFieldIntent: CallbackAction<NextFieldIntent>(
        onInvoke: (_) => _handleNextField(),
      ),
      PreviousFieldIntent: CallbackAction<PreviousFieldIntent>(
        onInvoke: (_) => _handlePreviousField(),
      ),
    };
  }

  // ===============================
  // معالجات الأحداث
  // ===============================

  void _handleAction(VoidCallback? callback, String actionName) {
    if (callback != null) {
      callback();
    } else {
      _showShortcutMessage('$actionName (${_getShortcutText()})');
    }
  }

  void _handleUndo() async {
    final success = await _undoRedoService.undo();
    if (success) {
      _showShortcutMessage('تم التراجع');
    } else {
      _showShortcutMessage('لا توجد عمليات للتراجع');
    }
  }

  void _handleRedo() async {
    final success = await _undoRedoService.redo();
    if (success) {
      _showShortcutMessage('تم الإعادة');
    } else {
      _showShortcutMessage('لا توجد عمليات للإعادة');
    }
  }

  void _handleHelp() {
    if (widget.onHelp != null) {
      widget.onHelp!();
    } else {
      _showShortcutsDialog();
    }
  }

  void _handleCancel() {
    Navigator.of(context).maybePop();
  }

  void _handleNewJournalEntry() {
    Navigator.of(context).pushNamed('/journal_entry');
  }

  void _handleNewInvoice() {
    Navigator.of(context).pushNamed('/invoice');
  }

  void _handleBalanceSheet() {
    Navigator.of(context).pushNamed('/balance_sheet');
  }

  void _handleIncomeStatement() {
    Navigator.of(context).pushNamed('/income_statement');
  }

  void _handleTrialBalance() {
    Navigator.of(context).pushNamed('/trial_balance');
  }

  void _handleNextField() {
    FocusScope.of(context).nextFocus();
  }

  void _handlePreviousField() {
    FocusScope.of(context).previousFocus();
  }

  // ===============================
  // مساعدات
  // ===============================

  String _getShortcutText() {
    // يمكن تحسين هذا لإظهار الاختصار الفعلي
    return 'اختصار';
  }

  void _showShortcutMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showShortcutsDialog() {
    showDialog(
      context: context,
      builder: (context) => const ShortcutsHelpDialog(),
    );
  }
}

/// حوار مساعدة الاختصارات
class ShortcutsHelpDialog extends StatelessWidget {
  const ShortcutsHelpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final shortcuts = KeyboardShortcutsService.getShortcutsByCategory();

    return AlertDialog(
      title: const Text('دليل اختصارات لوحة المفاتيح'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: shortcuts.entries.map((category) {
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.key,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      ...category.value.entries.map((shortcut) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  _formatShortcut(shortcut.key),
                                  style: const TextStyle(
                                    fontFamily: 'monospace',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(flex: 3, child: Text(shortcut.value)),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  String _formatShortcut(LogicalKeySet keySet) {
    final keys = <String>[];

    if (keySet.keys.contains(LogicalKeyboardKey.control) ||
        keySet.keys.contains(LogicalKeyboardKey.controlLeft) ||
        keySet.keys.contains(LogicalKeyboardKey.controlRight)) {
      keys.add('Ctrl');
    }

    if (keySet.keys.contains(LogicalKeyboardKey.shift) ||
        keySet.keys.contains(LogicalKeyboardKey.shiftLeft) ||
        keySet.keys.contains(LogicalKeyboardKey.shiftRight)) {
      keys.add('Shift');
    }

    if (keySet.keys.contains(LogicalKeyboardKey.alt) ||
        keySet.keys.contains(LogicalKeyboardKey.altLeft) ||
        keySet.keys.contains(LogicalKeyboardKey.altRight)) {
      keys.add('Alt');
    }

    // إضافة المفتاح الأساسي
    for (final key in keySet.keys) {
      if (key != LogicalKeyboardKey.control &&
          key != LogicalKeyboardKey.controlLeft &&
          key != LogicalKeyboardKey.controlRight &&
          key != LogicalKeyboardKey.shift &&
          key != LogicalKeyboardKey.shiftLeft &&
          key != LogicalKeyboardKey.shiftRight &&
          key != LogicalKeyboardKey.alt &&
          key != LogicalKeyboardKey.altLeft &&
          key != LogicalKeyboardKey.altRight) {
        keys.add(_getKeyName(key));
      }
    }

    return keys.join(' + ');
  }

  String _getKeyName(LogicalKeyboardKey key) {
    if (key == LogicalKeyboardKey.keyA) return 'A';
    if (key == LogicalKeyboardKey.keyB) return 'B';
    if (key == LogicalKeyboardKey.keyC) return 'C';
    if (key == LogicalKeyboardKey.keyD) return 'D';
    if (key == LogicalKeyboardKey.keyE) return 'E';
    if (key == LogicalKeyboardKey.keyF) return 'F';
    if (key == LogicalKeyboardKey.keyG) return 'G';
    if (key == LogicalKeyboardKey.keyH) return 'H';
    if (key == LogicalKeyboardKey.keyI) return 'I';
    if (key == LogicalKeyboardKey.keyJ) return 'J';
    if (key == LogicalKeyboardKey.keyK) return 'K';
    if (key == LogicalKeyboardKey.keyL) return 'L';
    if (key == LogicalKeyboardKey.keyM) return 'M';
    if (key == LogicalKeyboardKey.keyN) return 'N';
    if (key == LogicalKeyboardKey.keyO) return 'O';
    if (key == LogicalKeyboardKey.keyP) return 'P';
    if (key == LogicalKeyboardKey.keyQ) return 'Q';
    if (key == LogicalKeyboardKey.keyR) return 'R';
    if (key == LogicalKeyboardKey.keyS) return 'S';
    if (key == LogicalKeyboardKey.keyT) return 'T';
    if (key == LogicalKeyboardKey.keyU) return 'U';
    if (key == LogicalKeyboardKey.keyV) return 'V';
    if (key == LogicalKeyboardKey.keyW) return 'W';
    if (key == LogicalKeyboardKey.keyX) return 'X';
    if (key == LogicalKeyboardKey.keyY) return 'Y';
    if (key == LogicalKeyboardKey.keyZ) return 'Z';
    if (key == LogicalKeyboardKey.f1) return 'F1';
    if (key == LogicalKeyboardKey.f2) return 'F2';
    if (key == LogicalKeyboardKey.f3) return 'F3';
    if (key == LogicalKeyboardKey.f4) return 'F4';
    if (key == LogicalKeyboardKey.f5) return 'F5';
    if (key == LogicalKeyboardKey.f6) return 'F6';
    if (key == LogicalKeyboardKey.f7) return 'F7';
    if (key == LogicalKeyboardKey.f8) return 'F8';
    if (key == LogicalKeyboardKey.f9) return 'F9';
    if (key == LogicalKeyboardKey.f10) return 'F10';
    if (key == LogicalKeyboardKey.f11) return 'F11';
    if (key == LogicalKeyboardKey.f12) return 'F12';
    if (key == LogicalKeyboardKey.enter) return 'Enter';
    if (key == LogicalKeyboardKey.escape) return 'Esc';
    if (key == LogicalKeyboardKey.tab) return 'Tab';
    if (key == LogicalKeyboardKey.space) return 'Space';
    if (key == LogicalKeyboardKey.delete) return 'Del';
    if (key == LogicalKeyboardKey.insert) return 'Ins';
    if (key == LogicalKeyboardKey.pageUp) return 'PgUp';
    if (key == LogicalKeyboardKey.pageDown) return 'PgDn';
    if (key == LogicalKeyboardKey.arrowUp) return '↑';
    if (key == LogicalKeyboardKey.arrowDown) return '↓';
    if (key == LogicalKeyboardKey.arrowLeft) return '←';
    if (key == LogicalKeyboardKey.arrowRight) return '→';

    return key.keyLabel;
  }
}
