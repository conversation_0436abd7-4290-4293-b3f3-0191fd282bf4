import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

/// خدمة التصدير الحديثة والشاملة
/// توفر تصدير متقدم للتقارير بصيغ متعددة مع تنسيق احترافي
class ModernExportService {
  static const String _companyName = 'دفتر الأستاذ الذكي';

  /// تصدير تقرير إلى PDF مع تنسيق احترافي
  static Future<String?> exportToPDF({
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    Map<String, dynamic>? summary,
    Map<String, dynamic>? filters,
    Uint8List? chartImage,
    bool includeChart = true,
    bool includeSummary = true,
  }) async {
    try {
      LoggingService.info(
        'بدء تصدير PDF',
        category: 'ModernExport',
        data: {'reportTitle': reportTitle, 'recordCount': data.length},
      );

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => _buildPDFHeader(reportTitle),
          footer: (context) => _buildPDFFooter(context),
          build: (context) => [
            // معلومات التقرير
            if (filters != null && filters.isNotEmpty) ...[
              _buildReportInfo(filters),
              pw.SizedBox(height: 20),
            ],

            // الرسم البياني
            if (includeChart && chartImage != null) ...[
              _buildChartSection(chartImage),
              pw.SizedBox(height: 20),
            ],

            // جدول البيانات
            if (data.isNotEmpty) ...[
              _buildDataTable(data, headers),
              pw.SizedBox(height: 20),
            ],

            // الملخص
            if (includeSummary && summary != null && summary.isNotEmpty) ...[
              _buildSummarySection(summary),
            ],
          ],
        ),
      );

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${reportTitle}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      await _logExportOperation('PDF', reportTitle, filePath, data.length);

      LoggingService.info(
        'تم تصدير PDF بنجاح',
        category: 'ModernExport',
        data: {'filePath': filePath},
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير PDF',
        category: 'ModernExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// تصدير تقرير إلى Excel مع تنسيق متقدم
  static Future<String?> exportToExcel({
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    Map<String, dynamic>? summary,
    Map<String, dynamic>? filters,
  }) async {
    try {
      LoggingService.info(
        'بدء تصدير Excel',
        category: 'ModernExport',
        data: {'reportTitle': reportTitle, 'recordCount': data.length},
      );

      final excel = Excel.createExcel();
      final sheet = excel[reportTitle];

      int currentRow = 0;

      // رأس الشركة
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        _companyName,
      );
      sheet.merge(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        CellIndex.indexByColumnRow(
          columnIndex: headers.length - 1,
          rowIndex: currentRow,
        ),
      );
      currentRow += 2;

      // عنوان التقرير
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        reportTitle,
      );
      sheet.merge(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        CellIndex.indexByColumnRow(
          columnIndex: headers.length - 1,
          rowIndex: currentRow,
        ),
      );
      currentRow += 2;

      // تاريخ التقرير
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        'تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
      );
      currentRow += 1;

      // معلومات الفلاتر
      if (filters != null && filters.isNotEmpty) {
        currentRow += 1;
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
            )
            .value = TextCellValue(
          'معايير التقرير:',
        );
        currentRow += 1;

        for (final filter in filters.entries) {
          sheet
              .cell(
                CellIndex.indexByColumnRow(
                  columnIndex: 0,
                  rowIndex: currentRow,
                ),
              )
              .value = TextCellValue(
            '${filter.key}: ${filter.value}',
          );
          currentRow += 1;
        }
      }

      currentRow += 2;

      // رأس الجدول
      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
            )
            .value = TextCellValue(
          headers[i],
        );
      }
      currentRow += 1;

      // البيانات
      for (final row in data) {
        for (int i = 0; i < headers.length; i++) {
          final key = headers[i];
          final value = row[key];

          if (value is num) {
            sheet
                .cell(
                  CellIndex.indexByColumnRow(
                    columnIndex: i,
                    rowIndex: currentRow,
                  ),
                )
                .value = DoubleCellValue(
              value.toDouble(),
            );
          } else {
            sheet
                .cell(
                  CellIndex.indexByColumnRow(
                    columnIndex: i,
                    rowIndex: currentRow,
                  ),
                )
                .value = TextCellValue(
              value?.toString() ?? '',
            );
          }
        }
        currentRow += 1;
      }

      // الملخص
      if (summary != null && summary.isNotEmpty) {
        currentRow += 2;
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
            )
            .value = TextCellValue(
          'ملخص التقرير:',
        );
        currentRow += 1;

        for (final item in summary.entries) {
          sheet
              .cell(
                CellIndex.indexByColumnRow(
                  columnIndex: 0,
                  rowIndex: currentRow,
                ),
              )
              .value = TextCellValue(
            '${item.key}: ${item.value}',
          );
          currentRow += 1;
        }
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${reportTitle}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      await _logExportOperation('Excel', reportTitle, filePath, data.length);

      LoggingService.info(
        'تم تصدير Excel بنجاح',
        category: 'ModernExport',
        data: {'filePath': filePath},
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير Excel',
        category: 'ModernExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// تصدير تقرير إلى CSV
  static Future<String?> exportToCSV({
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    Map<String, dynamic>? summary,
  }) async {
    try {
      LoggingService.info(
        'بدء تصدير CSV',
        category: 'ModernExport',
        data: {'reportTitle': reportTitle, 'recordCount': data.length},
      );

      final csvData = <List<String>>[];

      // إضافة معلومات الشركة والتقرير
      csvData.add([_companyName]);
      csvData.add([reportTitle]);
      csvData.add([
        'تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
      ]);
      csvData.add([]); // سطر فارغ

      // إضافة رأس الجدول
      csvData.add(headers);

      // إضافة البيانات
      for (final row in data) {
        final csvRow = <String>[];
        for (final header in headers) {
          csvRow.add(row[header]?.toString() ?? '');
        }
        csvData.add(csvRow);
      }

      // إضافة الملخص
      if (summary != null && summary.isNotEmpty) {
        csvData.add([]); // سطر فارغ
        csvData.add(['ملخص التقرير']);
        for (final item in summary.entries) {
          csvData.add([item.key, item.value.toString()]);
        }
      }

      // تحويل إلى نص CSV
      final csvString = csvData.map((row) => row.join(',')).join('\n');

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${reportTitle}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.csv';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsString(csvString, encoding: utf8);

      await _logExportOperation('CSV', reportTitle, filePath, data.length);

      LoggingService.info(
        'تم تصدير CSV بنجاح',
        category: 'ModernExport',
        data: {'filePath': filePath},
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير CSV',
        category: 'ModernExport',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// مشاركة ملف
  static Future<void> shareFile(String filePath, String title) async {
    try {
      await Share.shareXFiles([
        XFile(filePath),
      ], text: 'تقرير $title من $_companyName');

      LoggingService.info(
        'تم مشاركة الملف',
        category: 'ModernExport',
        data: {'filePath': filePath, 'title': title},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في مشاركة الملف',
        category: 'ModernExport',
        data: {'error': e.toString()},
      );
    }
  }

  /// تسجيل عملية التصدير
  static Future<void> _logExportOperation(
    String format,
    String reportTitle,
    String filePath,
    int recordCount,
  ) async {
    await AuditService.logCreate(
      entityType: AppConstants.auditEntitySystem,
      entityId: 0,
      entityName: 'تصدير تقرير',
      newValues: {
        'format': format,
        'reportTitle': reportTitle,
        'filePath': filePath,
        'recordCount': recordCount,
        'timestamp': DateTime.now().toIso8601String(),
      },
      description: 'تم تصدير تقرير $reportTitle بصيغة $format',
      category: 'ModernExport',
    );
  }

  /// بناء رأس PDF
  static pw.Widget _buildPDFHeader(String reportTitle) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(color: PdfColors.grey300, width: 1),
        ),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            _companyName,
            style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            reportTitle,
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء تذييل PDF
  static pw.Widget _buildPDFFooter(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تم إنشاؤه بواسطة $_companyName',
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
          ),
          pw.Text(
            'صفحة ${context.pageNumber} من ${context.pagesCount}',
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات التقرير
  static pw.Widget _buildReportInfo(Map<String, dynamic> filters) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معايير التقرير',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          ...filters.entries.map(
            (entry) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 4),
              child: pw.Text(
                '${entry.key}: ${entry.value}',
                style: pw.TextStyle(fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الرسم البياني
  static pw.Widget _buildChartSection(Uint8List chartImage) {
    return pw.Container(
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الرسم البياني',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Center(
            child: pw.Image(
              pw.MemoryImage(chartImage),
              height: 300,
              fit: pw.BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء جدول البيانات
  static pw.Widget _buildDataTable(
    List<Map<String, dynamic>> data,
    List<String> headers,
  ) {
    return pw.TableHelper.fromTextArray(
      headers: headers,
      data: data
          .map(
            (row) =>
                headers.map((header) => row[header]?.toString() ?? '').toList(),
          )
          .toList(),
      headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
      headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
      cellAlignment: pw.Alignment.centerLeft,
      cellPadding: const pw.EdgeInsets.all(8),
      border: pw.TableBorder.all(color: PdfColors.grey400),
    );
  }

  /// بناء قسم الملخص
  static pw.Widget _buildSummarySection(Map<String, dynamic> summary) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص التقرير',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          ...summary.entries.map(
            (entry) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 4),
              child: pw.Text(
                '${entry.key}: ${entry.value}',
                style: pw.TextStyle(fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
