/// نموذج الفاتورة المتكررة
/// يحتوي على جميع البيانات المتعلقة بالفواتير المتكررة والدورية
library;

import 'dart:convert';
import 'invoice.dart';
import 'invoice_status.dart';

/// تكرار الفاتورة
enum RecurrenceFrequency {
  daily('daily', 'يومياً'),
  weekly('weekly', 'أسبوعياً'),
  biweekly('biweekly', 'كل أسبوعين'),
  monthly('monthly', 'شهرياً'),
  quarterly('quarterly', 'كل ثلاثة أشهر'),
  semiannually('semiannually', 'كل ستة أشهر'),
  annually('annually', 'سنوياً');

  const RecurrenceFrequency(this.code, this.displayName);
  final String code;
  final String displayName;

  static RecurrenceFrequency fromCode(String code) {
    return RecurrenceFrequency.values.firstWhere(
      (frequency) => frequency.code == code,
      orElse: () => RecurrenceFrequency.monthly,
    );
  }

  /// حساب التاريخ التالي للإنشاء
  DateTime getNextDate(DateTime currentDate) {
    switch (this) {
      case RecurrenceFrequency.daily:
        return currentDate.add(const Duration(days: 1));
      case RecurrenceFrequency.weekly:
        return currentDate.add(const Duration(days: 7));
      case RecurrenceFrequency.biweekly:
        return currentDate.add(const Duration(days: 14));
      case RecurrenceFrequency.monthly:
        return DateTime(
          currentDate.year,
          currentDate.month + 1,
          currentDate.day,
        );
      case RecurrenceFrequency.quarterly:
        return DateTime(
          currentDate.year,
          currentDate.month + 3,
          currentDate.day,
        );
      case RecurrenceFrequency.semiannually:
        return DateTime(
          currentDate.year,
          currentDate.month + 6,
          currentDate.day,
        );
      case RecurrenceFrequency.annually:
        return DateTime(
          currentDate.year + 1,
          currentDate.month,
          currentDate.day,
        );
    }
  }
}

/// نموذج الفاتورة المتكررة
class RecurringInvoice {
  final int? id;
  final String templateName;
  final String invoiceTemplate; // JSON للفاتورة النموذجية
  final RecurrenceFrequency frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime nextGenerationDate;
  final DateTime? lastGeneratedDate;
  final bool isActive;
  final int? customerId;
  final int? supplierId;
  final String? customerName;
  final String? supplierName;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // الفاتورة النموذجية المحللة
  Invoice? _parsedInvoiceTemplate;

  RecurringInvoice({
    this.id,
    required this.templateName,
    required this.invoiceTemplate,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.nextGenerationDate,
    this.lastGeneratedDate,
    this.isActive = true,
    this.customerId,
    this.supplierId,
    this.customerName,
    this.supplierName,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'template_name': templateName,
      'invoice_template': invoiceTemplate,
      'frequency': frequency.code,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'next_generation_date': nextGenerationDate.toIso8601String().split(
        'T',
      )[0],
      'last_generated_date': lastGeneratedDate?.toIso8601String().split('T')[0],
      'is_active': isActive ? 1 : 0,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory RecurringInvoice.fromMap(Map<String, dynamic> map) {
    return RecurringInvoice(
      id: map['id']?.toInt(),
      templateName: map['template_name'] ?? '',
      invoiceTemplate: map['invoice_template'] ?? '',
      frequency: RecurrenceFrequency.fromCode(map['frequency'] ?? 'monthly'),
      startDate: DateTime.parse(map['start_date']),
      endDate: map['end_date'] != null ? DateTime.parse(map['end_date']) : null,
      nextGenerationDate: DateTime.parse(map['next_generation_date']),
      lastGeneratedDate: map['last_generated_date'] != null
          ? DateTime.parse(map['last_generated_date'])
          : null,
      isActive: (map['is_active'] ?? 1) == 1,
      customerId: map['customer_id']?.toInt(),
      supplierId: map['supplier_id']?.toInt(),
      customerName: map['customer_name'],
      supplierName: map['supplier_name'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// إنشاء فاتورة متكررة من فاتورة موجودة
  factory RecurringInvoice.fromInvoice({
    required String templateName,
    required Invoice invoice,
    required RecurrenceFrequency frequency,
    required DateTime startDate,
    DateTime? endDate,
    String? notes,
  }) {
    // تحويل الفاتورة إلى JSON
    final invoiceJson = jsonEncode(invoice.toMap());

    return RecurringInvoice(
      templateName: templateName,
      invoiceTemplate: invoiceJson,
      frequency: frequency,
      startDate: startDate,
      endDate: endDate,
      nextGenerationDate: startDate,
      customerId: invoice.customerId,
      supplierId: invoice.supplierId,
      notes: notes,
    );
  }

  RecurringInvoice copyWith({
    int? id,
    String? templateName,
    String? invoiceTemplate,
    RecurrenceFrequency? frequency,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? nextGenerationDate,
    DateTime? lastGeneratedDate,
    bool? isActive,
    int? customerId,
    int? supplierId,
    String? customerName,
    String? supplierName,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecurringInvoice(
      id: id ?? this.id,
      templateName: templateName ?? this.templateName,
      invoiceTemplate: invoiceTemplate ?? this.invoiceTemplate,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      nextGenerationDate: nextGenerationDate ?? this.nextGenerationDate,
      lastGeneratedDate: lastGeneratedDate ?? this.lastGeneratedDate,
      isActive: isActive ?? this.isActive,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      customerName: customerName ?? this.customerName,
      supplierName: supplierName ?? this.supplierName,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على الفاتورة النموذجية المحللة
  Invoice? getParsedInvoiceTemplate() {
    if (_parsedInvoiceTemplate != null) {
      return _parsedInvoiceTemplate;
    }

    try {
      final Map<String, dynamic> invoiceMap = jsonDecode(invoiceTemplate);
      _parsedInvoiceTemplate = Invoice.fromMap(invoiceMap);
      return _parsedInvoiceTemplate;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء فاتورة جديدة من النموذج
  Invoice? generateNewInvoice({
    required String newInvoiceNumber,
    DateTime? invoiceDate,
  }) {
    final template = getParsedInvoiceTemplate();
    if (template == null) return null;

    return template.copyWith(
      id: null, // فاتورة جديدة
      invoiceNumber: newInvoiceNumber,
      invoiceDate: invoiceDate ?? DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      // إعادة تعيين حالة الدفع
      paidAmount: 0.0,
      remainingAmount: template.totalAmount,
      status: InvoiceStatus.draft,
      payments: [], // بدون دفعات
    );
  }

  /// تحديث تاريخ الإنشاء التالي
  RecurringInvoice updateNextGenerationDate() {
    final nextDate = frequency.getNextDate(nextGenerationDate);
    return copyWith(
      nextGenerationDate: nextDate,
      lastGeneratedDate: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // خصائص مساعدة
  bool get isDue {
    return isActive && DateTime.now().isAfter(nextGenerationDate);
  }

  bool get isExpired {
    return endDate != null && DateTime.now().isAfter(endDate!);
  }

  bool get shouldGenerate {
    return isDue && !isExpired;
  }

  String get displayFrequency {
    return frequency.displayName;
  }

  String get clientName {
    return customerName ?? supplierName ?? 'غير محدد';
  }

  @override
  String toString() {
    return 'RecurringInvoice(id: $id, name: $templateName, frequency: ${frequency.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecurringInvoice && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
