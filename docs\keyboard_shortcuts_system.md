# نظام اختصارات لوحة المفاتيح - Smart Ledger

## نظرة عامة

تم تطوير نظام شامل لاختصارات لوحة المفاتيح في تطبيق Smart Ledger لتحسين تجربة المستخدم وزيادة الإنتاجية. يتضمن النظام:

- **اختصارات أساسية** للعمليات الشائعة
- **نظام Undo/Redo** للتراجع والإعادة
- **اختصارات محاسبية مخصصة**
- **دليل تفاعلي للاختصارات**

## المكونات الرئيسية

### 1. خدمة اختصارات لوحة المفاتيح
**الملف:** `lib/services/keyboard_shortcuts_service.dart`

تدير جميع اختصارات لوحة المفاتيح في التطبيق وتوفر:
- اختصارات عامة للتطبيق
- اختصارات مخصصة للنماذج
- اختصارات محاسبية متخصصة
- تجميع الاختصارات حسب الفئة

### 2. تعريفات النوايا (Intents)
**الملف:** `lib/services/keyboard_intents.dart`

يحتوي على جميع Intent classes المطلوبة للاختصارات:
- اختصارات الملف (جديد، حفظ، طباعة)
- اختصارات التحرير (نسخ، لصق، قص)
- اختصارات البحث والتنقل
- اختصارات محاسبية مخصصة

### 3. نظام Undo/Redo
**الملف:** `lib/services/undo_redo_service.dart`

نظام متقدم للتراجع والإعادة يدعم:
- مكدس عمليات التراجع والإعادة
- عمليات مجمعة
- عمليات محاسبية مخصصة
- حد أقصى للعمليات المحفوظة (50 عملية)

### 4. مساعدات Undo/Redo
**الملف:** `lib/services/undo_redo_helpers.dart`

دوال مساعدة لتطبيق نظام Undo/Redo على الخدمات المختلفة:
- مساعدات الحسابات
- مساعدات القيود المحاسبية
- مساعدات الفواتير
- مساعدات الأصناف والعملاء والموردين

### 5. Widget الاختصارات
**الملف:** `lib/widgets/keyboard_shortcuts_wrapper.dart`

Widget يطبق اختصارات لوحة المفاتيح على أي شاشة:
- دعم اختصارات مخصصة حسب نوع الشاشة
- معالجة الأحداث تلقائياً
- رسائل تأكيد للمستخدم

### 6. شاشة دليل الاختصارات
**الملف:** `lib/screens/shortcuts_guide_screen.dart`

شاشة تفاعلية تعرض جميع الاختصارات المتاحة:
- تبويبات حسب الفئة
- بحث في الاختصارات
- نسخ الاختصارات
- مرجع سريع

### 7. Widget حالة Undo/Redo
**الملف:** `lib/widgets/undo_redo_status_widget.dart`

عرض حالة عمليات التراجع والإعادة:
- أزرار تفاعلية للتراجع والإعادة
- عرض عدد العمليات المتاحة
- تاريخ العمليات

## الاختصارات المتاحة

### اختصارات عامة
| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl + N` | جديد |
| `Ctrl + S` | حفظ |
| `Ctrl + O` | فتح |
| `Ctrl + P` | طباعة |
| `Ctrl + F` | بحث |
| `Ctrl + Z` | تراجع |
| `Ctrl + Y` | إعادة |
| `F1` | مساعدة |
| `F5` | تحديث |
| `Esc` | إلغاء |

### اختصارات محاسبية
| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl + J` | قيد محاسبي جديد |
| `Ctrl + M` | فاتورة جديدة |
| `Ctrl + B` | الميزانية العمومية |
| `Ctrl + I` | قائمة الدخل |
| `Ctrl + T` | ميزان المراجعة |
| `F4` | ترحيل القيد |
| `F9` | حساب المجاميع |

### اختصارات التنقل
| الاختصار | الوظيفة |
|----------|---------|
| `Tab` | الحقل التالي |
| `Shift + Tab` | الحقل السابق |
| `Page Up` | أول سجل |
| `Page Down` | آخر سجل |
| `Ctrl + ↑` | السجل السابق |
| `Ctrl + ↓` | السجل التالي |

## كيفية الاستخدام

### 1. تطبيق الاختصارات على شاشة جديدة

```dart
return KeyboardShortcutsWrapper(
  screenType: 'account', // نوع الشاشة
  onNew: _addNewAccount,
  onSave: _saveAccount,
  onSearch: _focusSearchField,
  onHelp: _showShortcutsHelp,
  child: Scaffold(
    // محتوى الشاشة
  ),
);
```

### 2. استخدام نظام Undo/Redo

```dart
// إضافة عملية مع دعم التراجع
final result = await UndoRedoHelpers.addAccountWithUndo(
  accountData: accountData,
  description: 'إضافة حساب جديد: ${accountData['name']}',
);

// حذف عملية مع دعم التراجع
final success = await UndoRedoHelpers.deleteAccountWithUndo(
  accountId: accountId,
  accountData: accountData,
  description: 'حذف الحساب: ${accountData['name']}',
);
```

### 3. عرض حالة Undo/Redo

```dart
// في AppBar
actions: [
  const UndoRedoStatusWidget(
    showLabels: false,
    showCounts: false,
  ),
  // باقي الأزرار...
],
```

### 4. عمليات مجمعة

```dart
// بدء مجموعة عمليات
UndoRedoHelpers.beginOperationGroup('إضافة فاتورة مع بنودها');

// تنفيذ عدة عمليات
await UndoRedoHelpers.addInvoiceWithUndo(...);
await UndoRedoHelpers.addItemWithUndo(...);
await UndoRedoHelpers.addItemWithUndo(...);

// إنهاء المجموعة
UndoRedoHelpers.endOperationGroup();
```

## الميزات المتقدمة

### 1. اختصارات مخصصة حسب الشاشة
يمكن تخصيص اختصارات مختلفة لكل نوع شاشة:
- `global`: اختصارات عامة
- `account`: اختصارات الحسابات
- `journal`: اختصارات القيود
- `invoice`: اختصارات الفواتير
- `report`: اختصارات التقارير

### 2. البحث في الاختصارات
شاشة دليل الاختصارات تدعم:
- البحث بالوصف
- البحث بالاختصار نفسه
- فلترة حسب الفئة

### 3. نسخ الاختصارات
يمكن نسخ أي اختصار للحافظة لسهولة المشاركة أو التوثيق.

### 4. المرجع السريع
عرض سريع لأهم الاختصارات الأساسية.

## التخصيص والتوسيع

### إضافة اختصار جديد

1. **إضافة Intent جديد:**
```dart
class CustomActionIntent extends Intent {
  const CustomActionIntent();
}
```

2. **إضافة الاختصار للخدمة:**
```dart
LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyK): const CustomActionIntent(),
```

3. **إضافة معالج الحدث:**
```dart
CustomActionIntent: CallbackAction<CustomActionIntent>(
  onInvoke: (_) => _handleCustomAction(),
),
```

### إضافة عملية Undo/Redo جديدة

```dart
class CustomAction extends UndoRedoAction {
  // تنفيذ العملية المخصصة
  
  @override
  Future<void> undo() async {
    // منطق التراجع
  }

  @override
  Future<void> redo() async {
    // منطق الإعادة
  }
}
```

## الاختبار

يمكن اختبار نظام الاختصارات من خلال:

1. **اختبار الاختصارات الأساسية:**
   - `Ctrl + N` لإضافة جديد
   - `Ctrl + S` للحفظ
   - `Ctrl + F` للبحث

2. **اختبار نظام Undo/Redo:**
   - تنفيذ عملية ثم `Ctrl + Z` للتراجع
   - `Ctrl + Y` للإعادة

3. **اختبار دليل الاختصارات:**
   - `F1` لفتح دليل المساعدة
   - البحث في الاختصارات
   - نسخ اختصار

## الملاحظات التقنية

- النظام يدعم جميع منصات Flutter (Windows, Android, Web)
- الاختصارات تعمل فقط عند التركيز على التطبيق
- نظام Undo/Redo محدود بـ 50 عملية لتوفير الذاكرة
- جميع الاختصارات قابلة للتخصيص والتوسيع

## المساهمة

لإضافة اختصارات جديدة أو تحسين النظام:

1. إضافة الاختصار في `KeyboardShortcutsService`
2. إضافة Intent في `keyboard_intents.dart`
3. إضافة معالج في `KeyboardShortcutsWrapper`
4. تحديث دليل الاختصارات
5. إضافة اختبارات مناسبة

---

**تم تطوير هذا النظام كجزء من مشروع Smart Ledger**
**المطور: مجد محمد زياد يسير**
