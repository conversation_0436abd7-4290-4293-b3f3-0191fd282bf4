/// نموذج حركة المخزون المحسن
/// يدعم المواقع المتعددة وأنواع الحركات المختلفة
library;

class InventoryMovement {
  final int? id;
  final int itemId;
  final int? warehouseId;
  final int? locationId;
  final String movementType; // 'in', 'out', 'transfer', 'adjustment'
  final double quantity;
  final double unitCost;
  final double totalCost;
  final String referenceType; // 'invoice', 'purchase', 'transfer', 'adjustment', 'inventory_count'
  final int? referenceId;
  final String? batchNumber; // رقم الدفعة
  final DateTime? expiryDate; // تاريخ الانتهاء
  final String? serialNumber; // الرقم التسلسلي
  final String? description;
  final int? fromLocationId; // للنقل بين المواقع
  final int? toLocationId; // للنقل بين المواقع
  final String? notes;
  final int? userId; // المستخدم الذي قام بالحركة
  final DateTime movementDate;
  final DateTime createdAt;

  InventoryMovement({
    this.id,
    required this.itemId,
    this.warehouseId,
    this.locationId,
    required this.movementType,
    required this.quantity,
    required this.unitCost,
    required this.totalCost,
    required this.referenceType,
    this.referenceId,
    this.batchNumber,
    this.expiryDate,
    this.serialNumber,
    this.description,
    this.fromLocationId,
    this.toLocationId,
    this.notes,
    this.userId,
    required this.movementDate,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'movement_type': movementType,
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_cost': totalCost,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'serial_number': serialNumber,
      'description': description,
      'from_location_id': fromLocationId,
      'to_location_id': toLocationId,
      'notes': notes,
      'user_id': userId,
      'movement_date': movementDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory InventoryMovement.fromMap(Map<String, dynamic> map) {
    return InventoryMovement(
      id: map['id']?.toInt(),
      itemId: map['item_id']?.toInt() ?? 0,
      warehouseId: map['warehouse_id']?.toInt(),
      locationId: map['location_id']?.toInt(),
      movementType: map['movement_type'] ?? '',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unitCost: map['unit_cost']?.toDouble() ?? 0.0,
      totalCost: map['total_cost']?.toDouble() ?? 0.0,
      referenceType: map['reference_type'] ?? '',
      referenceId: map['reference_id']?.toInt(),
      batchNumber: map['batch_number'],
      expiryDate: map['expiry_date'] != null 
          ? DateTime.parse(map['expiry_date']) 
          : null,
      serialNumber: map['serial_number'],
      description: map['description'],
      fromLocationId: map['from_location_id']?.toInt(),
      toLocationId: map['to_location_id']?.toInt(),
      notes: map['notes'],
      userId: map['user_id']?.toInt(),
      movementDate: DateTime.parse(map['movement_date'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// نسخ مع تعديل
  InventoryMovement copyWith({
    int? id,
    int? itemId,
    int? warehouseId,
    int? locationId,
    String? movementType,
    double? quantity,
    double? unitCost,
    double? totalCost,
    String? referenceType,
    int? referenceId,
    String? batchNumber,
    DateTime? expiryDate,
    String? serialNumber,
    String? description,
    int? fromLocationId,
    int? toLocationId,
    String? notes,
    int? userId,
    DateTime? movementDate,
    DateTime? createdAt,
  }) {
    return InventoryMovement(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      movementType: movementType ?? this.movementType,
      quantity: quantity ?? this.quantity,
      unitCost: unitCost ?? this.unitCost,
      totalCost: totalCost ?? this.totalCost,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      serialNumber: serialNumber ?? this.serialNumber,
      description: description ?? this.description,
      fromLocationId: fromLocationId ?? this.fromLocationId,
      toLocationId: toLocationId ?? this.toLocationId,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      movementDate: movementDate ?? this.movementDate,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'InventoryMovement(id: $id, itemId: $itemId, type: $movementType, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InventoryMovement && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  String get movementTypeDisplay {
    switch (movementType) {
      case 'in':
        return 'إدخال';
      case 'out':
        return 'إخراج';
      case 'transfer':
        return 'نقل';
      case 'adjustment':
        return 'تعديل';
      default:
        return movementType;
    }
  }

  String get referenceTypeDisplay {
    switch (referenceType) {
      case 'invoice':
        return 'فاتورة';
      case 'purchase':
        return 'مشتريات';
      case 'transfer':
        return 'نقل';
      case 'adjustment':
        return 'تعديل';
      case 'inventory_count':
        return 'جرد';
      default:
        return referenceType;
    }
  }

  bool get isInbound {
    return movementType == 'in';
  }

  bool get isOutbound {
    return movementType == 'out';
  }

  bool get isTransfer {
    return movementType == 'transfer';
  }

  bool get isAdjustment {
    return movementType == 'adjustment';
  }

  bool get hasBatch {
    return batchNumber?.isNotEmpty == true;
  }

  bool get hasExpiry {
    return expiryDate != null;
  }

  bool get hasSerial {
    return serialNumber?.isNotEmpty == true;
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  bool get isNearExpiry {
    if (expiryDate == null) return false;
    final daysToExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysToExpiry <= 30 && daysToExpiry > 0;
  }
}

/// أنواع حركات المخزون
enum MovementType {
  inbound('in', 'إدخال'),
  outbound('out', 'إخراج'),
  transfer('transfer', 'نقل'),
  adjustment('adjustment', 'تعديل');

  const MovementType(this.value, this.displayName);
  final String value;
  final String displayName;

  static MovementType fromString(String value) {
    return MovementType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MovementType.adjustment,
    );
  }
}

/// أنواع المراجع
enum ReferenceType {
  invoice('invoice', 'فاتورة'),
  purchase('purchase', 'مشتريات'),
  transfer('transfer', 'نقل'),
  adjustment('adjustment', 'تعديل'),
  inventoryCount('inventory_count', 'جرد');

  const ReferenceType(this.value, this.displayName);
  final String value;
  final String displayName;

  static ReferenceType fromString(String value) {
    return ReferenceType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ReferenceType.adjustment,
    );
  }
}
