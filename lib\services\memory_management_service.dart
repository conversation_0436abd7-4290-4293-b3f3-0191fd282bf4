import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../services/logging_service.dart';
import '../services/cache_service.dart';

/// خدمة إدارة الذاكرة المتقدمة
/// توفر مراقبة وتحسين استهلاك الذاكرة في التطبيق
class MemoryManagementService {
  static final MemoryManagementService _instance = MemoryManagementService._internal();
  factory MemoryManagementService() => _instance;
  MemoryManagementService._internal();

  Timer? _monitoringTimer;
  final List<MemorySnapshot> _snapshots = [];
  final Map<String, WeakReference<Object>> _weakReferences = {};
  
  // إعدادات المراقبة
  static const Duration monitoringInterval = Duration(seconds: 30);
  static const int maxSnapshots = 100;
  static const double memoryWarningThreshold = 0.8; // 80%
  static const double memoryCriticalThreshold = 0.9; // 90%
  static const int maxWeakReferences = 1000;

  bool _isMonitoring = false;
  MemoryPressureLevel _currentPressureLevel = MemoryPressureLevel.normal;

  /// بدء مراقبة الذاكرة
  void startMonitoring() {
    if (_isMonitoring) return;

    try {
      _isMonitoring = true;
      
      _monitoringTimer = Timer.periodic(monitoringInterval, (timer) {
        _checkMemoryUsage();
      });

      // مراقبة ضغط الذاكرة في النظام
      if (!kIsWeb) {
        _setupMemoryPressureListener();
      }

      LoggingService.info(
        'بدء مراقبة استهلاك الذاكرة',
        category: 'MemoryManagement',
        data: {'interval': monitoringInterval.inSeconds},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في بدء مراقبة الذاكرة',
        category: 'MemoryManagement',
        data: {'error': e.toString()},
      );
    }
  }

  /// إيقاف مراقبة الذاكرة
  void stopMonitoring() {
    if (!_isMonitoring) return;

    try {
      _isMonitoring = false;
      _monitoringTimer?.cancel();
      _monitoringTimer = null;

      LoggingService.info(
        'تم إيقاف مراقبة استهلاك الذاكرة',
        category: 'MemoryManagement',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إيقاف مراقبة الذاكرة',
        category: 'MemoryManagement',
        data: {'error': e.toString()},
      );
    }
  }

  /// فحص استهلاك الذاكرة
  Future<void> _checkMemoryUsage() async {
    try {
      final snapshot = await _takeMemorySnapshot();
      _snapshots.add(snapshot);

      // الاحتفاظ بعدد محدود من اللقطات
      if (_snapshots.length > maxSnapshots) {
        _snapshots.removeAt(0);
      }

      // تحديد مستوى ضغط الذاكرة
      _updateMemoryPressureLevel(snapshot);

      // تنفيذ إجراءات التحسين إذا لزم الأمر
      if (_currentPressureLevel != MemoryPressureLevel.normal) {
        await _performMemoryOptimization();
      }

      LoggingService.debug(
        'فحص استهلاك الذاكرة',
        category: 'MemoryManagement',
        data: {
          'usedMemoryMB': snapshot.usedMemoryMB,
          'pressureLevel': _currentPressureLevel.name,
          'gcCount': snapshot.gcCount,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص استهلاك الذاكرة',
        category: 'MemoryManagement',
        data: {'error': e.toString()},
      );
    }
  }

  /// أخذ لقطة من حالة الذاكرة
  Future<MemorySnapshot> _takeMemorySnapshot() async {
    final timestamp = DateTime.now();
    
    // الحصول على معلومات الذاكرة من النظام
    int usedMemory = 0;
    int totalMemory = 0;
    int gcCount = 0;

    if (!kIsWeb && Platform.isAndroid || Platform.isIOS) {
      // استخدام ProcessInfo للحصول على معلومات الذاكرة
      try {
        final info = ProcessInfo.currentRss;
        usedMemory = info;
        totalMemory = info * 2; // تقدير تقريبي
      } catch (e) {
        // في حالة فشل الحصول على المعلومات، استخدم تقدير افتراضي
        usedMemory = 50 * 1024 * 1024; // 50 MB
        totalMemory = 200 * 1024 * 1024; // 200 MB
      }
    } else {
      // تقدير افتراضي للمنصات الأخرى
      usedMemory = 50 * 1024 * 1024; // 50 MB
      totalMemory = 200 * 1024 * 1024; // 200 MB
    }

    return MemorySnapshot(
      timestamp: timestamp,
      usedMemory: usedMemory,
      totalMemory: totalMemory,
      gcCount: gcCount,
      cacheSize: CacheService().size,
      weakReferencesCount: _weakReferences.length,
    );
  }

  /// تحديث مستوى ضغط الذاكرة
  void _updateMemoryPressureLevel(MemorySnapshot snapshot) {
    final usageRatio = snapshot.memoryUsageRatio;
    
    final previousLevel = _currentPressureLevel;
    
    if (usageRatio >= memoryCriticalThreshold) {
      _currentPressureLevel = MemoryPressureLevel.critical;
    } else if (usageRatio >= memoryWarningThreshold) {
      _currentPressureLevel = MemoryPressureLevel.warning;
    } else {
      _currentPressureLevel = MemoryPressureLevel.normal;
    }

    // تسجيل تغيير مستوى الضغط
    if (_currentPressureLevel != previousLevel) {
      LoggingService.warning(
        'تغيير مستوى ضغط الذاكرة',
        category: 'MemoryManagement',
        data: {
          'previousLevel': previousLevel.name,
          'currentLevel': _currentPressureLevel.name,
          'usageRatio': usageRatio,
        },
      );
    }
  }

  /// تنفيذ تحسينات الذاكرة
  Future<void> _performMemoryOptimization() async {
    try {
      LoggingService.info(
        'بدء تحسين استهلاك الذاكرة',
        category: 'MemoryManagement',
        data: {'pressureLevel': _currentPressureLevel.name},
      );

      switch (_currentPressureLevel) {
        case MemoryPressureLevel.warning:
          await _performLightOptimization();
          break;
        case MemoryPressureLevel.critical:
          await _performAggressiveOptimization();
          break;
        case MemoryPressureLevel.normal:
          // لا حاجة للتحسين
          break;
      }

      LoggingService.info(
        'تم تحسين استهلاك الذاكرة',
        category: 'MemoryManagement',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحسين استهلاك الذاكرة',
        category: 'MemoryManagement',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحسين خفيف للذاكرة
  Future<void> _performLightOptimization() async {
    // تنظيف التخزين المؤقت
    CacheService().removeExpired();
    
    // تنظيف المراجع الضعيفة
    _cleanupWeakReferences();
    
    // اقتراح تشغيل جامع القمامة
    _suggestGarbageCollection();
  }

  /// تحسين قوي للذاكرة
  Future<void> _performAggressiveOptimization() async {
    // تنظيف شامل للتخزين المؤقت
    CacheService().clear();
    
    // تنظيف جميع المراجع الضعيفة
    _weakReferences.clear();
    
    // تنظيف لقطات الذاكرة القديمة
    if (_snapshots.length > 10) {
      _snapshots.removeRange(0, _snapshots.length - 10);
    }
    
    // فرض تشغيل جامع القمامة
    _forceGarbageCollection();
  }

  /// تنظيف المراجع الضعيفة
  void _cleanupWeakReferences() {
    final keysToRemove = <String>[];
    
    for (final entry in _weakReferences.entries) {
      if (entry.value.target == null) {
        keysToRemove.add(entry.key);
      }
    }
    
    for (final key in keysToRemove) {
      _weakReferences.remove(key);
    }

    LoggingService.debug(
      'تنظيف المراجع الضعيفة',
      category: 'MemoryManagement',
      data: {
        'removedReferences': keysToRemove.length,
        'remainingReferences': _weakReferences.length,
      },
    );
  }

  /// اقتراح تشغيل جامع القمامة
  void _suggestGarbageCollection() {
    // في Dart، لا يمكن فرض تشغيل GC مباشرة
    // لكن يمكن تشجيعه عبر إنشاء وحذف كائنات صغيرة
    for (int i = 0; i < 100; i++) {
      final temp = List.filled(100, 0);
      temp.clear();
    }
  }

  /// فرض تشغيل جامع القمامة
  void _forceGarbageCollection() {
    // محاولة أكثر قوة لتشجيع GC
    for (int i = 0; i < 1000; i++) {
      final temp = List.filled(1000, 0);
      temp.clear();
    }
  }

  /// إعداد مستمع ضغط الذاكرة
  void _setupMemoryPressureListener() {
    // هذا مثال تقريبي - في التطبيق الحقيقي قد نحتاج لاستخدام
    // platform channels للحصول على إشعارات ضغط الذاكرة من النظام
  }

  /// إضافة مرجع ضعيف لكائن
  void addWeakReference(String key, Object object) {
    if (_weakReferences.length >= maxWeakReferences) {
      _cleanupWeakReferences();
    }
    
    _weakReferences[key] = WeakReference(object);
  }

  /// إزالة مرجع ضعيف
  void removeWeakReference(String key) {
    _weakReferences.remove(key);
  }

  /// الحصول على كائن من المرجع الضعيف
  T? getWeakReference<T extends Object>(String key) {
    final ref = _weakReferences[key];
    return ref?.target as T?;
  }

  /// الحصول على إحصائيات الذاكرة
  MemoryStatistics getStatistics() {
    final currentSnapshot = _snapshots.isNotEmpty ? _snapshots.last : null;
    
    return MemoryStatistics(
      currentUsageMB: currentSnapshot?.usedMemoryMB ?? 0,
      totalMemoryMB: currentSnapshot?.totalMemoryMB ?? 0,
      pressureLevel: _currentPressureLevel,
      cacheSize: CacheService().size,
      weakReferencesCount: _weakReferences.length,
      snapshotsCount: _snapshots.length,
      isMonitoring: _isMonitoring,
      averageUsageMB: _calculateAverageUsage(),
      peakUsageMB: _calculatePeakUsage(),
    );
  }

  /// حساب متوسط الاستخدام
  double _calculateAverageUsage() {
    if (_snapshots.isEmpty) return 0;
    
    final total = _snapshots.fold<double>(
      0, 
      (sum, snapshot) => sum + snapshot.usedMemoryMB,
    );
    
    return total / _snapshots.length;
  }

  /// حساب ذروة الاستخدام
  double _calculatePeakUsage() {
    if (_snapshots.isEmpty) return 0;
    
    return _snapshots.fold<double>(
      0, 
      (max, snapshot) => snapshot.usedMemoryMB > max ? snapshot.usedMemoryMB : max,
    );
  }

  /// الحصول على لقطات الذاكرة
  List<MemorySnapshot> get snapshots => List.unmodifiable(_snapshots);

  /// مستوى ضغط الذاكرة الحالي
  MemoryPressureLevel get currentPressureLevel => _currentPressureLevel;

  /// هل المراقبة نشطة؟
  bool get isMonitoring => _isMonitoring;
}

/// لقطة من حالة الذاكرة
class MemorySnapshot {
  final DateTime timestamp;
  final int usedMemory;
  final int totalMemory;
  final int gcCount;
  final int cacheSize;
  final int weakReferencesCount;

  const MemorySnapshot({
    required this.timestamp,
    required this.usedMemory,
    required this.totalMemory,
    required this.gcCount,
    required this.cacheSize,
    required this.weakReferencesCount,
  });

  /// استهلاك الذاكرة بالميجابايت
  double get usedMemoryMB => usedMemory / (1024 * 1024);

  /// إجمالي الذاكرة بالميجابايت
  double get totalMemoryMB => totalMemory / (1024 * 1024);

  /// نسبة استهلاك الذاكرة
  double get memoryUsageRatio => totalMemory > 0 ? usedMemory / totalMemory : 0;

  @override
  String toString() {
    return 'MemorySnapshot(${usedMemoryMB.toStringAsFixed(1)}MB/${totalMemoryMB.toStringAsFixed(1)}MB, ${(memoryUsageRatio * 100).toStringAsFixed(1)}%)';
  }
}

/// مستوى ضغط الذاكرة
enum MemoryPressureLevel {
  normal,
  warning,
  critical,
}

/// إحصائيات الذاكرة
class MemoryStatistics {
  final double currentUsageMB;
  final double totalMemoryMB;
  final MemoryPressureLevel pressureLevel;
  final int cacheSize;
  final int weakReferencesCount;
  final int snapshotsCount;
  final bool isMonitoring;
  final double averageUsageMB;
  final double peakUsageMB;

  const MemoryStatistics({
    required this.currentUsageMB,
    required this.totalMemoryMB,
    required this.pressureLevel,
    required this.cacheSize,
    required this.weakReferencesCount,
    required this.snapshotsCount,
    required this.isMonitoring,
    required this.averageUsageMB,
    required this.peakUsageMB,
  });

  /// نسبة الاستخدام الحالية
  double get currentUsageRatio => 
      totalMemoryMB > 0 ? currentUsageMB / totalMemoryMB : 0;

  @override
  String toString() {
    return 'MemoryStatistics(current: ${currentUsageMB.toStringAsFixed(1)}MB, peak: ${peakUsageMB.toStringAsFixed(1)}MB, pressure: ${pressureLevel.name})';
  }
}
