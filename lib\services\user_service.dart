import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../database/database_helper.dart';
import '../models/user.dart';
import '../models/permission.dart';
import '../constants/app_constants.dart';
import '../services/auth_service.dart';
import '../services/audit_service.dart';
import '../services/logging_service.dart';

/// خدمة إدارة المستخدمين
class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.rawQuery('''
        SELECT u.*, r.name as role_name 
        FROM ${AppConstants.usersTable} u
        LEFT JOIN ${AppConstants.rolesTable} r ON u.role_id = r.id
        ORDER BY u.full_name
      ''');

      return results.map((map) => User.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المستخدمين',
        category: 'UserService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.rawQuery('''
        SELECT u.*, r.name as role_name 
        FROM ${AppConstants.usersTable} u
        LEFT JOIN ${AppConstants.rolesTable} r ON u.role_id = r.id
        WHERE u.is_active = 1
        ORDER BY u.full_name
      ''');

      return results.map((map) => User.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المستخدمين النشطين',
        category: 'UserService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على مستخدم بالمعرف
  Future<User?> getUserById(int id) async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.rawQuery(
        '''
        SELECT u.*, r.name as role_name 
        FROM ${AppConstants.usersTable} u
        LEFT JOIN ${AppConstants.rolesTable} r ON u.role_id = r.id
        WHERE u.id = ?
      ''',
        [id],
      );

      if (results.isNotEmpty) {
        return User.fromMap(results.first);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المستخدم',
        category: 'UserService',
        data: {'id': id, 'error': e.toString()},
      );
    }
    return null;
  }

  /// الحصول على مستخدم باسم المستخدم
  Future<User?> getUserByUsername(String username) async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.rawQuery(
        '''
        SELECT u.*, r.name as role_name 
        FROM ${AppConstants.usersTable} u
        LEFT JOIN ${AppConstants.rolesTable} r ON u.role_id = r.id
        WHERE u.username = ?
      ''',
        [username],
      );

      if (results.isNotEmpty) {
        return User.fromMap(results.first);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المستخدم باسم المستخدم',
        category: 'UserService',
        data: {'username': username, 'error': e.toString()},
      );
    }
    return null;
  }

  /// إضافة مستخدم جديد
  Future<int> insertUser(User user) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageUsers);

      final db = await DatabaseHelper().database;

      // التحقق من عدم تكرار اسم المستخدم
      final existingUser = await getUserByUsername(user.username);
      if (existingUser != null) {
        throw Exception('اسم المستخدم موجود مسبقاً');
      }

      // التحقق من صحة البيانات
      if (!user.isValid()) {
        throw Exception('بيانات المستخدم غير صحيحة');
      }

      final userMap = user.toMap();
      userMap.remove('id'); // إزالة المعرف للإدراج

      final id = await db.insert(AppConstants.usersTable, userMap);

      // تسجيل العملية
      await AuditService.log(
        action: 'INSERT',
        entityType: 'users',
        entityId: id,
        entityName: user.username,
        description: 'إضافة مستخدم جديد: ${user.fullName}',
      );

      LoggingService.info(
        'تم إضافة مستخدم جديد: ${user.username}',
        category: 'UserService',
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة المستخدم',
        category: 'UserService',
        data: {'username': user.username, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث مستخدم
  Future<int> updateUser(User user) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageUsers);

      if (user.id == null) {
        throw Exception('معرف المستخدم مطلوب للتحديث');
      }

      final db = await DatabaseHelper().database;

      // الحصول على البيانات القديمة
      final oldUser = await getUserById(user.id!);
      if (oldUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      // التحقق من عدم تكرار اسم المستخدم (إذا تم تغييره)
      if (user.username != oldUser.username) {
        final existingUser = await getUserByUsername(user.username);
        if (existingUser != null) {
          throw Exception('اسم المستخدم موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      if (!user.isValid()) {
        throw Exception('بيانات المستخدم غير صحيحة');
      }

      final userMap = user.toMap();
      userMap['updated_at'] = DateTime.now().toIso8601String();

      final result = await db.update(
        AppConstants.usersTable,
        userMap,
        where: 'id = ?',
        whereArgs: [user.id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'users',
        entityId: user.id,
        entityName: user.username,
        description: 'تحديث بيانات المستخدم: ${user.fullName}',
      );

      LoggingService.info(
        'تم تحديث المستخدم: ${user.username}',
        category: 'UserService',
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المستخدم',
        category: 'UserService',
        data: {'id': user.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف مستخدم
  Future<int> deleteUser(int id) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageUsers);

      final db = await DatabaseHelper().database;

      // الحصول على بيانات المستخدم قبل الحذف
      final user = await getUserById(id);
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      // منع حذف المستخدم الحالي
      if (AuthService.currentUser?.id == id) {
        throw Exception('لا يمكن حذف المستخدم الحالي');
      }

      // منع حذف المدير الافتراضي
      if (user.username == AppConstants.defaultAdminUsername) {
        throw Exception('لا يمكن حذف المدير الافتراضي');
      }

      final result = await db.delete(
        AppConstants.usersTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'DELETE',
        entityType: 'users',
        entityId: id,
        entityName: user.username,
        description: 'حذف المستخدم: ${user.fullName}',
      );

      LoggingService.info(
        'تم حذف المستخدم: ${user.username}',
        category: 'UserService',
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف المستخدم',
        category: 'UserService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل مستخدم
  Future<bool> toggleUserStatus(int id) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageUsers);

      final user = await getUserById(id);
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      // منع إلغاء تفعيل المستخدم الحالي
      if (AuthService.currentUser?.id == id && user.isActive) {
        throw Exception('لا يمكن إلغاء تفعيل المستخدم الحالي');
      }

      final updatedUser = user.copyWith(
        isActive: !user.isActive,
        updatedAt: DateTime.now(),
      );

      await updateUser(updatedUser);
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة المستخدم',
        category: 'UserService',
        data: {'id': id, 'error': e.toString()},
      );
      return false;
    }
  }

  /// إعادة تعيين كلمة مرور المستخدم
  Future<bool> resetUserPassword(int id, String newPassword) async {
    try {
      // التحقق من الصلاحيات
      AuthService.requirePermission(PermissionType.manageUsers);

      final user = await getUserById(id);
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      // تشفير كلمة المرور الجديدة
      final passwordHash = _hashPassword(newPassword);

      final db = await DatabaseHelper().database;
      await db.update(
        AppConstants.usersTable,
        {
          'password_hash': passwordHash,
          'failed_login_attempts': 0,
          'locked_until': null,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'RESET_PASSWORD',
        entityType: 'users',
        entityId: id,
        entityName: user.username,
        description: 'إعادة تعيين كلمة مرور المستخدم: ${user.fullName}',
      );

      LoggingService.security(
        'تم إعادة تعيين كلمة مرور المستخدم: ${user.username}',
        category: 'UserService',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في إعادة تعيين كلمة المرور',
        category: 'UserService',
        data: {'id': id, 'error': e.toString()},
      );
      return false;
    }
  }

  /// البحث في المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      final db = await DatabaseHelper().database;
      final results = await db.rawQuery(
        '''
        SELECT u.*, r.name as role_name 
        FROM ${AppConstants.usersTable} u
        LEFT JOIN ${AppConstants.rolesTable} r ON u.role_id = r.id
        WHERE u.username LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?
        ORDER BY u.full_name
      ''',
        ['%$query%', '%$query%', '%$query%'],
      );

      return results.map((map) => User.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في المستخدمين',
        category: 'UserService',
        data: {'query': query, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات المستخدمين
  Future<Map<String, int>> getUserStatistics() async {
    try {
      final db = await DatabaseHelper().database;

      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.usersTable}',
      );

      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.usersTable} WHERE is_active = 1',
      );

      final adminResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.usersTable} WHERE is_admin = 1',
      );

      return {
        'total': totalResult.first['count'] as int,
        'active': activeResult.first['count'] as int,
        'admin': adminResult.first['count'] as int,
        'inactive':
            (totalResult.first['count'] as int) -
            (activeResult.first['count'] as int),
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات المستخدمين',
        category: 'UserService',
        data: {'error': e.toString()},
      );
      return {'total': 0, 'active': 0, 'admin': 0, 'inactive': 0};
    }
  }

  /// تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
