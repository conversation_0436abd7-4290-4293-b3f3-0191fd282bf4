import 'invoice_status.dart';
import 'payment.dart';

class Invoice {
  final int? id;
  final String invoiceNumber;
  final DateTime invoiceDate;
  final DateTime? dueDate; // تاريخ الاستحقاق
  final String type;
  final int? customerId;
  final int? supplierId;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final int currencyId;
  final InvoiceStatus status; // تغيير من String إلى InvoiceStatus
  final String? notes;
  final String? terms; // شروط الدفع
  final String? reference; // مرجع خارجي
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<InvoiceItem> items;
  final List<Payment> payments; // قائمة الدفعات

  Invoice({
    this.id,
    required this.invoiceNumber,
    required this.invoiceDate,
    this.dueDate,
    required this.type,
    this.customerId,
    this.supplierId,
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.totalAmount = 0.0,
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    required this.currencyId,
    this.status = InvoiceStatus.draft,
    this.notes,
    this.terms,
    this.reference,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.items = const [],
    this.payments = const [],
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'invoice_date': invoiceDate.toIso8601String().split('T')[0],
      'type': type,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'currency_id': currencyId,
      'due_date': dueDate?.toIso8601String(),
      'status': status.code,
      'terms': terms,
      'reference': reference,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id']?.toInt(),
      invoiceNumber: map['invoice_number'] ?? '',
      invoiceDate: DateTime.parse(
        map['invoice_date'] ?? DateTime.now().toIso8601String(),
      ),
      dueDate: map['due_date'] != null ? DateTime.parse(map['due_date']) : null,
      type: map['type'] ?? '',
      customerId: map['customer_id']?.toInt(),
      supplierId: map['supplier_id']?.toInt(),
      subtotal: map['subtotal']?.toDouble() ?? 0.0,
      taxAmount: map['tax_amount']?.toDouble() ?? 0.0,
      discountAmount: map['discount_amount']?.toDouble() ?? 0.0,
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      remainingAmount: map['remaining_amount']?.toDouble() ?? 0.0,
      currencyId: map['currency_id']?.toInt() ?? 1,
      status: InvoiceStatus.fromCode(map['status'] ?? 'draft'),
      notes: map['notes'],
      terms: map['terms'],
      reference: map['reference'],
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    DateTime? invoiceDate,
    DateTime? dueDate,
    String? type,
    int? customerId,
    int? supplierId,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    int? currencyId,
    InvoiceStatus? status,
    String? notes,
    String? terms,
    String? reference,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<InvoiceItem>? items,
    List<Payment>? payments,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      invoiceDate: invoiceDate ?? this.invoiceDate,
      dueDate: dueDate ?? this.dueDate,
      type: type ?? this.type,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      currencyId: currencyId ?? this.currencyId,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      terms: terms ?? this.terms,
      reference: reference ?? this.reference,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
      payments: payments ?? this.payments,
    );
  }

  String get typeArabic {
    switch (type) {
      case 'sale':
        return 'فاتورة مبيعات';
      case 'purchase':
        return 'فاتورة مشتريات';
      case 'sale_return':
        return 'مردود مبيعات';
      case 'purchase_return':
        return 'مردود مشتريات';
      default:
        return 'غير محدد';
    }
  }

  String get statusArabic {
    return status.displayName;
  }

  bool get isPaid {
    return (totalAmount - paidAmount).abs() < 0.01;
  }

  bool get isPartiallyPaid {
    return paidAmount > 0 && !isPaid;
  }

  String get displaySubtotal {
    return subtotal.toStringAsFixed(2);
  }

  String get displayTotal {
    return totalAmount.toStringAsFixed(2);
  }
}

class InvoiceItem {
  final int? id;
  final int invoiceId;
  final int itemId;
  final double quantity;
  final double unitPrice;
  final double totalPrice;
  final double discountPercentage;
  final double discountAmount;
  final double taxPercentage;
  final double taxAmount;
  final double netAmount;
  final DateTime createdAt;

  InvoiceItem({
    this.id,
    required this.invoiceId,
    required this.itemId,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    this.taxPercentage = 0.0,
    this.taxAmount = 0.0,
    required this.netAmount,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'item_id': itemId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'tax_percentage': taxPercentage,
      'tax_amount': taxAmount,
      'net_amount': netAmount,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id']?.toInt(),
      invoiceId: map['invoice_id']?.toInt() ?? 0,
      itemId: map['item_id']?.toInt() ?? 0,
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unitPrice: map['unit_price']?.toDouble() ?? 0.0,
      totalPrice: map['total_price']?.toDouble() ?? 0.0,
      discountPercentage: map['discount_percentage']?.toDouble() ?? 0.0,
      discountAmount: map['discount_amount']?.toDouble() ?? 0.0,
      taxPercentage: map['tax_percentage']?.toDouble() ?? 0.0,
      taxAmount: map['tax_amount']?.toDouble() ?? 0.0,
      netAmount: map['net_amount']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  InvoiceItem copyWith({
    int? id,
    int? invoiceId,
    int? itemId,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountPercentage,
    double? discountAmount,
    double? taxPercentage,
    double? taxAmount,
    double? netAmount,
    DateTime? createdAt,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      itemId: itemId ?? this.itemId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      taxPercentage: taxPercentage ?? this.taxPercentage,
      taxAmount: taxAmount ?? this.taxAmount,
      netAmount: netAmount ?? this.netAmount,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
