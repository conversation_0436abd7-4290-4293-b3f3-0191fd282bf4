/// نموذج سجل المراجعة
/// يمثل سجل واحد من عمليات المراجعة في النظام
class AuditLog {
  final int? id;
  final String action;
  final String entityType;
  final int? entityId;
  final String? entityName;
  final String? oldValues;
  final String? newValues;
  final String? userId;
  final String? userName;
  final String? ipAddress;
  final String? userAgent;
  final String? sessionId;
  final DateTime timestamp;
  final String? description;
  final String severity;
  final String? category;
  final String? referenceType;
  final int? referenceId;
  final DateTime createdAt;

  const AuditLog({
    this.id,
    required this.action,
    required this.entityType,
    this.entityId,
    this.entityName,
    this.oldValues,
    this.newValues,
    this.userId,
    this.userName,
    this.ipAddress,
    this.userAgent,
    this.sessionId,
    required this.timestamp,
    this.description,
    this.severity = 'INFO',
    this.category,
    this.referenceType,
    this.referenceId,
    required this.createdAt,
  });

  /// إنشاء نموذج من Map
  factory AuditLog.fromMap(Map<String, dynamic> map) {
    return AuditLog(
      id: map['id'] as int?,
      action: map['action'] as String,
      entityType: map['entity_type'] as String,
      entityId: map['entity_id'] as int?,
      entityName: map['entity_name'] as String?,
      oldValues: map['old_values'] as String?,
      newValues: map['new_values'] as String?,
      userId: map['user_id'] as String?,
      userName: map['user_name'] as String?,
      ipAddress: map['ip_address'] as String?,
      userAgent: map['user_agent'] as String?,
      sessionId: map['session_id'] as String?,
      timestamp: DateTime.parse(map['timestamp'] as String),
      description: map['description'] as String?,
      severity: map['severity'] as String? ?? 'INFO',
      category: map['category'] as String?,
      referenceType: map['reference_type'] as String?,
      referenceId: map['reference_id'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'action': action,
      'entity_type': entityType,
      'entity_id': entityId,
      'entity_name': entityName,
      'old_values': oldValues,
      'new_values': newValues,
      'user_id': userId,
      'user_name': userName,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'session_id': sessionId,
      'timestamp': timestamp.toIso8601String(),
      'description': description,
      'severity': severity,
      'category': category,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  AuditLog copyWith({
    int? id,
    String? action,
    String? entityType,
    int? entityId,
    String? entityName,
    String? oldValues,
    String? newValues,
    String? userId,
    String? userName,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    DateTime? timestamp,
    String? description,
    String? severity,
    String? category,
    String? referenceType,
    int? referenceId,
    DateTime? createdAt,
  }) {
    return AuditLog(
      id: id ?? this.id,
      action: action ?? this.action,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      entityName: entityName ?? this.entityName,
      oldValues: oldValues ?? this.oldValues,
      newValues: newValues ?? this.newValues,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      sessionId: sessionId ?? this.sessionId,
      timestamp: timestamp ?? this.timestamp,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      category: category ?? this.category,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'AuditLog{id: $id, action: $action, entityType: $entityType, '
        'entityId: $entityId, entityName: $entityName, userId: $userId, '
        'timestamp: $timestamp, severity: $severity}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuditLog &&
        other.id == id &&
        other.action == action &&
        other.entityType == entityType &&
        other.entityId == entityId &&
        other.entityName == entityName &&
        other.oldValues == oldValues &&
        other.newValues == newValues &&
        other.userId == userId &&
        other.userName == userName &&
        other.ipAddress == ipAddress &&
        other.userAgent == userAgent &&
        other.sessionId == sessionId &&
        other.timestamp == timestamp &&
        other.description == description &&
        other.severity == severity &&
        other.category == category &&
        other.referenceType == referenceType &&
        other.referenceId == referenceId &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      id,
      action,
      entityType,
      entityId,
      entityName,
      oldValues,
      newValues,
      userId,
      userName,
      ipAddress,
      userAgent,
      sessionId,
      timestamp,
      description,
      severity,
      category,
      referenceType,
      referenceId,
      createdAt,
    ]);
  }

  /// الحصول على وصف مقروء للعملية
  String get readableDescription {
    final actionMap = {
      'CREATE': 'إنشاء',
      'UPDATE': 'تحديث',
      'DELETE': 'حذف',
      'LOGIN': 'تسجيل دخول',
      'LOGOUT': 'تسجيل خروج',
      'PASSWORD_CHANGE': 'تغيير كلمة المرور',
      'INVOICE_POST': 'ترحيل فاتورة',
      'JOURNAL_POST': 'ترحيل قيد',
      'BACKUP': 'نسخ احتياطي',
      'RESTORE': 'استعادة',
      'EXPORT': 'تصدير',
      'IMPORT': 'استيراد',
    };

    final entityMap = {
      'ACCOUNT': 'حساب',
      'CUSTOMER': 'عميل',
      'SUPPLIER': 'مورد',
      'ITEM': 'صنف',
      'INVOICE': 'فاتورة',
      'JOURNAL_ENTRY': 'قيد محاسبي',
      'CURRENCY': 'عملة',
      'TAX': 'ضريبة',
      'SETTINGS': 'إعدادات',
      'USER': 'مستخدم',
      'SYSTEM': 'نظام',
    };

    final actionText = actionMap[action] ?? action;
    final entityText = entityMap[entityType] ?? entityType;
    final nameText = entityName != null ? ' ($entityName)' : '';

    return '$actionText $entityText$nameText';
  }

  /// الحصول على لون الشدة
  String get severityColor {
    switch (severity.toUpperCase()) {
      case 'ERROR':
        return '#F44336'; // أحمر
      case 'WARNING':
        return '#FF9800'; // برتقالي
      case 'SUCCESS':
        return '#4CAF50'; // أخضر
      case 'INFO':
      default:
        return '#2196F3'; // أزرق
    }
  }

  /// الحصول على أيقونة العملية
  String get actionIcon {
    switch (action.toUpperCase()) {
      case 'CREATE':
        return '➕';
      case 'UPDATE':
        return '✏️';
      case 'DELETE':
        return '🗑️';
      case 'LOGIN':
        return '🔓';
      case 'LOGOUT':
        return '🔒';
      case 'PASSWORD_CHANGE':
        return '🔑';
      case 'INVOICE_POST':
        return '📄';
      case 'JOURNAL_POST':
        return '📊';
      case 'BACKUP':
        return '💾';
      case 'RESTORE':
        return '🔄';
      case 'EXPORT':
        return '📤';
      case 'IMPORT':
        return '📥';
      default:
        return '📝';
    }
  }
}
