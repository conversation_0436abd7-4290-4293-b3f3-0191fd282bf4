import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// خدمة إدارة التركيز
/// تدير التنقل بين العناصر باستخدام لوحة المفاتيح وإدارة التركيز
class FocusManagementService {
  static final FocusManagementService _instance =
      FocusManagementService._internal();
  factory FocusManagementService() => _instance;
  FocusManagementService._internal();

  // ===============================
  // إدارة مجموعات التركيز
  // ===============================

  /// إنشاء مجموعة تركيز للنموذج
  static FocusNode createFormFocusGroup() {
    return FocusNode(debugLabel: 'FormFocusGroup');
  }

  /// إنشاء مجموعة تركيز للقائمة
  static FocusNode createListFocusGroup() {
    return FocusNode(debugLabel: 'ListFocusGroup');
  }

  /// إنشاء مجموعة تركيز للتنقل
  static FocusNode createNavigationFocusGroup() {
    return FocusNode(debugLabel: 'NavigationFocusGroup');
  }

  // ===============================
  // إدارة التركيز للحقول
  // ===============================

  /// إنشاء focus node للحقل مع تسمية
  static FocusNode createFieldFocusNode(String fieldName) {
    return FocusNode(debugLabel: 'Field_$fieldName');
  }

  /// الانتقال للحقل التالي
  static void moveToNextField(
    BuildContext context,
    FocusNode currentNode,
    FocusNode nextNode,
  ) {
    currentNode.unfocus();
    FocusScope.of(context).requestFocus(nextNode);
  }

  /// الانتقال للحقل السابق
  static void moveToPreviousField(
    BuildContext context,
    FocusNode currentNode,
    FocusNode previousNode,
  ) {
    currentNode.unfocus();
    FocusScope.of(context).requestFocus(previousNode);
  }

  /// إنهاء التركيز على جميع الحقول
  static void unfocusAll(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  // ===============================
  // إدارة التركيز للقوائم
  // ===============================

  /// إنشاء focus node لعنصر في القائمة
  static FocusNode createListItemFocusNode(String itemId) {
    return FocusNode(debugLabel: 'ListItem_$itemId');
  }

  /// الانتقال للعنصر التالي في القائمة
  static void moveToNextListItem(BuildContext context) {
    FocusScope.of(context).nextFocus();
  }

  /// الانتقال للعنصر السابق في القائمة
  static void moveToPreviousListItem(BuildContext context) {
    FocusScope.of(context).previousFocus();
  }

  /// الانتقال لأول عنصر في القائمة
  static void moveToFirstListItem(
    BuildContext context,
    FocusNode firstItemNode,
  ) {
    FocusScope.of(context).requestFocus(firstItemNode);
  }

  /// الانتقال لآخر عنصر في القائمة
  static void moveToLastListItem(BuildContext context, FocusNode lastItemNode) {
    FocusScope.of(context).requestFocus(lastItemNode);
  }

  // ===============================
  // إدارة اختصارات لوحة المفاتيح
  // ===============================

  /// إنشاء خريطة اختصارات أساسية
  static Map<LogicalKeySet, Intent> createBasicShortcuts() {
    return {
      LogicalKeySet(LogicalKeyboardKey.tab): const NextFocusIntent(),
      LogicalKeySet(LogicalKeyboardKey.tab, LogicalKeyboardKey.shift):
          const PreviousFocusIntent(),
      LogicalKeySet(LogicalKeyboardKey.escape): const DismissIntent(),
      LogicalKeySet(LogicalKeyboardKey.enter): const ActivateIntent(),
      LogicalKeySet(LogicalKeyboardKey.space): const ActivateIntent(),
    };
  }

  /// إنشاء خريطة اختصارات للنماذج
  static Map<LogicalKeySet, Intent> createFormShortcuts() {
    return {
      ...createBasicShortcuts(),
      LogicalKeySet(LogicalKeyboardKey.f2): const EditIntent(),
      LogicalKeySet(LogicalKeyboardKey.delete): const DeleteIntent(),
      LogicalKeySet(LogicalKeyboardKey.controlLeft, LogicalKeyboardKey.keyS):
          const SaveIntent(),
      LogicalKeySet(LogicalKeyboardKey.controlLeft, LogicalKeyboardKey.keyN):
          const AddIntent(),
    };
  }

  /// إنشاء خريطة اختصارات للقوائم
  static Map<LogicalKeySet, Intent> createListShortcuts() {
    return {
      ...createBasicShortcuts(),
      LogicalKeySet(LogicalKeyboardKey.arrowUp): const PreviousFocusIntent(),
      LogicalKeySet(LogicalKeyboardKey.arrowDown): const NextFocusIntent(),
      LogicalKeySet(LogicalKeyboardKey.home): const FirstFocusIntent(),
      LogicalKeySet(LogicalKeyboardKey.end): const LastFocusIntent(),
      LogicalKeySet(LogicalKeyboardKey.controlLeft, LogicalKeyboardKey.keyF):
          const SearchIntent(),
    };
  }

  // ===============================
  // إنشاء Actions مخصصة
  // ===============================

  /// إنشاء خريطة Actions أساسية
  static Map<Type, Action<Intent>> createBasicActions(BuildContext context) {
    return {
      NextFocusIntent: CallbackAction<NextFocusIntent>(
        onInvoke: (intent) {
          FocusScope.of(context).nextFocus();
          return null;
        },
      ),
      PreviousFocusIntent: CallbackAction<PreviousFocusIntent>(
        onInvoke: (intent) {
          FocusScope.of(context).previousFocus();
          return null;
        },
      ),
      DismissIntent: CallbackAction<DismissIntent>(
        onInvoke: (intent) {
          Navigator.of(context).maybePop();
          return null;
        },
      ),
    };
  }

  /// إنشاء خريطة Actions للنماذج
  static Map<Type, Action<Intent>> createFormActions(
    BuildContext context, {
    VoidCallback? onSave,
    VoidCallback? onAdd,
    VoidCallback? onEdit,
    VoidCallback? onDelete,
  }) {
    return {
      ...createBasicActions(context),
      SaveIntent: CallbackAction<SaveIntent>(
        onInvoke: (intent) {
          onSave?.call();
          return null;
        },
      ),
      AddIntent: CallbackAction<AddIntent>(
        onInvoke: (intent) {
          onAdd?.call();
          return null;
        },
      ),
      EditIntent: CallbackAction<EditIntent>(
        onInvoke: (intent) {
          onEdit?.call();
          return null;
        },
      ),
      DeleteIntent: CallbackAction<DeleteIntent>(
        onInvoke: (intent) {
          onDelete?.call();
          return null;
        },
      ),
    };
  }

  // ===============================
  // دوال مساعدة للتركيز
  // ===============================

  /// التحقق من إمكانية التركيز على العنصر
  static bool canFocus(FocusNode node) {
    return node.canRequestFocus;
  }

  /// التحقق من وجود تركيز على العنصر
  static bool hasFocus(FocusNode node) {
    return node.hasFocus;
  }

  /// إنشاء FocusableActionDetector مع إعدادات مناسبة
  static Widget createFocusableWidget({
    required Widget child,
    required FocusNode focusNode,
    Map<LogicalKeySet, Intent>? shortcuts,
    Map<Type, Action<Intent>>? actions,
    ValueChanged<bool>? onFocusChange,
    bool autofocus = false,
  }) {
    return FocusableActionDetector(
      focusNode: focusNode,
      autofocus: autofocus,
      shortcuts: shortcuts ?? createBasicShortcuts(),
      actions: actions ?? {},
      onFocusChange: onFocusChange,
      child: child,
    );
  }

  /// إنشاء Focus widget مع إعدادات مناسبة
  static Widget createFocusWidget({
    required Widget child,
    required FocusNode focusNode,
    bool autofocus = false,
    ValueChanged<bool>? onFocusChange,
  }) {
    return Focus(
      focusNode: focusNode,
      autofocus: autofocus,
      onFocusChange: onFocusChange,
      child: child,
    );
  }

  // ===============================
  // إدارة ترتيب التركيز
  // ===============================

  /// إنشاء FocusTraversalGroup مع ترتيب مخصص
  static Widget createTraversalGroup({
    required Widget child,
    required FocusTraversalPolicy policy,
  }) {
    return FocusTraversalGroup(policy: policy, child: child);
  }

  /// إنشاء ترتيب تركيز للنموذج
  static FocusTraversalPolicy createFormTraversalPolicy() {
    return OrderedTraversalPolicy();
  }

  /// إنشاء ترتيب تركيز للقائمة
  static FocusTraversalPolicy createListTraversalPolicy() {
    return ReadingOrderTraversalPolicy();
  }

  // ===============================
  // دوال التنظيف والإدارة
  // ===============================

  /// تنظيف focus nodes
  static void disposeFocusNodes(List<FocusNode> nodes) {
    for (final node in nodes) {
      node.dispose();
    }
  }

  /// إنشاء مدير تركيز للشاشة
  static FocusManager createScreenFocusManager() {
    return FocusManager();
  }
}

// ===============================
// Intent Classes مخصصة
// ===============================

class SaveIntent extends Intent {
  const SaveIntent();
}

class AddIntent extends Intent {
  const AddIntent();
}

class EditIntent extends Intent {
  const EditIntent();
}

class DeleteIntent extends Intent {
  const DeleteIntent();
}

class SearchIntent extends Intent {
  const SearchIntent();
}

class FirstFocusIntent extends Intent {
  const FirstFocusIntent();
}

class LastFocusIntent extends Intent {
  const LastFocusIntent();
}
