/// خدمة ربط الفواتير بالقيود المحاسبية
/// تقوم بإنشاء القيود المحاسبية تلقائياً عند حفظ الفواتير
/// وتحديث أرصدة العملاء والموردين والحسابات المختلفة
library;

import '../database/database_helper.dart';
import '../models/invoice.dart';
import '../models/journal_entry.dart';
import '../models/account.dart';
import '../services/journal_entry_service.dart';
import '../services/account_service.dart';
import '../services/customer_service.dart';
import '../services/supplier_service.dart';
import '../services/settings_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';
import '../exceptions/business_rule_exception.dart' as business_rules;
import '../exceptions/validation_exception.dart' as validation;

class InvoiceAccountingIntegrationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();
  final SettingsService _settingsService = SettingsService();

  /// إنشاء قيد محاسبي من فاتورة
  Future<int> createJournalEntryFromInvoice(Invoice invoice) async {
    try {
      // التحقق من صحة الفاتورة
      _validateInvoiceForAccounting(invoice);

      // إنشاء القيد حسب نوع الفاتورة
      JournalEntry journalEntry;
      switch (invoice.type) {
        case AppConstants.invoiceTypeSale:
          journalEntry = await _createSalesJournalEntry(invoice);
          break;
        case AppConstants.invoiceTypePurchase:
          journalEntry = await _createPurchaseJournalEntry(invoice);
          break;
        case AppConstants.invoiceTypeSaleReturn:
          journalEntry = await _createSalesReturnJournalEntry(invoice);
          break;
        case AppConstants.invoiceTypePurchaseReturn:
          journalEntry = await _createPurchaseReturnJournalEntry(invoice);
          break;
        default:
          throw business_rules.BusinessRuleException(
            'نوع فاتورة غير مدعوم: ${invoice.type}',
          );
      }

      // حفظ القيد في قاعدة البيانات
      final journalEntryId = await _journalEntryService.insertJournalEntry(
        journalEntry,
      );

      // ترحيل القيد تلقائياً إذا كان مفعلاً في الإعدادات
      final autoPost = await _settingsService.getSetting(
        'auto_post_journal_entries',
      );
      if (autoPost == 'true') {
        await _journalEntryService.postJournalEntry(journalEntryId);
      }

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: 'journal_entry',
        entityId: journalEntryId,
        entityName: journalEntry.entryNumber,
        newValues: journalEntry.toMap(),
        description: 'إنشاء قيد محاسبي من فاتورة ${invoice.invoiceNumber}',
        category: 'Accounting Integration',
      );

      LoggingService.info(
        'تم إنشاء قيد محاسبي من فاتورة بنجاح',
        category: 'InvoiceAccountingIntegration',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'journalEntryId': journalEntryId,
          'entryNumber': journalEntry.entryNumber,
        },
      );

      return journalEntryId;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد محاسبي من فاتورة',
        category: 'InvoiceAccountingIntegration',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إنشاء قيد مبيعات
  Future<JournalEntry> _createSalesJournalEntry(Invoice invoice) async {
    final entryNumber = await _journalEntryService.generateEntryNumber();
    final details = <JournalEntryDetail>[];

    // الحصول على الحسابات المطلوبة
    final salesAccount = await _getAccountByType(AppConstants.accountTypeSale);
    final customerAccount = await _getCustomerAccount(invoice.customerId!);
    final taxAccount = await _getTaxAccount();

    // قيد العميل (مدين)
    details.add(
      JournalEntryDetail(
        journalEntryId: 0, // سيتم تحديثه لاحقاً
        accountId: customerAccount.id!,
        debitAmount: invoice.totalAmount,
        creditAmount: 0.0,
        description: 'فاتورة مبيعات رقم ${invoice.invoiceNumber}',
      ),
    );

    // قيد المبيعات (دائن)
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: salesAccount.id!,
        debitAmount: 0.0,
        creditAmount: invoice.subtotal,
        description: 'مبيعات فاتورة رقم ${invoice.invoiceNumber}',
      ),
    );

    // قيد الضريبة إذا وجدت
    if (invoice.taxAmount > 0 && taxAccount != null) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: taxAccount.id!,
          debitAmount: 0.0,
          creditAmount: invoice.taxAmount,
          description: 'ضريبة فاتورة مبيعات رقم ${invoice.invoiceNumber}',
        ),
      );
    }

    return JournalEntry(
      entryNumber: entryNumber,
      entryDate: invoice.invoiceDate,
      description: 'قيد مبيعات - فاتورة رقم ${invoice.invoiceNumber}',
      type: AppConstants.entryTypeSale,
      totalDebit: invoice.totalAmount,
      totalCredit: invoice.totalAmount,
      currencyId: invoice.currencyId,
      referenceType: 'invoice',
      referenceId: invoice.id,
      details: details,
    );
  }

  /// إنشاء قيد مشتريات
  Future<JournalEntry> _createPurchaseJournalEntry(Invoice invoice) async {
    final entryNumber = await _journalEntryService.generateEntryNumber();
    final details = <JournalEntryDetail>[];

    // الحصول على الحسابات المطلوبة
    final purchaseAccount = await _getAccountByType(
      AppConstants.accountTypePurchase,
    );
    final supplierAccount = await _getSupplierAccount(invoice.supplierId!);
    final taxAccount = await _getTaxAccount();

    // قيد المشتريات (مدين)
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: purchaseAccount.id!,
        debitAmount: invoice.subtotal,
        creditAmount: 0.0,
        description: 'مشتريات فاتورة رقم ${invoice.invoiceNumber}',
      ),
    );

    // قيد الضريبة إذا وجدت (مدين)
    if (invoice.taxAmount > 0 && taxAccount != null) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: taxAccount.id!,
          debitAmount: invoice.taxAmount,
          creditAmount: 0.0,
          description: 'ضريبة فاتورة مشتريات رقم ${invoice.invoiceNumber}',
        ),
      );
    }

    // قيد المورد (دائن)
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: supplierAccount.id!,
        debitAmount: 0.0,
        creditAmount: invoice.totalAmount,
        description: 'فاتورة مشتريات رقم ${invoice.invoiceNumber}',
      ),
    );

    return JournalEntry(
      entryNumber: entryNumber,
      entryDate: invoice.invoiceDate,
      description: 'قيد مشتريات - فاتورة رقم ${invoice.invoiceNumber}',
      type: AppConstants.entryTypePurchase,
      totalDebit: invoice.totalAmount,
      totalCredit: invoice.totalAmount,
      currencyId: invoice.currencyId,
      referenceType: 'invoice',
      referenceId: invoice.id,
      details: details,
    );
  }

  /// إنشاء قيد مردود مبيعات
  Future<JournalEntry> _createSalesReturnJournalEntry(Invoice invoice) async {
    final entryNumber = await _journalEntryService.generateEntryNumber();
    final details = <JournalEntryDetail>[];

    // الحصول على الحسابات المطلوبة
    final salesAccount = await _getAccountByType(AppConstants.accountTypeSale);
    final customerAccount = await _getCustomerAccount(invoice.customerId!);
    final taxAccount = await _getTaxAccount();

    // قيد المبيعات (مدين) - عكس المبيعات العادية
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: salesAccount.id!,
        debitAmount: invoice.subtotal,
        creditAmount: 0.0,
        description: 'مردود مبيعات فاتورة رقم ${invoice.invoiceNumber}',
      ),
    );

    // قيد الضريبة إذا وجدت (مدين)
    if (invoice.taxAmount > 0 && taxAccount != null) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: taxAccount.id!,
          debitAmount: invoice.taxAmount,
          creditAmount: 0.0,
          description: 'ضريبة مردود مبيعات فاتورة رقم ${invoice.invoiceNumber}',
        ),
      );
    }

    // قيد العميل (دائن)
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: customerAccount.id!,
        debitAmount: 0.0,
        creditAmount: invoice.totalAmount,
        description: 'مردود مبيعات فاتورة رقم ${invoice.invoiceNumber}',
      ),
    );

    return JournalEntry(
      entryNumber: entryNumber,
      entryDate: invoice.invoiceDate,
      description: 'قيد مردود مبيعات - فاتورة رقم ${invoice.invoiceNumber}',
      type: AppConstants.entryTypeSale,
      totalDebit: invoice.totalAmount,
      totalCredit: invoice.totalAmount,
      currencyId: invoice.currencyId,
      referenceType: 'invoice',
      referenceId: invoice.id,
      details: details,
    );
  }

  /// إنشاء قيد مردود مشتريات
  Future<JournalEntry> _createPurchaseReturnJournalEntry(
    Invoice invoice,
  ) async {
    final entryNumber = await _journalEntryService.generateEntryNumber();
    final details = <JournalEntryDetail>[];

    // الحصول على الحسابات المطلوبة
    final purchaseAccount = await _getAccountByType(
      AppConstants.accountTypePurchase,
    );
    final supplierAccount = await _getSupplierAccount(invoice.supplierId!);
    final taxAccount = await _getTaxAccount();

    // قيد المورد (مدين)
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: supplierAccount.id!,
        debitAmount: invoice.totalAmount,
        creditAmount: 0.0,
        description: 'مردود مشتريات فاتورة رقم ${invoice.invoiceNumber}',
      ),
    );

    // قيد المشتريات (دائن) - عكس المشتريات العادية
    details.add(
      JournalEntryDetail(
        journalEntryId: 0,
        accountId: purchaseAccount.id!,
        debitAmount: 0.0,
        creditAmount: invoice.subtotal,
        description: 'مردود مشتريات فاتورة رقم ${invoice.invoiceNumber}',
      ),
    );

    // قيد الضريبة إذا وجدت (دائن)
    if (invoice.taxAmount > 0 && taxAccount != null) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0,
          accountId: taxAccount.id!,
          debitAmount: 0.0,
          creditAmount: invoice.taxAmount,
          description:
              'ضريبة مردود مشتريات فاتورة رقم ${invoice.invoiceNumber}',
        ),
      );
    }

    return JournalEntry(
      entryNumber: entryNumber,
      entryDate: invoice.invoiceDate,
      description: 'قيد مردود مشتريات - فاتورة رقم ${invoice.invoiceNumber}',
      type: AppConstants.entryTypePurchase,
      totalDebit: invoice.totalAmount,
      totalCredit: invoice.totalAmount,
      currencyId: invoice.currencyId,
      referenceType: 'invoice',
      referenceId: invoice.id,
      details: details,
    );
  }

  // ===============================
  // الطرق المساعدة
  // ===============================

  /// التحقق من صحة الفاتورة للمحاسبة
  void _validateInvoiceForAccounting(Invoice invoice) {
    if (invoice.id == null) {
      throw validation.ValidationException('معرف الفاتورة مطلوب');
    }

    if (invoice.totalAmount <= 0) {
      throw validation.ValidationException(
        'إجمالي الفاتورة يجب أن يكون أكبر من الصفر',
      );
    }

    if (invoice.type == AppConstants.invoiceTypeSale ||
        invoice.type == AppConstants.invoiceTypeSaleReturn) {
      if (invoice.customerId == null) {
        throw validation.ValidationException(
          'معرف العميل مطلوب لفواتير المبيعات',
        );
      }
    }

    if (invoice.type == AppConstants.invoiceTypePurchase ||
        invoice.type == AppConstants.invoiceTypePurchaseReturn) {
      if (invoice.supplierId == null) {
        throw validation.ValidationException(
          'معرف المورد مطلوب لفواتير المشتريات',
        );
      }
    }

    if (invoice.items.isEmpty) {
      throw validation.ValidationException(
        'الفاتورة يجب أن تحتوي على عنصر واحد على الأقل',
      );
    }
  }

  /// الحصول على حساب حسب النوع
  Future<Account> _getAccountByType(String accountType) async {
    final accounts = await _accountService.getAccountsByType(accountType);
    if (accounts.isEmpty) {
      throw business_rules.BusinessRuleException(
        'لا يوجد حساب من نوع: $accountType',
      );
    }

    // إرجاع أول حساب من النوع المطلوب
    // يمكن تحسين هذا لاحقاً لاختيار حساب افتراضي محدد
    return accounts.first;
  }

  /// الحصول على حساب العميل
  Future<Account> _getCustomerAccount(int customerId) async {
    final customer = await _customerService.getCustomerById(customerId);
    if (customer == null) {
      throw business_rules.BusinessRuleException(
        'العميل غير موجود: $customerId',
      );
    }

    // البحث عن حساب العميل أو إنشاؤه
    Account? customerAccount = await _accountService.getAccountByCode(
      'C${customer.code}',
    );

    if (customerAccount == null) {
      // إنشاء حساب جديد للعميل
      customerAccount = Account(
        code: 'C${customer.code}',
        name: 'العميل - ${customer.name}',
        type: AppConstants.accountTypeAsset, // حساب العملاء من الأصول
        currencyId: 1, // العملة الافتراضية
        description: 'حساب العميل ${customer.name}',
      );

      final accountId = await _accountService.insertAccount(customerAccount);
      customerAccount = customerAccount.copyWith(id: accountId);
    }

    return customerAccount;
  }

  /// الحصول على حساب المورد
  Future<Account> _getSupplierAccount(int supplierId) async {
    final supplier = await _supplierService.getSupplierById(supplierId);
    if (supplier == null) {
      throw business_rules.BusinessRuleException(
        'المورد غير موجود: $supplierId',
      );
    }

    // البحث عن حساب المورد أو إنشاؤه
    Account? supplierAccount = await _accountService.getAccountByCode(
      'S${supplier.code}',
    );

    if (supplierAccount == null) {
      // إنشاء حساب جديد للمورد
      supplierAccount = Account(
        code: 'S${supplier.code}',
        name: 'المورد - ${supplier.name}',
        type: AppConstants.accountTypeLiability, // حساب الموردين من الخصوم
        currencyId: 1, // العملة الافتراضية
        description: 'حساب المورد ${supplier.name}',
      );

      final accountId = await _accountService.insertAccount(supplierAccount);
      supplierAccount = supplierAccount.copyWith(id: accountId);
    }

    return supplierAccount;
  }

  /// الحصول على حساب الضريبة
  Future<Account?> _getTaxAccount() async {
    try {
      // البحث عن حساب الضريبة الافتراضي
      final taxAccounts = await _accountService.searchAccounts('ضريبة');
      if (taxAccounts.isNotEmpty) {
        return taxAccounts.first;
      }

      // إنشاء حساب ضريبة افتراضي إذا لم يوجد
      final taxAccount = Account(
        code: 'TAX001',
        name: 'ضريبة القيمة المضافة',
        type: AppConstants.accountTypeLiability,
        currencyId: 1,
        description: 'حساب ضريبة القيمة المضافة',
      );

      final accountId = await _accountService.insertAccount(taxAccount);
      return taxAccount.copyWith(id: accountId);
    } catch (e) {
      LoggingService.warning(
        'تعذر الحصول على حساب الضريبة',
        category: 'InvoiceAccountingIntegration',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// حذف القيد المحاسبي المرتبط بالفاتورة
  Future<bool> deleteJournalEntryForInvoice(int invoiceId) async {
    try {
      final journalEntries = await _getJournalEntriesByReference(
        'invoice',
        invoiceId,
      );

      for (final entry in journalEntries) {
        if (entry.isPosted) {
          throw business_rules.BusinessRuleException(
            'لا يمكن حذف قيد مرحل. يجب إلغاء ترحيل القيد أولاً',
          );
        }
        await _journalEntryService.deleteJournalEntry(entry.id!);
      }

      LoggingService.info(
        'تم حذف القيود المحاسبية للفاتورة',
        category: 'InvoiceAccountingIntegration',
        data: {'invoiceId': invoiceId, 'deletedEntries': journalEntries.length},
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف القيود المحاسبية للفاتورة',
        category: 'InvoiceAccountingIntegration',
        data: {'invoiceId': invoiceId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على القيود المحاسبية المرتبطة بالفاتورة
  Future<List<JournalEntry>> getJournalEntriesForInvoice(int invoiceId) async {
    return await _getJournalEntriesByReference('invoice', invoiceId);
  }

  /// البحث عن القيود المحاسبية بالمرجع
  Future<List<JournalEntry>> _getJournalEntriesByReference(
    String referenceType,
    int referenceId,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.journalEntriesTable,
      where: 'reference_type = ? AND reference_id = ?',
      whereArgs: [referenceType, referenceId],
      orderBy: 'entry_date DESC, id DESC',
    );

    List<JournalEntry> entries = [];
    for (final map in result) {
      final entry = JournalEntry.fromMap(map);
      final details = await _getJournalEntryDetails(entry.id!);
      entries.add(entry.copyWith(details: details));
    }

    return entries;
  }

  /// الحصول على تفاصيل القيد المحاسبي
  Future<List<JournalEntryDetail>> _getJournalEntryDetails(
    int journalEntryId,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.journalEntryDetailsTable,
      where: 'journal_entry_id = ?',
      whereArgs: [journalEntryId],
      orderBy: 'id ASC',
    );

    return result.map((map) => JournalEntryDetail.fromMap(map)).toList();
  }

  /// التحقق من وجود قيد محاسبي للفاتورة
  Future<bool> hasJournalEntryForInvoice(int invoiceId) async {
    final entries = await getJournalEntriesForInvoice(invoiceId);
    return entries.isNotEmpty;
  }
}
