import 'package:flutter/material.dart';
import '../models/permission.dart';
import '../services/auth_service.dart';

/// حارس الصلاحيات - يوفر طرق مختلفة للتحقق من الصلاحيات
class PermissionGuard {
  /// التحقق من صلاحية واحدة
  static bool check(PermissionType permission) {
    return AuthService.hasPermission(permission);
  }

  /// التحقق من صلاحيات متعددة (يجب أن تكون جميعها متوفرة)
  static bool checkAll(List<PermissionType> permissions) {
    return AuthService.hasAllPermissions(permissions);
  }

  /// التحقق من صلاحيات متعددة (يكفي وجود واحدة منها)
  static bool checkAny(List<PermissionType> permissions) {
    return AuthService.hasAnyPermission(permissions);
  }

  /// فرض الصلاحية (يرمي استثناء إذا لم تكن متوفرة)
  static void require(PermissionType permission) {
    AuthService.requirePermission(permission);
  }

  /// فرض صلاحيات متعددة (يجب أن تكون جميعها متوفرة)
  static void requireAll(List<PermissionType> permissions) {
    for (final permission in permissions) {
      AuthService.requirePermission(permission);
    }
  }

  /// التحقق من كون المستخدم مدير
  static bool isAdmin() {
    return AuthService.currentUser?.isAdmin ?? false;
  }

  /// التحقق من تسجيل الدخول
  static bool isLoggedIn() {
    return AuthService.isLoggedIn;
  }

  /// التحقق من انتهاء صلاحية الجلسة
  static bool isSessionExpired() {
    return AuthService.isSessionExpired;
  }
}

/// Widget للتحكم في عرض المحتوى حسب الصلاحيات
class PermissionWidget extends StatelessWidget {
  final PermissionType? permission;
  final List<PermissionType>? permissions;
  final bool requireAll;
  final Widget child;
  final Widget? fallback;
  final bool adminOverride;

  const PermissionWidget({
    super.key,
    this.permission,
    this.permissions,
    this.requireAll = true,
    required this.child,
    this.fallback,
    this.adminOverride = true,
  }) : assert(permission != null || permissions != null,
         'يجب تحديد صلاحية واحدة على الأقل');

  @override
  Widget build(BuildContext context) {
    bool hasPermission = false;

    // المدير له جميع الصلاحيات (إذا كان adminOverride مفعل)
    if (adminOverride && PermissionGuard.isAdmin()) {
      hasPermission = true;
    } else if (permission != null) {
      // التحقق من صلاحية واحدة
      hasPermission = PermissionGuard.check(permission!);
    } else if (permissions != null) {
      // التحقق من صلاحيات متعددة
      if (requireAll) {
        hasPermission = PermissionGuard.checkAll(permissions!);
      } else {
        hasPermission = PermissionGuard.checkAny(permissions!);
      }
    }

    if (hasPermission) {
      return child;
    } else {
      return fallback ?? const SizedBox.shrink();
    }
  }
}

/// Widget لعرض رسالة عدم وجود صلاحية
class NoPermissionWidget extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final VoidCallback? onRetry;

  const NoPermissionWidget({
    super.key,
    this.message,
    this.icon,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.lock_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message ?? 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Mixin للشاشات التي تحتاج للتحقق من الصلاحيات
mixin PermissionMixin<T extends StatefulWidget> on State<T> {
  /// الصلاحيات المطلوبة للشاشة
  List<PermissionType> get requiredPermissions;

  /// هل يجب أن تكون جميع الصلاحيات متوفرة؟
  bool get requireAllPermissions => true;

  /// هل المدير يتجاوز فحص الصلاحيات؟
  bool get adminOverride => true;

  /// التحقق من الصلاحيات
  bool checkPermissions() {
    if (adminOverride && PermissionGuard.isAdmin()) {
      return true;
    }

    if (requireAllPermissions) {
      return PermissionGuard.checkAll(requiredPermissions);
    } else {
      return PermissionGuard.checkAny(requiredPermissions);
    }
  }

  /// عرض رسالة عدم وجود صلاحية
  Widget buildNoPermissionWidget() {
    return const NoPermissionWidget();
  }

  /// التحقق من الصلاحيات قبل تنفيذ عملية
  bool checkPermissionForAction(PermissionType permission) {
    if (adminOverride && PermissionGuard.isAdmin()) {
      return true;
    }
    return PermissionGuard.check(permission);
  }

  /// عرض رسالة خطأ عدم وجود صلاحية
  void showNoPermissionDialog(BuildContext context, [String? message]) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('عدم وجود صلاحية'),
          ],
        ),
        content: Text(
          message ?? 'ليس لديك صلاحية لتنفيذ هذه العملية',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض SnackBar لعدم وجود صلاحية
  void showNoPermissionSnackBar(BuildContext context, [String? message]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.lock_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message ?? 'ليس لديك صلاحية لتنفيذ هذه العملية',
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// Decorator للدوال التي تحتاج للتحقق من الصلاحيات
class PermissionDecorator {
  /// تنفيذ دالة مع التحقق من الصلاحية
  static T? executeWithPermission<T>(
    PermissionType permission,
    T Function() function, {
    T? fallback,
    bool adminOverride = true,
  }) {
    if (adminOverride && PermissionGuard.isAdmin()) {
      return function();
    }

    if (PermissionGuard.check(permission)) {
      return function();
    }

    return fallback;
  }

  /// تنفيذ دالة async مع التحقق من الصلاحية
  static Future<T?> executeAsyncWithPermission<T>(
    PermissionType permission,
    Future<T> Function() function, {
    T? fallback,
    bool adminOverride = true,
  }) async {
    if (adminOverride && PermissionGuard.isAdmin()) {
      return await function();
    }

    if (PermissionGuard.check(permission)) {
      return await function();
    }

    return fallback;
  }

  /// تنفيذ دالة مع التحقق من صلاحيات متعددة
  static T? executeWithPermissions<T>(
    List<PermissionType> permissions,
    T Function() function, {
    T? fallback,
    bool requireAll = true,
    bool adminOverride = true,
  }) {
    if (adminOverride && PermissionGuard.isAdmin()) {
      return function();
    }

    bool hasPermission = requireAll
        ? PermissionGuard.checkAll(permissions)
        : PermissionGuard.checkAny(permissions);

    if (hasPermission) {
      return function();
    }

    return fallback;
  }
}
