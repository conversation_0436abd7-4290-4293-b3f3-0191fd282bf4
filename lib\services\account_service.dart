import '../database/database_helper.dart';
import '../models/account.dart';
import '../constants/app_constants.dart';
import 'validation_service.dart';
import '../exceptions/validation_exception.dart';
import 'data_sanitization_service.dart';
import 'audit_service.dart';
import 'pagination_service.dart';
import 'cache_service.dart';
import 'performance_service.dart';

class AccountService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<Account>> getAllAccounts() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  Future<List<Account>> getAccountsByType(String type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'type = ? AND is_active = 1',
      whereArgs: [type],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  Future<List<Account>> getActiveAccounts() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'is_active = 1',
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  Future<List<Account>> getParentAccounts() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'parent_id IS NULL AND is_active = 1',
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  Future<List<Account>> getChildAccounts(int parentId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'parent_id = ? AND is_active = 1',
      whereArgs: [parentId],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  Future<Account?> getAccountById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  Future<Account?> getAccountByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  Future<int> insertAccount(Account account) async {
    final db = await _databaseHelper.database;

    // التحقق من صحة البيانات باستخدام ValidationService
    final validation = ValidationService.validateAccount(
      code: account.code,
      name: account.name,
      type: account.type,
      balance: account.balance,
    );

    if (!validation.isValid) {
      throw ValidationException(validation.errorMessage!);
    }

    // التحقق من عدم تكرار الكود
    final existingAccount = await getAccountByCode(account.code);
    if (existingAccount != null) {
      throw ValidationException.duplicate('كود الحساب', account.code);
    }

    // تنظيف البيانات باستخدام DataSanitizationService
    final sanitizedData = DataSanitizationService.sanitizeAccountData(
      code: account.code,
      name: account.name,
      description: account.description,
    );

    final cleanAccount = account.copyWith(
      code: sanitizedData['code']!,
      name: sanitizedData['name']!,
      description: sanitizedData['description']!.isEmpty
          ? null
          : sanitizedData['description']!,
    );

    final accountData = cleanAccount.toMap();
    accountData.remove('id'); // إزالة المعرف للإدراج التلقائي

    final accountId = await db.insert(AppConstants.accountsTable, accountData);

    // تسجيل عملية الإنشاء في سجل المراجعة
    await AuditService.logCreate(
      entityType: AppConstants.auditEntityAccount,
      entityId: accountId,
      entityName: cleanAccount.name,
      newValues: cleanAccount.toMap(),
      description:
          'تم إنشاء حساب جديد: ${cleanAccount.name} (${cleanAccount.code})',
      category: 'Accounting',
    );

    return accountId;
  }

  Future<int> updateAccount(Account account) async {
    final db = await _databaseHelper.database;

    // الحصول على البيانات القديمة للمراجعة
    final oldAccount = await getAccountById(account.id!);
    if (oldAccount == null) {
      throw Exception('الحساب غير موجود');
    }

    // التحقق من صحة البيانات باستخدام ValidationService
    final validation = ValidationService.validateAccount(
      code: account.code,
      name: account.name,
      type: account.type,
      balance: account.balance,
    );

    if (!validation.isValid) {
      throw ValidationException(validation.errorMessage!);
    }

    // التحقق من عدم تكرار الكود مع حسابات أخرى
    final existingAccount = await getAccountByCode(account.code);
    if (existingAccount != null && existingAccount.id != account.id) {
      throw ValidationException.duplicate('كود الحساب', account.code);
    }

    // تنظيف البيانات باستخدام DataSanitizationService
    final sanitizedData = DataSanitizationService.sanitizeAccountData(
      code: account.code,
      name: account.name,
      description: account.description,
    );

    final cleanAccount = account.copyWith(
      code: sanitizedData['code']!,
      name: sanitizedData['name']!,
      description: sanitizedData['description']!.isEmpty
          ? null
          : sanitizedData['description']!,
      updatedAt: DateTime.now(),
    );

    final accountData = cleanAccount.toMap();

    final result = await db.update(
      AppConstants.accountsTable,
      accountData,
      where: 'id = ?',
      whereArgs: [account.id],
    );

    // تسجيل عملية التحديث في سجل المراجعة
    await AuditService.logUpdate(
      entityType: AppConstants.auditEntityAccount,
      entityId: account.id!,
      entityName: cleanAccount.name,
      oldValues: oldAccount.toMap(),
      newValues: cleanAccount.toMap(),
      description:
          'تم تحديث الحساب: ${cleanAccount.name} (${cleanAccount.code})',
      category: 'Accounting',
    );

    return result;
  }

  Future<int> deleteAccount(int id) async {
    final db = await _databaseHelper.database;

    // الحصول على بيانات الحساب قبل الحذف للمراجعة
    final accountToDelete = await getAccountById(id);
    if (accountToDelete == null) {
      throw Exception('الحساب غير موجود');
    }

    // التحقق من عدم وجود حسابات فرعية
    final childAccounts = await getChildAccounts(id);
    final hasSubAccounts = childAccounts.isNotEmpty;

    // التحقق من عدم وجود قيود محاسبية
    final journalEntries = await db.query(
      AppConstants.journalEntryDetailsTable,
      where: 'account_id = ?',
      whereArgs: [id],
      limit: 1,
    );
    final hasJournalEntries = journalEntries.isNotEmpty;

    // التحقق من عدم وجود فواتير مرتبطة
    final invoices = await db.query(
      AppConstants.invoicesTable,
      where: 'customer_id = ? OR supplier_id = ?',
      whereArgs: [id, id],
      limit: 1,
    );
    final hasInvoices = invoices.isNotEmpty;

    // استخدام ValidationService للتحقق من قواعد الحذف
    ValidationService.validateAccountDeletion(
      id,
      hasJournalEntries: hasJournalEntries,
      hasInvoices: hasInvoices,
      hasSubAccounts: hasSubAccounts,
    );

    final result = await db.delete(
      AppConstants.accountsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    // تسجيل عملية الحذف في سجل المراجعة
    await AuditService.logDelete(
      entityType: AppConstants.auditEntityAccount,
      entityId: id,
      entityName: accountToDelete.name,
      oldValues: accountToDelete.toMap(),
      description:
          'تم حذف الحساب: ${accountToDelete.name} (${accountToDelete.code})',
      category: 'Accounting',
    );

    return result;
  }

  Future<void> updateAccountBalance(
    int accountId,
    double amount,
    bool isDebit,
  ) async {
    final db = await _databaseHelper.database;
    final account = await getAccountById(accountId);

    if (account == null) {
      throw Exception('الحساب غير موجود');
    }

    double newBalance = account.balance;

    if (account.isDebitAccount) {
      // حسابات المدين: تزيد بالمدين وتقل بالدائن
      newBalance += isDebit ? amount : -amount;
    } else {
      // حسابات الدائن: تزيد بالدائن وتقل بالمدين
      newBalance += isDebit ? -amount : amount;
    }

    await db.update(
      AppConstants.accountsTable,
      {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }

  Future<List<Account>> searchAccounts(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.accountsTable,
      where: '(name LIKE ? OR code LIKE ?) AND is_active = 1',
      whereArgs: ['%$searchTerm%', '%$searchTerm%'],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }

  Future<String> generateAccountCode(String type) async {
    final db = await _databaseHelper.database;

    // تحديد البادئة حسب نوع الحساب
    String prefix = _getAccountTypePrefix(type);

    // استعلام محسن للحصول على أكبر رقم كود
    final result = await db.rawQuery(
      '''
      SELECT MAX(CAST(SUBSTR(code, ${prefix.length + 1}) AS INTEGER)) as max_number
      FROM ${AppConstants.accountsTable}
      WHERE type = ? AND code LIKE ? AND code GLOB ?
    ''',
      [type, '$prefix%', '$prefix[0-9]*'],
    );

    final maxNumber = result.first['max_number'] as int? ?? 0;
    return '$prefix${(maxNumber + 1).toString().padLeft(3, '0')}';
  }

  String _getAccountTypePrefix(String type) {
    switch (type) {
      case AppConstants.accountTypeAsset:
        return '1';
      case AppConstants.accountTypeLiability:
        return '2';
      case AppConstants.accountTypeEquity:
        return '3';
      case AppConstants.accountTypeRevenue:
        return '4';
      case AppConstants.accountTypeExpense:
        return '5';
      case AppConstants.accountTypePurchase:
        return '6';
      case AppConstants.accountTypeSale:
        return '7';
      case AppConstants.accountTypeInventory:
        return '8';
      default:
        return '9';
    }
  }

  Future<Map<String, double>> getAccountBalancesByType() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT type, SUM(balance) as total_balance
      FROM ${AppConstants.accountsTable}
      WHERE is_active = 1
      GROUP BY type
    ''');

    Map<String, double> balances = {};
    for (final row in result) {
      balances[row['type'] as String] =
          (row['total_balance'] as num?)?.toDouble() ?? 0.0;
    }

    return balances;
  }

  // ===============================
  // طرق التقسيم والتحسين
  // ===============================

  /// الحصول على الحسابات مع التقسيم
  Future<PaginationResult<Account>> getAccountsPaginated(
    PaginationParams params,
  ) async {
    return await PerformanceService().measureOperation(
      'get_accounts_paginated',
      () async {
        final cacheKey =
            'accounts_page_${params.page}_${params.pageSize}_${params.searchQuery ?? ''}_${params.sortBy ?? ''}';

        // محاولة الحصول على النتيجة من التخزين المؤقت
        final cached = CacheService().get<PaginationResult<Account>>(cacheKey);
        if (cached != null) {
          return cached;
        }

        final db = await _databaseHelper.database;

        // بناء الاستعلام الأساسي
        String baseQuery =
            '''
          SELECT * FROM ${AppConstants.accountsTable}
          WHERE is_active = 1
        ''';

        final queryParams = <dynamic>[];

        // إضافة البحث
        if (params.searchQuery != null && params.searchQuery!.isNotEmpty) {
          baseQuery +=
              ' AND (code LIKE ? OR name LIKE ? OR description LIKE ?)';
          final searchTerm = '%${params.searchQuery}%';
          queryParams.addAll([searchTerm, searchTerm, searchTerm]);
        }

        // إضافة الفلترة
        if (params.filters != null) {
          for (final entry in params.filters!.entries) {
            if (entry.value != null) {
              baseQuery += ' AND ${entry.key} = ?';
              queryParams.add(entry.value);
            }
          }
        }

        // إضافة الترتيب
        final sortBy = params.sortBy ?? 'code';
        final sortDirection = params.sortAscending ? 'ASC' : 'DESC';
        baseQuery += ' ORDER BY $sortBy $sortDirection';

        // حساب العدد الإجمالي
        final countQuery =
            'SELECT COUNT(*) as count FROM (${baseQuery.replaceAll(RegExp(r'ORDER BY.*$'), '')})';
        final countResult = await db.rawQuery(countQuery, queryParams);
        final totalItems = countResult.first['count'] as int;

        // إضافة التقسيم
        final offset = (params.page - 1) * params.pageSize;
        final paginatedQuery =
            '$baseQuery LIMIT ${params.pageSize} OFFSET $offset';

        // تنفيذ الاستعلام
        final results = await db.rawQuery(paginatedQuery, queryParams);
        final accounts = results.map((map) => Account.fromMap(map)).toList();

        // إنشاء نتيجة التقسيم
        final result = PaginationResult<Account>(
          items: accounts,
          currentPage: params.page,
          pageSize: params.pageSize,
          totalItems: totalItems,
          totalPages: (totalItems / params.pageSize).ceil(),
          hasNextPage: params.page < (totalItems / params.pageSize).ceil(),
          hasPreviousPage: params.page > 1,
        );

        // حفظ في التخزين المؤقت
        CacheService().put(
          cacheKey,
          result,
          ttl: Duration(minutes: 5),
          tags: ['accounts'],
        );

        return result;
      },
      category: 'database',
      metadata: {
        'page': params.page,
        'pageSize': params.pageSize,
        'hasSearch': params.searchQuery?.isNotEmpty ?? false,
      },
    );
  }

  /// البحث في الحسابات مع التقسيم
  Future<PaginationResult<Account>> searchAccountsPaginated(
    String query,
    int page, {
    int pageSize = 20,
    String? sortBy,
    bool sortAscending = true,
  }) async {
    final params = PaginationParams(
      page: page,
      pageSize: pageSize,
      searchQuery: query,
      sortBy: sortBy,
      sortAscending: sortAscending,
    );

    return await getAccountsPaginated(params);
  }

  /// الحصول على الحسابات حسب النوع مع التقسيم
  Future<PaginationResult<Account>> getAccountsByTypePaginated(
    String accountType,
    int page, {
    int pageSize = 20,
    String? sortBy,
    bool sortAscending = true,
  }) async {
    final params = PaginationParams(
      page: page,
      pageSize: pageSize,
      filters: {'type': accountType},
      sortBy: sortBy,
      sortAscending: sortAscending,
    );

    return await getAccountsPaginated(params);
  }

  /// مسح التخزين المؤقت للحسابات
  void clearAccountsCache() {
    CacheService().removeByTags(['accounts']);
  }
}
