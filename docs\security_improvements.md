# تحسينات الأمان المقترحة - Smart Ledger

## 🔒 تحسينات أمنية فورية

### 1. تشفير قاعدة البيانات

```dart
import 'package:sqflite_sqlcipher/sqflite.dart';

class SecureDatabaseHelper {
  static final SecureDatabaseHelper _instance = SecureDatabaseHelper._internal();
  static Database? _database;
  
  factory SecureDatabaseHelper() => _instance;
  SecureDatabaseHelper._internal();
  
  Future<Database> get database async {
    _database ??= await _initSecureDatabase();
    return _database!;
  }
  
  Future<Database> _initSecureDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    
    // كلمة مرور قوية للتشفير
    String password = await _generateDatabasePassword();
    
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      password: password,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }
  
  Future<String> _generateDatabasePassword() async {
    final prefs = await SharedPreferences.getInstance();
    String? password = prefs.getString('db_password');
    
    if (password == null) {
      // إنشاء كلمة مرور عشوائية قوية
      password = _generateSecurePassword();
      await prefs.setString('db_password', password);
    }
    
    return password;
  }
  
  String _generateSecurePassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#\$%^&*';
    final random = Random.secure();
    return List.generate(32, (index) => chars[random.nextInt(chars.length)]).join();
  }
}
```

### 2. إضافة نظام Audit Trail

```dart
class AuditService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  Future<void> logActivity({
    required String action,
    required String tableName,
    required int recordId,
    String? oldValues,
    String? newValues,
    String? userId,
  }) async {
    final db = await _databaseHelper.database;
    
    await db.insert(AppConstants.auditLogTable, {
      'action': action,
      'table_name': tableName,
      'record_id': recordId,
      'old_values': oldValues,
      'new_values': newValues,
      'user_id': userId ?? 'system',
      'ip_address': await _getDeviceIP(),
      'device_info': await _getDeviceInfo(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  Future<String> _getDeviceIP() async {
    // الحصول على IP الجهاز
    return '127.0.0.1'; // للتطبيقات المحلية
  }
  
  Future<String> _getDeviceInfo() async {
    // معلومات الجهاز
    return Platform.operatingSystem;
  }
}

// إضافة جدول Audit Log
Future<void> _createAuditLogTable(Database db) async {
  await db.execute('''
    CREATE TABLE ${AppConstants.auditLogTable} (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      action TEXT NOT NULL,
      table_name TEXT NOT NULL,
      record_id INTEGER NOT NULL,
      old_values TEXT,
      new_values TEXT,
      user_id TEXT NOT NULL,
      ip_address TEXT,
      device_info TEXT,
      timestamp TEXT NOT NULL
    )
  ''');
  
  // فهرس للبحث السريع
  await db.execute('''
    CREATE INDEX idx_audit_log_timestamp ON ${AppConstants.auditLogTable}(timestamp);
  ''');
  
  await db.execute('''
    CREATE INDEX idx_audit_log_table_record ON ${AppConstants.auditLogTable}(table_name, record_id);
  ''');
}
```

### 3. تحسين التحقق من صحة البيانات

```dart
class ValidationService {
  // التحقق من صحة البيانات المالية
  static bool validateAmount(double amount) {
    return amount >= 0 && amount <= *********.99;
  }
  
  // التحقق من صحة كود الحساب
  static bool validateAccountCode(String code) {
    final regex = RegExp(r'^[A-Z][0-9]{3,6}$');
    return regex.hasMatch(code);
  }
  
  // التحقق من صحة رقم الهاتف
  static bool validatePhoneNumber(String phone) {
    final regex = RegExp(r'^\+?[0-9]{8,15}$');
    return regex.hasMatch(phone);
  }
  
  // التحقق من صحة البريد الإلكتروني
  static bool validateEmail(String email) {
    final regex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return regex.hasMatch(email);
  }
  
  // التحقق من صحة التاريخ
  static bool validateDate(DateTime date) {
    final now = DateTime.now();
    final minDate = DateTime(1900);
    final maxDate = DateTime(now.year + 10);
    
    return date.isAfter(minDate) && date.isBefore(maxDate);
  }
  
  // تنظيف البيانات من الأحرف الخطيرة
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"\']'), '')
        .replaceAll(RegExp(r'[;]'), '')
        .trim();
  }
}

// استخدام التحقق في الخدمات
class SecureAccountService extends AccountService {
  @override
  Future<int> insertAccount(Account account) async {
    // التحقق من صحة البيانات
    if (!ValidationService.validateAccountCode(account.code)) {
      throw ValidationException('كود الحساب غير صحيح');
    }
    
    if (!ValidationService.validateAmount(account.balance)) {
      throw ValidationException('رصيد الحساب غير صحيح');
    }
    
    // تنظيف البيانات
    final cleanAccount = account.copyWith(
      name: ValidationService.sanitizeInput(account.name),
      description: account.description != null 
          ? ValidationService.sanitizeInput(account.description!)
          : null,
    );
    
    // تسجيل العملية
    final auditService = AuditService();
    final result = await super.insertAccount(cleanAccount);
    
    await auditService.logActivity(
      action: 'INSERT',
      tableName: 'accounts',
      recordId: result,
      newValues: jsonEncode(cleanAccount.toMap()),
    );
    
    return result;
  }
}
```

### 4. إضافة نظام صلاحيات المستخدمين

```dart
enum Permission {
  viewAccounts,
  addAccounts,
  editAccounts,
  deleteAccounts,
  viewJournalEntries,
  addJournalEntries,
  editJournalEntries,
  deleteJournalEntries,
  postJournalEntries,
  viewReports,
  exportReports,
  manageUsers,
  manageSettings,
}

class User {
  final int? id;
  final String username;
  final String passwordHash;
  final String fullName;
  final String email;
  final bool isActive;
  final List<Permission> permissions;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  const User({
    this.id,
    required this.username,
    required this.passwordHash,
    required this.fullName,
    required this.email,
    required this.isActive,
    required this.permissions,
    required this.createdAt,
    required this.updatedAt,
  });
}

class AuthService {
  static User? _currentUser;
  
  static User? get currentUser => _currentUser;
  
  static bool hasPermission(Permission permission) {
    return _currentUser?.permissions.contains(permission) ?? false;
  }
  
  static Future<bool> login(String username, String password) async {
    final userService = UserService();
    final user = await userService.authenticateUser(username, password);
    
    if (user != null && user.isActive) {
      _currentUser = user;
      
      // تسجيل عملية تسجيل الدخول
      final auditService = AuditService();
      await auditService.logActivity(
        action: 'LOGIN',
        tableName: 'users',
        recordId: user.id!,
        userId: user.username,
      );
      
      return true;
    }
    
    return false;
  }
  
  static Future<void> logout() async {
    if (_currentUser != null) {
      final auditService = AuditService();
      await auditService.logActivity(
        action: 'LOGOUT',
        tableName: 'users',
        recordId: _currentUser!.id!,
        userId: _currentUser!.username,
      );
      
      _currentUser = null;
    }
  }
}

class PermissionGuard {
  static void requirePermission(Permission permission) {
    if (!AuthService.hasPermission(permission)) {
      throw UnauthorizedException('ليس لديك صلاحية لتنفيذ هذه العملية');
    }
  }
}

// استخدام نظام الصلاحيات
class SecureJournalEntryService extends JournalEntryService {
  @override
  Future<int> insertJournalEntry(JournalEntry journalEntry) async {
    PermissionGuard.requirePermission(Permission.addJournalEntries);
    return super.insertJournalEntry(journalEntry);
  }
  
  @override
  Future<int> deleteJournalEntry(int id) async {
    PermissionGuard.requirePermission(Permission.deleteJournalEntries);
    return super.deleteJournalEntry(id);
  }
  
  @override
  Future<int> postJournalEntry(int id) async {
    PermissionGuard.requirePermission(Permission.postJournalEntries);
    return super.postJournalEntry(id);
  }
}
```

### 5. تشفير النسخ الاحتياطية

```dart
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

class SecureBackupService {
  late final Encrypter _encrypter;
  late final IV _iv;
  
  SecureBackupService() {
    final key = Key.fromSecureRandom(32);
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }
  
  Future<String> createEncryptedBackup() async {
    try {
      // تصدير البيانات
      final backupData = await _exportAllData();
      
      // ضغط البيانات
      final compressedData = gzip.encode(utf8.encode(jsonEncode(backupData)));
      
      // تشفير البيانات
      final encryptedData = _encrypter.encryptBytes(compressedData, iv: _iv);
      
      // إنشاء ملف النسخة الاحتياطية
      final backupFile = {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'checksum': _calculateChecksum(compressedData),
        'data': encryptedData.base64,
        'iv': _iv.base64,
      };
      
      return jsonEncode(backupFile);
    } catch (e) {
      throw BackupException('فشل في إنشاء النسخة الاحتياطية: $e');
    }
  }
  
  Future<bool> restoreFromEncryptedBackup(String encryptedBackup) async {
    try {
      final backupFile = jsonDecode(encryptedBackup);
      
      // فك التشفير
      final encryptedData = Encrypted.fromBase64(backupFile['data']);
      final iv = IV.fromBase64(backupFile['iv']);
      final decryptedData = _encrypter.decryptBytes(encryptedData, iv: iv);
      
      // فك الضغط
      final decompressedData = gzip.decode(decryptedData);
      final backupData = jsonDecode(utf8.decode(decompressedData));
      
      // التحقق من سلامة البيانات
      final expectedChecksum = backupFile['checksum'];
      final actualChecksum = _calculateChecksum(decryptedData);
      
      if (expectedChecksum != actualChecksum) {
        throw BackupException('البيانات تالفة - فشل في التحقق من السلامة');
      }
      
      // استعادة البيانات
      await _importAllData(backupData);
      
      return true;
    } catch (e) {
      throw BackupException('فشل في استعادة النسخة الاحتياطية: $e');
    }
  }
  
  String _calculateChecksum(List<int> data) {
    return sha256.convert(data).toString();
  }
  
  Future<Map<String, dynamic>> _exportAllData() async {
    // تصدير جميع البيانات من قاعدة البيانات
    final db = await DatabaseHelper().database;
    
    final accounts = await db.query(AppConstants.accountsTable);
    final journalEntries = await db.query(AppConstants.journalEntriesTable);
    final journalEntryDetails = await db.query(AppConstants.journalEntryDetailsTable);
    final customers = await db.query(AppConstants.customersTable);
    final suppliers = await db.query(AppConstants.suppliersTable);
    final items = await db.query(AppConstants.itemsTable);
    
    return {
      'accounts': accounts,
      'journal_entries': journalEntries,
      'journal_entry_details': journalEntryDetails,
      'customers': customers,
      'suppliers': suppliers,
      'items': items,
    };
  }
  
  Future<void> _importAllData(Map<String, dynamic> data) async {
    final db = await DatabaseHelper().database;
    
    await db.transaction((txn) async {
      // حذف البيانات الموجودة
      await txn.delete(AppConstants.journalEntryDetailsTable);
      await txn.delete(AppConstants.journalEntriesTable);
      await txn.delete(AppConstants.accountsTable);
      await txn.delete(AppConstants.customersTable);
      await txn.delete(AppConstants.suppliersTable);
      await txn.delete(AppConstants.itemsTable);
      
      // استيراد البيانات الجديدة
      for (final account in data['accounts']) {
        await txn.insert(AppConstants.accountsTable, account);
      }
      
      for (final customer in data['customers']) {
        await txn.insert(AppConstants.customersTable, customer);
      }
      
      for (final supplier in data['suppliers']) {
        await txn.insert(AppConstants.suppliersTable, supplier);
      }
      
      for (final item in data['items']) {
        await txn.insert(AppConstants.itemsTable, item);
      }
      
      for (final entry in data['journal_entries']) {
        await txn.insert(AppConstants.journalEntriesTable, entry);
      }
      
      for (final detail in data['journal_entry_details']) {
        await txn.insert(AppConstants.journalEntryDetailsTable, detail);
      }
    });
  }
}

class BackupException implements Exception {
  final String message;
  BackupException(this.message);
  
  @override
  String toString() => 'BackupException: $message';
}

class UnauthorizedException implements Exception {
  final String message;
  UnauthorizedException(this.message);
  
  @override
  String toString() => 'UnauthorizedException: $message';
}

class ValidationException implements Exception {
  final String message;
  ValidationException(this.message);
  
  @override
  String toString() => 'ValidationException: $message';
}
```

---

**ملاحظة:** هذه التحسينات الأمنية ضرورية لحماية البيانات المالية الحساسة. يُنصح بتطبيقها تدريجياً مع اختبار شامل لكل ميزة أمنية.
