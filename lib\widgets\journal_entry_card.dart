import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_colors.dart';
import '../models/journal_entry.dart';

class JournalEntryCard extends StatelessWidget {
  final JournalEntry journalEntry;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onPost;

  const JournalEntryCard({
    super.key,
    required this.journalEntry,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    required this.onPost,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة نوع القيد
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getEntryTypeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getEntryTypeIcon(),
                      color: _getEntryTypeColor(),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // معلومات القيد
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'قيد رقم: ${journalEntry.entryNumber}',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.textPrimary,
                                    ),
                              ),
                            ),
                            // حالة القيد
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: journalEntry.isPosted
                                    ? AppColors.success.withValues(alpha: 0.1)
                                    : AppColors.warning.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                journalEntry.statusArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: journalEntry.isPosted
                                          ? AppColors.success
                                          : AppColors.warning,
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'التاريخ: ${DateFormat('dd/MM/yyyy').format(journalEntry.entryDate)}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: AppColors.textSecondary),
                            ),
                            const SizedBox(width: 16),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getEntryTypeColor().withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                journalEntry.typeArabic,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: _getEntryTypeColor(),
                                      fontSize: 10,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                        case 'post':
                          onPost();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (!journalEntry.isPosted) ...[
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'post',
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                size: 18,
                                color: Colors.green,
                              ),
                              SizedBox(width: 8),
                              Text('ترحيل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ],
                    child: const Icon(
                      Icons.more_vert,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // وصف القيد
              Text(
                journalEntry.description,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: AppColors.textPrimary),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              // المبالغ
              Row(
                children: [
                  Expanded(
                    child: _buildAmountInfo(
                      'إجمالي المدين',
                      journalEntry.totalDebit,
                      AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAmountInfo(
                      'إجمالي الدائن',
                      journalEntry.totalCredit,
                      AppColors.error,
                    ),
                  ),
                ],
              ),
              // عدد التفاصيل
              if (journalEntry.details.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.list, size: 16, color: AppColors.textSecondary),
                    const SizedBox(width: 4),
                    Text(
                      'عدد الحسابات: ${journalEntry.details.length}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    if (!journalEntry.isBalanced)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'غير متوازن',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppColors.error, fontSize: 10),
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInfo(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
        ),
        Text(
          '${NumberFormat('#,##0.00').format(amount)} ل.س',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Color _getEntryTypeColor() {
    switch (journalEntry.type) {
      case 'general':
        return AppColors.primary;
      case 'sale':
        return AppColors.success;
      case 'purchase':
        return AppColors.warning;
      case 'payment':
        return AppColors.error;
      case 'receipt':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getEntryTypeIcon() {
    switch (journalEntry.type) {
      case 'general':
        return Icons.receipt_long;
      case 'sale':
        return Icons.point_of_sale;
      case 'purchase':
        return Icons.shopping_cart;
      case 'payment':
        return Icons.payment;
      case 'receipt':
        return Icons.receipt;
      default:
        return Icons.description;
    }
  }
}
