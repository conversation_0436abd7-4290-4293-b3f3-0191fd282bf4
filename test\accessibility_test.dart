import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/main.dart';
import 'package:smart_ledger/screens/accounts_screen.dart';
import 'package:smart_ledger/screens/add_account_screen.dart';
import 'package:smart_ledger/services/accessibility_service.dart';
import 'package:smart_ledger/services/screen_reader_service.dart';

void main() {
  group('اختبارات إمكانية الوصول', () {
    testWidgets('اختبار Semantic Labels في الشاشة الرئيسية', (
      WidgetTester tester,
    ) async {
      // بناء التطبيق
      await tester.pumpWidget(const SmartLedgerApp());

      // التحقق من وجود semantic labels للعناصر الرئيسية
      expect(find.bySemanticsLabel(RegExp(r'شعار تطبيق')), findsOneWidget);
      expect(find.bySemanticsLabel(RegExp(r'عنوان التطبيق')), findsOneWidget);
      expect(find.bySemanticsLabel(RegExp(r'إحصائيات سريعة')), findsOneWidget);
      expect(
        find.bySemanticsLabel(RegExp(r'لوحة التحكم الرئيسية')),
        findsOneWidget,
      );

      // التحقق من وجود semantic labels للبطاقات
      expect(
        find.bySemanticsLabel(RegExp(r'بطاقة.*دليل الحسابات')),
        findsOneWidget,
      );
      expect(
        find.bySemanticsLabel(RegExp(r'بطاقة.*القيود المحاسبية')),
        findsOneWidget,
      );
      expect(find.bySemanticsLabel(RegExp(r'بطاقة.*الفواتير')), findsOneWidget);
      expect(
        find.bySemanticsLabel(RegExp(r'بطاقة.*العملاء والموردين')),
        findsOneWidget,
      );
      expect(find.bySemanticsLabel(RegExp(r'بطاقة.*التقارير')), findsOneWidget);
      expect(
        find.bySemanticsLabel(RegExp(r'بطاقة.*الإعدادات')),
        findsOneWidget,
      );
    });

    testWidgets('اختبار التنقل بلوحة المفاتيح', (WidgetTester tester) async {
      await tester.pumpWidget(const SmartLedgerApp());

      // العثور على أول عنصر قابل للتركيز
      final firstFocusable = find.byType(InkWell).first;
      await tester.tap(firstFocusable);
      await tester.pump();

      // محاكاة الضغط على Tab للانتقال للعنصر التالي
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pump();

      // التحقق من تغيير التركيز
      expect(tester.binding.focusManager.primaryFocus, isNotNull);
    });

    testWidgets('اختبار إمكانية الوصول في شاشة الحسابات', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(MaterialApp(home: AccountsScreen()));
      await tester.pumpAndSettle();

      // التحقق من وجود semantic labels
      expect(
        find.bySemanticsLabel(RegExp(r'شاشة دليل الحسابات')),
        findsOneWidget,
      );
      expect(find.bySemanticsLabel(RegExp(r'إضافة حساب جديد')), findsOneWidget);
      expect(
        find.bySemanticsLabel(RegExp(r'تحديث قائمة الحسابات')),
        findsOneWidget,
      );
      expect(find.bySemanticsLabel(RegExp(r'حقل البحث')), findsOneWidget);
      expect(
        find.bySemanticsLabel(RegExp(r'فلاتر أنواع الحسابات')),
        findsOneWidget,
      );
    });

    testWidgets('اختبار إمكانية الوصول في نموذج إضافة الحساب', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(MaterialApp(home: AddAccountScreen()));
      await tester.pumpAndSettle();

      // التحقق من وجود focus nodes للحقول
      final codeField = find.byType(TextFormField).first;
      await tester.tap(codeField);
      await tester.pump();

      // التحقق من إمكانية التنقل بين الحقول
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pump();

      expect(tester.binding.focusManager.primaryFocus, isNotNull);
    });

    testWidgets('اختبار اختصارات لوحة المفاتيح', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: AddAccountScreen()));
      await tester.pumpAndSettle();

      // اختبار اختصار الحفظ (Ctrl+S)
      await tester.sendKeyDownEvent(LogicalKeyboardKey.controlLeft);
      await tester.sendKeyEvent(LogicalKeyboardKey.keyS);
      await tester.sendKeyUpEvent(LogicalKeyboardKey.controlLeft);
      await tester.pump();

      // اختبار اختصار الإلغاء (Escape)
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pump();
    });

    testWidgets('اختبار التباين والألوان', (WidgetTester tester) async {
      await tester.pumpWidget(const SmartLedgerApp());

      // العثور على عناصر النص والتحقق من التباين
      final textWidgets = find.byType(Text);
      expect(textWidgets, findsWidgets);

      // التحقق من وجود ألوان مناسبة للتباين
      for (final textWidget in textWidgets.evaluate()) {
        final text = textWidget.widget as Text;
        if (text.style?.color != null) {
          // التحقق من أن اللون ليس شفافاً جداً
          expect(
            (text.style!.color!.a * 255.0).round() & 0xff,
            greaterThan(100),
          );
        }
      }
    });

    testWidgets('اختبار أحجام النصوص', (WidgetTester tester) async {
      await tester.pumpWidget(const SmartLedgerApp());

      // التحقق من أن النصوص لها أحجام مناسبة
      final textWidgets = find.byType(Text);
      for (final textWidget in textWidgets.evaluate()) {
        final text = textWidget.widget as Text;
        if (text.style?.fontSize != null) {
          // التحقق من أن حجم النص مناسب (أكبر من 12)
          expect(text.style!.fontSize!, greaterThanOrEqualTo(12.0));
        }
      }
    });

    testWidgets('اختبار أحجام أهداف اللمس', (WidgetTester tester) async {
      await tester.pumpWidget(const SmartLedgerApp());

      // العثور على الأزرار والتحقق من أحجامها
      final buttons = find.byType(InkWell);
      for (final button in buttons.evaluate()) {
        final renderBox = button.renderObject as RenderBox?;
        if (renderBox != null) {
          final size = renderBox.size;
          // التحقق من أن الهدف كبير بما فيه الكفاية (48x48 على الأقل)
          expect(size.width, greaterThanOrEqualTo(44.0));
          expect(size.height, greaterThanOrEqualTo(44.0));
        }
      }
    });

    group('اختبارات خدمات إمكانية الوصول', () {
      test('اختبار AccessibilityService', () {
        // اختبار إنشاء semantic button
        final button = AccessibilityService.createSemanticButton(
          child: const Text('اختبار'),
          onPressed: () {},
          label: 'زر اختبار',
          hint: 'اضغط للاختبار',
        );

        expect(button, isA<Semantics>());
      });

      test('اختبار ScreenReaderService', () {
        // اختبار بناء وصف العنصر
        final description = ScreenReaderService.buildElementDescription(
          type: 'زر',
          name: 'حفظ',
          value: 'مفعل',
          state: 'متاح',
          hint: 'اضغط للحفظ',
        );

        expect(description, contains('زر: حفظ'));
        expect(description, contains('القيمة: مفعل'));
        expect(description, contains('الحالة: متاح'));
        expect(description, contains('اضغط للحفظ'));
      });

      test('اختبار بناء وصف القائمة', () {
        final description = ScreenReaderService.buildListDescription(
          'قائمة الحسابات',
          5,
          2,
        );

        expect(description, contains('قائمة الحسابات'));
        expect(description, contains('تحتوي على 5 عنصر'));
        expect(description, contains('العنصر المحدد: 3'));
      });

      test('اختبار بناء وصف النموذج', () {
        final description = ScreenReaderService.buildFormDescription(
          'نموذج إضافة حساب',
          4,
          1,
        );

        expect(description, contains('نموذج إضافة حساب'));
        expect(description, contains('يحتوي على 4 حقل'));
        expect(description, contains('يوجد 1 خطأ'));
      });
    });

    group('اختبارات التكامل مع قارئ الشاشة', () {
      testWidgets('اختبار الإعلانات الصوتية', (WidgetTester tester) async {
        await tester.pumpWidget(const SmartLedgerApp());

        // محاكاة تفعيل قارئ الشاشة
        tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('flutter/accessibility'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'announce') {
              // التحقق من أن الرسالة تم إرسالها
              expect(methodCall.arguments['message'], isA<String>());
              return null;
            }
            return null;
          },
        );

        // اختبار إعلان
        ScreenReaderService.announceSuccess('حفظ البيانات');
      });
    });

    group('اختبارات الأداء مع إمكانية الوصول', () {
      testWidgets('اختبار أداء التطبيق مع تفعيل إمكانية الوصول', (
        WidgetTester tester,
      ) async {
        // قياس وقت بناء الشاشة
        final stopwatch = Stopwatch()..start();

        await tester.pumpWidget(const SmartLedgerApp());
        await tester.pumpAndSettle();

        stopwatch.stop();

        // التحقق من أن الوقت معقول (أقل من ثانية واحدة)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('اختبارات التوافق مع أنظمة التشغيل', () {
      testWidgets('اختبار التوافق مع TalkBack (Android)', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(const SmartLedgerApp());

        // التحقق من وجود semantic nodes مناسبة
        final semantics = tester.binding.rootPipelineOwner.semanticsOwner;
        expect(semantics, isNotNull);

        // التحقق من أن العقد الدلالية تحتوي على معلومات مناسبة
        if (semantics?.rootSemanticsNode != null) {
          semantics!.rootSemanticsNode!.visitChildren((node) {
            if (node.label.isNotEmpty) {
              expect(node.label, isA<String>());
            }
            return true;
          });
        }
      });
    });
  });
}
