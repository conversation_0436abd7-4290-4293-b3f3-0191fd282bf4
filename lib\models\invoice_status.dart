/// تعدادات وإدارة حالات الفواتير
/// يحتوي على جميع الحالات الممكنة للفواتير والانتقالات المسموحة بينها
library;

/// حالات الفواتير المختلفة
enum InvoiceStatus {
  /// مسودة - الحالة الأولية للفاتورة
  draft('draft', 'مسودة', 'فاتورة في مرحلة الإعداد'),
  
  /// مؤكدة - تم تأكيد الفاتورة وأصبحت نافذة
  confirmed('confirmed', 'مؤكدة', 'فاتورة مؤكدة ونافذة'),
  
  /// مدفوعة جزئياً - تم دفع جزء من قيمة الفاتورة
  partiallyPaid('partially_paid', 'مدفوعة جزئياً', 'تم دفع جزء من قيمة الفاتورة'),
  
  /// مدفوعة بالكامل - تم دفع كامل قيمة الفاتورة
  fullyPaid('fully_paid', 'مدفوعة بالكامل', 'تم دفع كامل قيمة الفاتورة'),
  
  /// متأخرة - تجاوزت الفاتورة تاريخ الاستحقاق
  overdue('overdue', 'متأخرة', 'تجاوزت الفاتورة تاريخ الاستحقاق'),
  
  /// ملغاة - تم إلغاء الفاتورة
  cancelled('cancelled', 'ملغاة', 'فاتورة ملغاة');

  const InvoiceStatus(this.code, this.displayName, this.description);

  /// كود الحالة في قاعدة البيانات
  final String code;
  
  /// الاسم المعروض للمستخدم
  final String displayName;
  
  /// وصف الحالة
  final String description;

  /// الحصول على حالة من الكود
  static InvoiceStatus fromCode(String code) {
    return InvoiceStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => InvoiceStatus.draft,
    );
  }

  /// التحقق من إمكانية الانتقال إلى حالة أخرى
  bool canTransitionTo(InvoiceStatus newStatus) {
    switch (this) {
      case InvoiceStatus.draft:
        return [
          InvoiceStatus.confirmed,
          InvoiceStatus.cancelled,
        ].contains(newStatus);
        
      case InvoiceStatus.confirmed:
        return [
          InvoiceStatus.partiallyPaid,
          InvoiceStatus.fullyPaid,
          InvoiceStatus.overdue,
          InvoiceStatus.cancelled,
        ].contains(newStatus);
        
      case InvoiceStatus.partiallyPaid:
        return [
          InvoiceStatus.fullyPaid,
          InvoiceStatus.overdue,
          InvoiceStatus.cancelled,
        ].contains(newStatus);
        
      case InvoiceStatus.overdue:
        return [
          InvoiceStatus.partiallyPaid,
          InvoiceStatus.fullyPaid,
          InvoiceStatus.cancelled,
        ].contains(newStatus);
        
      case InvoiceStatus.fullyPaid:
        return [
          InvoiceStatus.cancelled, // في حالة الإلغاء الاستثنائي
        ].contains(newStatus);
        
      case InvoiceStatus.cancelled:
        return false; // لا يمكن الانتقال من الملغاة إلى أي حالة أخرى
    }
  }

  /// الحصول على الحالات المسموح الانتقال إليها
  List<InvoiceStatus> getAllowedTransitions() {
    return InvoiceStatus.values
        .where((status) => canTransitionTo(status))
        .toList();
  }

  /// التحقق من إمكانية تعديل الفاتورة
  bool get canEdit {
    return this == InvoiceStatus.draft;
  }

  /// التحقق من إمكانية حذف الفاتورة
  bool get canDelete {
    return [
      InvoiceStatus.draft,
      InvoiceStatus.cancelled,
    ].contains(this);
  }

  /// التحقق من إمكانية إضافة دفعات
  bool get canAddPayments {
    return [
      InvoiceStatus.confirmed,
      InvoiceStatus.partiallyPaid,
      InvoiceStatus.overdue,
    ].contains(this);
  }

  /// التحقق من إمكانية طباعة الفاتورة
  bool get canPrint {
    return this != InvoiceStatus.draft;
  }

  /// الحصول على لون الحالة للواجهة
  String get colorCode {
    switch (this) {
      case InvoiceStatus.draft:
        return '#9E9E9E'; // رمادي
      case InvoiceStatus.confirmed:
        return '#2196F3'; // أزرق
      case InvoiceStatus.partiallyPaid:
        return '#FF9800'; // برتقالي
      case InvoiceStatus.fullyPaid:
        return '#4CAF50'; // أخضر
      case InvoiceStatus.overdue:
        return '#F44336'; // أحمر
      case InvoiceStatus.cancelled:
        return '#607D8B'; // رمادي داكن
    }
  }

  /// الحصول على أيقونة الحالة
  String get iconCode {
    switch (this) {
      case InvoiceStatus.draft:
        return 'edit'; // أيقونة تعديل
      case InvoiceStatus.confirmed:
        return 'check_circle'; // أيقونة تأكيد
      case InvoiceStatus.partiallyPaid:
        return 'schedule'; // أيقونة جدولة
      case InvoiceStatus.fullyPaid:
        return 'check_circle_outline'; // أيقونة مكتمل
      case InvoiceStatus.overdue:
        return 'warning'; // أيقونة تحذير
      case InvoiceStatus.cancelled:
        return 'cancel'; // أيقونة إلغاء
    }
  }
}

/// أسباب تغيير حالة الفاتورة
enum StatusChangeReason {
  /// تأكيد الفاتورة
  confirmation('confirmation', 'تأكيد الفاتورة'),
  
  /// استلام دفعة
  paymentReceived('payment_received', 'استلام دفعة'),
  
  /// تجاوز تاريخ الاستحقاق
  overdueDate('overdue_date', 'تجاوز تاريخ الاستحقاق'),
  
  /// إلغاء الفاتورة
  cancellation('cancellation', 'إلغاء الفاتورة'),
  
  /// تصحيح خطأ
  correction('correction', 'تصحيح خطأ'),
  
  /// تحديث تلقائي
  automaticUpdate('automatic_update', 'تحديث تلقائي');

  const StatusChangeReason(this.code, this.displayName);

  final String code;
  final String displayName;

  static StatusChangeReason fromCode(String code) {
    return StatusChangeReason.values.firstWhere(
      (reason) => reason.code == code,
      orElse: () => StatusChangeReason.automaticUpdate,
    );
  }
}

/// سجل تغيير حالة الفاتورة
class InvoiceStatusHistory {
  final int? id;
  final int invoiceId;
  final InvoiceStatus fromStatus;
  final InvoiceStatus toStatus;
  final StatusChangeReason reason;
  final String? notes;
  final String? userId;
  final DateTime changedAt;

  InvoiceStatusHistory({
    this.id,
    required this.invoiceId,
    required this.fromStatus,
    required this.toStatus,
    required this.reason,
    this.notes,
    this.userId,
    DateTime? changedAt,
  }) : changedAt = changedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'from_status': fromStatus.code,
      'to_status': toStatus.code,
      'reason': reason.code,
      'notes': notes,
      'user_id': userId,
      'changed_at': changedAt.toIso8601String(),
    };
  }

  factory InvoiceStatusHistory.fromMap(Map<String, dynamic> map) {
    return InvoiceStatusHistory(
      id: map['id']?.toInt(),
      invoiceId: map['invoice_id']?.toInt() ?? 0,
      fromStatus: InvoiceStatus.fromCode(map['from_status'] ?? 'draft'),
      toStatus: InvoiceStatus.fromCode(map['to_status'] ?? 'draft'),
      reason: StatusChangeReason.fromCode(map['reason'] ?? 'automatic_update'),
      notes: map['notes'],
      userId: map['user_id'],
      changedAt: DateTime.parse(map['changed_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  InvoiceStatusHistory copyWith({
    int? id,
    int? invoiceId,
    InvoiceStatus? fromStatus,
    InvoiceStatus? toStatus,
    StatusChangeReason? reason,
    String? notes,
    String? userId,
    DateTime? changedAt,
  }) {
    return InvoiceStatusHistory(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      fromStatus: fromStatus ?? this.fromStatus,
      toStatus: toStatus ?? this.toStatus,
      reason: reason ?? this.reason,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      changedAt: changedAt ?? this.changedAt,
    );
  }
}

/// خدمة إدارة حالات الفواتير
class InvoiceStatusManager {
  /// تحديد الحالة التلقائية بناءً على المدفوعات
  static InvoiceStatus calculateStatusFromPayments({
    required double totalAmount,
    required double paidAmount,
    required DateTime? dueDate,
    required InvoiceStatus currentStatus,
  }) {
    // إذا كانت الفاتورة ملغاة، تبقى ملغاة
    if (currentStatus == InvoiceStatus.cancelled) {
      return InvoiceStatus.cancelled;
    }

    // إذا كانت مسودة، تبقى مسودة حتى يتم تأكيدها
    if (currentStatus == InvoiceStatus.draft) {
      return InvoiceStatus.draft;
    }

    // حساب المتبقي
    final remainingAmount = totalAmount - paidAmount;

    // إذا تم دفع كامل المبلغ
    if (remainingAmount <= 0.01) { // هامش خطأ صغير
      return InvoiceStatus.fullyPaid;
    }

    // إذا تم دفع جزء من المبلغ
    if (paidAmount > 0.01) {
      // التحقق من تجاوز تاريخ الاستحقاق
      if (dueDate != null && DateTime.now().isAfter(dueDate)) {
        return InvoiceStatus.overdue;
      }
      return InvoiceStatus.partiallyPaid;
    }

    // إذا لم يتم دفع أي مبلغ
    // التحقق من تجاوز تاريخ الاستحقاق
    if (dueDate != null && DateTime.now().isAfter(dueDate)) {
      return InvoiceStatus.overdue;
    }

    // الحالة الافتراضية للفواتير المؤكدة غير المدفوعة
    return InvoiceStatus.confirmed;
  }

  /// التحقق من صحة تغيير الحالة
  static bool validateStatusChange({
    required InvoiceStatus fromStatus,
    required InvoiceStatus toStatus,
    required StatusChangeReason reason,
  }) {
    // التحقق من إمكانية الانتقال
    if (!fromStatus.canTransitionTo(toStatus)) {
      return false;
    }

    // التحقق من منطقية السبب مع التغيير
    switch (reason) {
      case StatusChangeReason.paymentReceived:
        return [
          InvoiceStatus.partiallyPaid,
          InvoiceStatus.fullyPaid,
        ].contains(toStatus);
        
      case StatusChangeReason.confirmation:
        return fromStatus == InvoiceStatus.draft && 
               toStatus == InvoiceStatus.confirmed;
               
      case StatusChangeReason.cancellation:
        return toStatus == InvoiceStatus.cancelled;
        
      case StatusChangeReason.overdueDate:
        return toStatus == InvoiceStatus.overdue;
        
      default:
        return true; // الأسباب الأخرى مقبولة
    }
  }
}
