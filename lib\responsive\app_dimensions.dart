import 'package:flutter/material.dart';
import 'breakpoints.dart';

/// نظام الأبعاد الموحد للتطبيق
/// يوفر قيم ثابتة ومتجاوبة للمسافات والأحجام والخطوط
class AppDimensions {
  /// الحصول على الأبعاد بناءً على عرض الشاشة
  static AppDimensions fromWidth(double width) {
    final deviceType = DeviceType.fromWidth(width);
    return AppDimensions._(deviceType, width);
  }

  /// الحصول على الأبعاد من السياق
  static AppDimensions of(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return fromWidth(width);
  }

  final DeviceType _deviceType;
  final double _width;

  const AppDimensions._(this._deviceType, this._width);

  // ==================== المسافات ====================

  /// المسافة الصغيرة جداً
  double get spacingXS => _getResponsiveValue(4, 6, 8, 10);

  /// المسافة الصغيرة
  double get spacingS => _getResponsiveValue(8, 10, 12, 14);

  /// المسافة المتوسطة
  double get spacingM => _getResponsiveValue(12, 16, 20, 24);

  /// المسافة الكبيرة
  double get spacingL => _getResponsiveValue(16, 20, 24, 28);

  /// المسافة الكبيرة جداً
  double get spacingXL => _getResponsiveValue(20, 24, 28, 32);

  /// المسافة الضخمة
  double get spacingXXL => _getResponsiveValue(24, 32, 40, 48);

  // ==================== الحشو ====================

  /// الحشو الصغير
  double get paddingS => _getResponsiveValue(8, 12, 16, 20);

  /// الحشو المتوسط
  double get paddingM => _getResponsiveValue(16, 20, 24, 28);

  /// الحشو الكبير
  double get paddingL => _getResponsiveValue(20, 24, 28, 32);

  /// الحشو الكبير جداً
  double get paddingXL => _getResponsiveValue(24, 28, 32, 40);

  /// حشو الحاوية الرئيسية
  double get containerPadding => _getResponsiveValue(16, 20, 24, 32);

  /// حشو البطاقات
  double get cardPadding => _getResponsiveValue(12, 16, 20, 24);

  /// حشو الأزرار
  EdgeInsets get buttonPadding => EdgeInsets.symmetric(
    horizontal: _getResponsiveValue(16, 20, 24, 28),
    vertical: _getResponsiveValue(8, 10, 12, 14),
  );

  // ==================== أحجام الخطوط ====================

  /// حجم خط العنوان الرئيسي
  double get fontSizeH1 => _getResponsiveValue(24, 28, 32, 36);

  /// حجم خط العنوان الثانوي
  double get fontSizeH2 => _getResponsiveValue(20, 24, 28, 32);

  /// حجم خط العنوان الفرعي
  double get fontSizeH3 => _getResponsiveValue(18, 20, 24, 28);

  /// حجم خط العنوان الصغير
  double get fontSizeH4 => _getResponsiveValue(16, 18, 20, 24);

  /// حجم خط النص العادي
  double get fontSizeBody => _getResponsiveValue(14, 15, 16, 18);

  /// حجم خط النص الصغير
  double get fontSizeCaption => _getResponsiveValue(12, 13, 14, 16);

  /// حجم خط الأزرار
  double get fontSizeButton => _getResponsiveValue(14, 15, 16, 18);

  // ==================== الأحجام ====================

  /// ارتفاع الأزرار
  double get buttonHeight => _getResponsiveValue(40, 44, 48, 52);

  /// ارتفاع حقول الإدخال
  double get inputHeight => _getResponsiveValue(48, 52, 56, 60);

  /// ارتفاع شريط التطبيق
  double get appBarHeight => _getResponsiveValue(56, 60, 64, 68);

  /// عرض الشريط الجانبي
  double get sidebarWidth => _getResponsiveValue(200, 220, 250, 300);

  /// حجم الأيقونات الصغيرة
  double get iconSizeS => _getResponsiveValue(16, 18, 20, 22);

  /// حجم الأيقونات المتوسطة
  double get iconSizeM => _getResponsiveValue(20, 22, 24, 28);

  /// حجم الأيقونات الكبيرة
  double get iconSizeL => _getResponsiveValue(24, 28, 32, 36);

  /// حجم الأيقونات الكبيرة جداً
  double get iconSizeXL => _getResponsiveValue(32, 36, 40, 48);

  // ==================== الشبكة ====================

  /// عدد الأعمدة في الشبكة
  int get gridColumns => Breakpoints.getGridColumns(_width);

  /// المسافة بين عناصر الشبكة
  double get gridSpacing => _getResponsiveValue(8, 12, 16, 20);

  /// نسبة العرض إلى الارتفاع للبطاقات
  double get cardAspectRatio => Breakpoints.getCardAspectRatio(_width);

  // ==================== الحدود والزوايا ====================

  /// نصف قطر الحدود الصغير
  double get borderRadiusS => _getResponsiveValue(4, 6, 8, 10);

  /// نصف قطر الحدود المتوسط
  double get borderRadiusM => _getResponsiveValue(8, 10, 12, 14);

  /// نصف قطر الحدود الكبير
  double get borderRadiusL => _getResponsiveValue(12, 14, 16, 20);

  /// نصف قطر الحدود للبطاقات
  double get cardBorderRadius => _getResponsiveValue(8, 10, 12, 16);

  /// نصف قطر الحدود للأزرار
  double get buttonBorderRadius => _getResponsiveValue(6, 8, 10, 12);

  /// عرض الحدود
  double get borderWidth => 1.0;

  // ==================== الظلال ====================

  /// الظل الخفيف
  List<BoxShadow> get shadowLight => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05),
      blurRadius: _getResponsiveValue(2, 3, 4, 5),
      offset: const Offset(0, 1),
    ),
  ];

  /// الظل المتوسط
  List<BoxShadow> get shadowMedium => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: _getResponsiveValue(4, 6, 8, 10),
      offset: const Offset(0, 2),
    ),
  ];

  /// الظل الكبير
  List<BoxShadow> get shadowLarge => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      blurRadius: _getResponsiveValue(8, 12, 16, 20),
      offset: const Offset(0, 4),
    ),
  ];

  // ==================== الحد الأقصى للعرض ====================

  /// الحد الأقصى لعرض المحتوى
  double get maxContentWidth => _getResponsiveValue(
    double.infinity, // لا حد أقصى للهواتف
    600, // حد أقصى للأجهزة اللوحية
    800, // حد أقصى لأجهزة سطح المكتب الصغيرة
    1200, // حد أقصى للشاشات الكبيرة
  );

  /// الحد الأقصى لعرض النماذج
  double get maxFormWidth =>
      _getResponsiveValue(double.infinity, 500, 600, 700);

  // ==================== الدوال المساعدة ====================

  /// الحصول على قيمة متجاوبة بناءً على نوع الجهاز
  double _getResponsiveValue(
    double mobile,
    double tablet,
    double desktop,
    double largeDesktop,
  ) {
    switch (_deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
      case DeviceType.largeDesktop:
        return largeDesktop;
    }
  }

  /// الحصول على EdgeInsets متجاوب
  EdgeInsets getResponsiveEdgeInsets({
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    if (all != null) {
      final value = _getResponsiveValue(all, all * 1.2, all * 1.4, all * 1.6);
      return EdgeInsets.all(value);
    }

    return EdgeInsets.only(
      top: top != null
          ? _getResponsiveValue(top, top * 1.2, top * 1.4, top * 1.6)
          : vertical != null
          ? _getResponsiveValue(
              vertical,
              vertical * 1.2,
              vertical * 1.4,
              vertical * 1.6,
            )
          : 0,
      bottom: bottom != null
          ? _getResponsiveValue(
              bottom,
              bottom * 1.2,
              bottom * 1.4,
              bottom * 1.6,
            )
          : vertical != null
          ? _getResponsiveValue(
              vertical,
              vertical * 1.2,
              vertical * 1.4,
              vertical * 1.6,
            )
          : 0,
      left: left != null
          ? _getResponsiveValue(left, left * 1.2, left * 1.4, left * 1.6)
          : horizontal != null
          ? _getResponsiveValue(
              horizontal,
              horizontal * 1.2,
              horizontal * 1.4,
              horizontal * 1.6,
            )
          : 0,
      right: right != null
          ? _getResponsiveValue(right, right * 1.2, right * 1.4, right * 1.6)
          : horizontal != null
          ? _getResponsiveValue(
              horizontal,
              horizontal * 1.2,
              horizontal * 1.4,
              horizontal * 1.6,
            )
          : 0,
    );
  }
}

/// امتدادات مساعدة للحصول على الأبعاد
extension AppDimensionsExtension on BuildContext {
  /// الحصول على أبعاد التطبيق
  AppDimensions get dimensions => AppDimensions.of(this);
}
