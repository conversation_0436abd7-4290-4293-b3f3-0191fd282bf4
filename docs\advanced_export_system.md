# نظام التصدير المتقدم - Smart Ledger

## 📋 نظرة عامة

تم تطوير نظام التصدير المتقدم في Smart Ledger لتوفير إمكانيات تصدير احترافية ومرنة للتقارير والرسوم البيانية. يدعم النظام تصدير PDF و Excel مع خيارات تخصيص شاملة.

## 🎯 الميزات الرئيسية

### 1. تصدير PDF احترافي
- **تنسيق احترافي**: رؤوس وتذييلات مخصصة
- **دعم الرسوم البيانية**: تضمين الرسوم البيانية عالية الجودة
- **علامة مائية**: حماية إضافية للمستندات
- **تخصيص الألوان**: ألوان مخصصة للرؤوس والعناصر
- **معاينة قبل الحفظ**: إمكانية معاينة PDF قبل التصدير

### 2. تصدير Excel متقدم
- **أوراق متعددة**: ورقة للبيانات وورقة للإحصائيات
- **تنسيق احترافي**: خلايا منسقة مع ألوان وخطوط
- **جداول منظمة**: بيانات منظمة في جداول واضحة
- **إحصائيات تلقائية**: حسابات وملخصات تلقائية
- **دعم الصيغ**: صيغ Excel للحسابات المعقدة

### 3. تصدير الرسوم البيانية
- **جودة عالية**: صور عالية الدقة للرسوم البيانية
- **تنسيقات متعددة**: PNG, JPG
- **تضمين في التقارير**: دمج الرسوم مع التقارير
- **مقارنات بصرية**: صفحات مقارنة للرسوم المتعددة

## 🏗️ البنية التقنية

### الخدمات الأساسية

#### 1. AdvancedExportService
```dart
class AdvancedExportService {
  // تصدير PDF متقدم
  static Future<String?> exportAdvancedPDF({
    required String reportType,
    required dynamic reportData,
    required Map<String, dynamic> filters,
    Uint8List? chartImage,
    ExportCustomization? customization,
    bool includeWatermark = false,
  });

  // تصدير Excel متقدم
  static Future<String?> exportAdvancedExcel({
    required String reportType,
    required dynamic reportData,
    required Map<String, dynamic> filters,
    ExportCustomization? customization,
    bool includeCharts = true,
  });
}
```

#### 2. ChartPdfExportService
```dart
class ChartPdfExportService {
  // التقاط الرسم البياني
  static Future<Uint8List?> captureChartAsImage(GlobalKey chartKey);
  
  // بناء صفحة PDF للرسم البياني
  static pw.Widget buildChartPage({
    required Uint8List chartImage,
    required String chartTitle,
    required String reportType,
    Map<String, dynamic>? chartData,
    Color headerColor = AppColors.primary,
  });
}
```

### واجهات المستخدم

#### 1. AdvancedExportDialog
حوار شامل لخيارات التصدير المتقدمة:
- اختيار تنسيق التصدير (PDF/Excel)
- تخصيص عنوان التقرير
- اختيار لون الرأس
- خيارات المحتوى (رسوم بيانية، إحصائيات)
- خيارات إضافية (علامة مائية)

#### 2. ExportOptionsWidget
ويدجت مدمج لخيارات التصدير السريع:
- أزرار تصدير سريع (PDF, Excel, رسم بياني)
- زر للخيارات المتقدمة
- رسائل حالة التصدير

#### 3. ExportToolbarWidget
ويدجت مبسط لشريط الأدوات:
- قائمة منسدلة لخيارات التصدير
- تكامل سهل مع شاشات التقارير

## 🎨 خيارات التخصيص

### ExportCustomization
```dart
class ExportCustomization {
  final String title;                // عنوان التقرير
  final Color headerColor;           // لون الرأس
  final bool includeCharts;          // تضمين الرسوم البيانية
  final bool includeStatistics;      // تضمين الإحصائيات
  final bool includeWatermark;       // إضافة علامة مائية
  final String? customFooter;        // تذييل مخصص
}
```

### الألوان المدعومة
- `AppColors.primary` - الأزرق الأساسي
- `AppColors.secondary` - الرمادي الثانوي
- `AppColors.success` - الأخضر للنجاح
- `AppColors.warning` - البرتقالي للتحذير
- `AppColors.error` - الأحمر للخطأ
- `AppColors.info` - الأزرق للمعلومات

## 📊 أنواع التقارير المدعومة

### 1. ميزان المراجعة (trial_balance)
- جدول الحسابات مع الأرصدة
- إجماليات المدين والدائن
- رسم بياني للتوزيع

### 2. قائمة الدخل (profit_loss)
- الإيرادات والمصروفات
- صافي الربح/الخسارة
- رسوم بيانية للاتجاهات

### 3. الميزانية العمومية (balance_sheet)
- الأصول والخصوم وحقوق الملكية
- توازن الميزانية
- رسوم بيانية للتوزيع

### 4. تقرير المخزون (inventory_report)
- أرصدة المخزون
- حركة الأصناف
- تحليل القيم

## 🔧 الاستخدام

### التكامل في الشاشات
```dart
// في شاشة التقرير
ExportOptionsWidget(
  reportType: 'trial_balance',
  reportData: reportData,
  filters: currentFilters,
  chartKey: chartGlobalKey,
  onExportComplete: () {
    // إجراءات بعد التصدير
  },
)
```

### التصدير المباشر
```dart
// تصدير PDF مباشر
final filePath = await AdvancedExportService.exportAdvancedPDF(
  reportType: 'profit_loss',
  reportData: data,
  filters: filters,
  customization: ExportCustomization(
    title: 'قائمة الدخل الشهرية',
    headerColor: AppColors.success,
    includeCharts: true,
    includeStatistics: true,
  ),
);
```

## 📁 هيكل الملفات

```
lib/
├── services/
│   ├── advanced_export_service.dart      # الخدمة الرئيسية للتصدير
│   └── chart_pdf_export_service.dart     # خدمة تصدير الرسوم البيانية
├── widgets/
│   ├── advanced_export_dialog.dart       # حوار الخيارات المتقدمة
│   └── export_options_widget.dart        # ويدجت خيارات التصدير
└── test/
    └── advanced_export_test.dart         # اختبارات النظام
```

## 🧪 الاختبارات

### اختبارات الوحدة
- اختبار خدمات التصدير
- اختبار فئات البيانات
- اختبار خيارات التخصيص

### اختبارات الواجهة
- اختبار الحوارات والويدجت
- اختبار التفاعل مع المستخدم
- اختبار سير العمل الكامل

### تشغيل الاختبارات
```bash
flutter test test/advanced_export_test.dart
```

## 🔒 الأمان والخصوصية

### حماية البيانات
- تشفير الملفات المصدرة (اختياري)
- علامات مائية للحماية
- تسجيل عمليات التصدير في سجل المراجعة

### إدارة الأذونات
- طلب أذونات التخزين
- التحقق من صلاحيات المستخدم
- تسجيل العمليات الحساسة

## 🚀 التطوير المستقبلي

### ميزات مخططة
- [ ] دعم تنسيقات إضافية (Word, PowerPoint)
- [ ] قوالب تصدير مخصصة
- [ ] جدولة التصدير التلقائي
- [ ] تصدير مجمع للتقارير المتعددة
- [ ] دعم التوقيع الرقمي
- [ ] تكامل مع خدمات التخزين السحابي

### تحسينات الأداء
- [ ] تحسين سرعة التصدير
- [ ] ضغط الملفات الكبيرة
- [ ] معالجة متوازية للبيانات الكبيرة
- [ ] ذاكرة تخزين مؤقت للقوالب

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع الوثائق التقنية
2. تحقق من سجلات الأخطاء
3. اتصل بفريق الدعم التقني

---

**ملاحظة**: هذا النظام جزء من مشروع Smart Ledger ويتطلب التبعيات المناسبة للعمل بشكل صحيح.
