import 'dart:convert';
import 'dart:async';
import '../services/logging_service.dart';

/// خدمة التخزين المؤقت المتقدمة
/// توفر نظام تخزين مؤقت ذكي مع انتهاء صلاحية وإدارة ذاكرة محسنة
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  final Map<String, CacheEntry> _cache = {};
  final Map<String, Timer> _timers = {};
  
  // إعدادات التخزين المؤقت
  static const Duration defaultTtl = Duration(minutes: 15);
  static const int maxCacheSize = 1000;
  static const int cleanupThreshold = 800;

  /// حفظ قيمة في التخزين المؤقت
  void put<T>(
    String key,
    T value, {
    Duration? ttl,
    CachePriority priority = CachePriority.normal,
    List<String>? tags,
  }) {
    try {
      // تنظيف التخزين المؤقت إذا تجاوز الحد المسموح
      if (_cache.length >= maxCacheSize) {
        _cleanup();
      }

      // إلغاء المؤقت السابق إذا وجد
      _cancelTimer(key);

      final effectiveTtl = ttl ?? defaultTtl;
      final expiryTime = DateTime.now().add(effectiveTtl);

      final entry = CacheEntry<T>(
        key: key,
        value: value,
        createdAt: DateTime.now(),
        expiryTime: expiryTime,
        priority: priority,
        tags: tags ?? [],
        accessCount: 0,
        lastAccessTime: DateTime.now(),
      );

      _cache[key] = entry;

      // إعداد مؤقت لحذف القيمة عند انتهاء الصلاحية
      _timers[key] = Timer(effectiveTtl, () {
        remove(key);
      });

      LoggingService.debug(
        'تم حفظ قيمة في التخزين المؤقت: $key',
        category: 'Cache',
        data: {'ttl': effectiveTtl.inSeconds, 'priority': priority.name},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ قيمة في التخزين المؤقت',
        category: 'Cache',
        data: {'key': key, 'error': e.toString()},
      );
    }
  }

  /// استرجاع قيمة من التخزين المؤقت
  T? get<T>(String key) {
    try {
      final entry = _cache[key];
      if (entry == null) return null;

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().isAfter(entry.expiryTime)) {
        remove(key);
        return null;
      }

      // تحديث إحصائيات الوصول
      entry.accessCount++;
      entry.lastAccessTime = DateTime.now();

      LoggingService.debug(
        'تم استرجاع قيمة من التخزين المؤقت: $key',
        category: 'Cache',
        data: {'accessCount': entry.accessCount},
      );

      return entry.value as T?;
    } catch (e) {
      LoggingService.error(
        'خطأ في استرجاع قيمة من التخزين المؤقت',
        category: 'Cache',
        data: {'key': key, 'error': e.toString()},
      );
      return null;
    }
  }

  /// استرجاع قيمة أو تنفيذ دالة إذا لم توجد
  Future<T> getOrPut<T>(
    String key,
    Future<T> Function() factory, {
    Duration? ttl,
    CachePriority priority = CachePriority.normal,
    List<String>? tags,
  }) async {
    final cached = get<T>(key);
    if (cached != null) return cached;

    try {
      final value = await factory();
      put(key, value, ttl: ttl, priority: priority, tags: tags);
      return value;
    } catch (e) {
      LoggingService.error(
        'خطأ في تنفيذ factory function للتخزين المؤقت',
        category: 'Cache',
        data: {'key': key, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من وجود مفتاح
  bool containsKey(String key) {
    final entry = _cache[key];
    if (entry == null) return false;

    // التحقق من انتهاء الصلاحية
    if (DateTime.now().isAfter(entry.expiryTime)) {
      remove(key);
      return false;
    }

    return true;
  }

  /// حذف قيمة من التخزين المؤقت
  void remove(String key) {
    try {
      _cache.remove(key);
      _cancelTimer(key);

      LoggingService.debug(
        'تم حذف قيمة من التخزين المؤقت: $key',
        category: 'Cache',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف قيمة من التخزين المؤقت',
        category: 'Cache',
        data: {'key': key, 'error': e.toString()},
      );
    }
  }

  /// حذف قيم متعددة بالعلامات
  void removeByTags(List<String> tags) {
    try {
      final keysToRemove = <String>[];
      
      for (final entry in _cache.entries) {
        final cacheEntry = entry.value;
        if (cacheEntry.tags.any((tag) => tags.contains(tag))) {
          keysToRemove.add(entry.key);
        }
      }

      for (final key in keysToRemove) {
        remove(key);
      }

      LoggingService.debug(
        'تم حذف ${keysToRemove.length} قيمة من التخزين المؤقت بالعلامات',
        category: 'Cache',
        data: {'tags': tags, 'removedCount': keysToRemove.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف قيم بالعلامات من التخزين المؤقت',
        category: 'Cache',
        data: {'tags': tags, 'error': e.toString()},
      );
    }
  }

  /// حذف القيم المنتهية الصلاحية
  void removeExpired() {
    try {
      final now = DateTime.now();
      final expiredKeys = <String>[];

      for (final entry in _cache.entries) {
        if (now.isAfter(entry.value.expiryTime)) {
          expiredKeys.add(entry.key);
        }
      }

      for (final key in expiredKeys) {
        remove(key);
      }

      LoggingService.debug(
        'تم حذف ${expiredKeys.length} قيمة منتهية الصلاحية',
        category: 'Cache',
        data: {'expiredCount': expiredKeys.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف القيم المنتهية الصلاحية',
        category: 'Cache',
        data: {'error': e.toString()},
      );
    }
  }

  /// مسح جميع القيم
  void clear() {
    try {
      final count = _cache.length;
      
      // إلغاء جميع المؤقتات
      for (final timer in _timers.values) {
        timer.cancel();
      }
      
      _cache.clear();
      _timers.clear();

      LoggingService.info(
        'تم مسح التخزين المؤقت بالكامل',
        category: 'Cache',
        data: {'clearedCount': count},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في مسح التخزين المؤقت',
        category: 'Cache',
        data: {'error': e.toString()},
      );
    }
  }

  /// تنظيف التخزين المؤقت (حذف القيم الأقل استخداماً)
  void _cleanup() {
    try {
      if (_cache.length <= cleanupThreshold) return;

      // ترتيب القيم حسب الأولوية وعدد الوصول
      final entries = _cache.entries.toList();
      entries.sort((a, b) {
        final entryA = a.value;
        final entryB = b.value;

        // مقارنة الأولوية أولاً
        final priorityComparison = entryA.priority.index.compareTo(entryB.priority.index);
        if (priorityComparison != 0) return priorityComparison;

        // ثم مقارنة عدد الوصول
        final accessComparison = entryA.accessCount.compareTo(entryB.accessCount);
        if (accessComparison != 0) return accessComparison;

        // أخيراً مقارنة وقت آخر وصول
        return entryA.lastAccessTime.compareTo(entryB.lastAccessTime);
      });

      // حذف النصف الأول (الأقل أهمية)
      final toRemove = entries.length ~/ 2;
      for (int i = 0; i < toRemove; i++) {
        remove(entries[i].key);
      }

      LoggingService.info(
        'تم تنظيف التخزين المؤقت',
        category: 'Cache',
        data: {'removedCount': toRemove, 'remainingCount': _cache.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف التخزين المؤقت',
        category: 'Cache',
        data: {'error': e.toString()},
      );
    }
  }

  /// إلغاء مؤقت
  void _cancelTimer(String key) {
    final timer = _timers.remove(key);
    timer?.cancel();
  }

  /// الحصول على إحصائيات التخزين المؤقت
  CacheStatistics getStatistics() {
    final now = DateTime.now();
    int expiredCount = 0;
    int totalAccessCount = 0;
    final priorityCounts = <CachePriority, int>{};

    for (final entry in _cache.values) {
      if (now.isAfter(entry.expiryTime)) {
        expiredCount++;
      }
      totalAccessCount += entry.accessCount;
      priorityCounts[entry.priority] = (priorityCounts[entry.priority] ?? 0) + 1;
    }

    return CacheStatistics(
      totalEntries: _cache.length,
      expiredEntries: expiredCount,
      totalAccessCount: totalAccessCount,
      priorityCounts: priorityCounts,
      memoryUsageEstimate: _estimateMemoryUsage(),
    );
  }

  /// تقدير استخدام الذاكرة
  int _estimateMemoryUsage() {
    int totalSize = 0;
    for (final entry in _cache.values) {
      // تقدير تقريبي لحجم البيانات
      try {
        final jsonString = jsonEncode(entry.value);
        totalSize += jsonString.length * 2; // UTF-16 encoding
      } catch (e) {
        // إذا فشل التحويل إلى JSON، استخدم تقدير افتراضي
        totalSize += 100; // 100 bytes per entry
      }
    }
    return totalSize;
  }

  /// الحصول على جميع المفاتيح
  List<String> get keys => _cache.keys.toList();

  /// الحصول على حجم التخزين المؤقت
  int get size => _cache.length;

  /// التحقق من كون التخزين المؤقت فارغ
  bool get isEmpty => _cache.isEmpty;

  /// التحقق من كون التخزين المؤقت غير فارغ
  bool get isNotEmpty => _cache.isNotEmpty;
}

/// مدخل التخزين المؤقت
class CacheEntry<T> {
  final String key;
  final T value;
  final DateTime createdAt;
  final DateTime expiryTime;
  final CachePriority priority;
  final List<String> tags;
  int accessCount;
  DateTime lastAccessTime;

  CacheEntry({
    required this.key,
    required this.value,
    required this.createdAt,
    required this.expiryTime,
    required this.priority,
    required this.tags,
    required this.accessCount,
    required this.lastAccessTime,
  });

  /// التحقق من انتهاء الصلاحية
  bool get isExpired => DateTime.now().isAfter(expiryTime);

  /// الوقت المتبقي حتى انتهاء الصلاحية
  Duration get timeToExpiry => expiryTime.difference(DateTime.now());

  @override
  String toString() {
    return 'CacheEntry(key: $key, priority: $priority, accessCount: $accessCount, expired: $isExpired)';
  }
}

/// أولوية التخزين المؤقت
enum CachePriority {
  low,
  normal,
  high,
  critical,
}

/// إحصائيات التخزين المؤقت
class CacheStatistics {
  final int totalEntries;
  final int expiredEntries;
  final int totalAccessCount;
  final Map<CachePriority, int> priorityCounts;
  final int memoryUsageEstimate;

  const CacheStatistics({
    required this.totalEntries,
    required this.expiredEntries,
    required this.totalAccessCount,
    required this.priorityCounts,
    required this.memoryUsageEstimate,
  });

  /// معدل الوصول
  double get averageAccessCount => 
      totalEntries > 0 ? totalAccessCount / totalEntries : 0.0;

  /// نسبة القيم المنتهية الصلاحية
  double get expiredRatio => 
      totalEntries > 0 ? expiredEntries / totalEntries : 0.0;

  /// استخدام الذاكرة بالكيلوبايت
  double get memoryUsageKB => memoryUsageEstimate / 1024.0;

  @override
  String toString() {
    return 'CacheStatistics(entries: $totalEntries, expired: $expiredEntries, memory: ${memoryUsageKB.toStringAsFixed(2)} KB)';
  }
}
