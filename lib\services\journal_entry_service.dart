import '../database/database_helper.dart';
import '../models/journal_entry.dart';
import '../constants/app_constants.dart';
import 'account_service.dart';
import 'validation_service.dart';
import '../exceptions/validation_exception.dart';

class JournalEntryService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final AccountService _accountService = AccountService();

  Future<List<JournalEntry>> getAllJournalEntries() async {
    final db = await _databaseHelper.database;

    // استعلام واحد محسن مع JOIN لتجنب N+1 Query
    final result = await db.rawQuery('''
      SELECT
        je.id, je.entry_number, je.entry_date, je.description, je.type,
        je.total_debit, je.total_credit, je.currency_id, je.is_posted,
        je.created_at, je.updated_at,
        jed.id as detail_id, jed.account_id, jed.debit_amount,
        jed.credit_amount, jed.description as detail_description
      FROM ${AppConstants.journalEntriesTable} je
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON je.id = jed.journal_entry_id
      ORDER BY je.entry_date DESC, je.id DESC, jed.id ASC
    ''');

    return _groupJournalEntries(result);
  }

  // دالة مساعدة لتجميع نتائج الاستعلام
  List<JournalEntry> _groupJournalEntries(List<Map<String, dynamic>> result) {
    Map<int, JournalEntry> entriesMap = {};

    for (final row in result) {
      final entryId = row['id'] as int;

      if (!entriesMap.containsKey(entryId)) {
        entriesMap[entryId] = JournalEntry.fromMap({
          'id': row['id'],
          'entry_number': row['entry_number'],
          'entry_date': row['entry_date'],
          'description': row['description'],
          'type': row['type'],
          'total_debit': row['total_debit'],
          'total_credit': row['total_credit'],
          'currency_id': row['currency_id'],
          'is_posted': row['is_posted'],
          'created_at': row['created_at'],
          'updated_at': row['updated_at'],
        }).copyWith(details: []);
      }

      if (row['detail_id'] != null) {
        final detail = JournalEntryDetail.fromMap({
          'id': row['detail_id'],
          'journal_entry_id': entryId,
          'account_id': row['account_id'],
          'debit_amount': row['debit_amount'],
          'credit_amount': row['credit_amount'],
          'description': row['detail_description'],
        });

        entriesMap[entryId] = entriesMap[entryId]!.copyWith(
          details: [...entriesMap[entryId]!.details, detail],
        );
      }
    }

    return entriesMap.values.toList();
  }

  Future<List<JournalEntry>> getJournalEntriesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;

    // استعلام محسن مع JOIN لتجنب N+1 Query
    final result = await db.rawQuery(
      '''
      SELECT
        je.id, je.entry_number, je.entry_date, je.description, je.type,
        je.total_debit, je.total_credit, je.currency_id, je.is_posted,
        je.created_at, je.updated_at,
        jed.id as detail_id, jed.account_id, jed.debit_amount,
        jed.credit_amount, jed.description as detail_description
      FROM ${AppConstants.journalEntriesTable} je
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON je.id = jed.journal_entry_id
      WHERE je.entry_date BETWEEN ? AND ?
      ORDER BY je.entry_date DESC, je.id DESC, jed.id ASC
    ''',
      [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    return _groupJournalEntries(result);
  }

  Future<JournalEntry?> getJournalEntryById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.journalEntriesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final entry = JournalEntry.fromMap(maps.first);
      final details = await getJournalEntryDetails(id);
      return entry.copyWith(details: details);
    }
    return null;
  }

  Future<List<JournalEntryDetail>> getJournalEntryDetails(
    int journalEntryId,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.journalEntryDetailsTable,
      where: 'journal_entry_id = ?',
      whereArgs: [journalEntryId],
      orderBy: 'id ASC',
    );

    return List.generate(maps.length, (i) {
      return JournalEntryDetail.fromMap(maps[i]);
    });
  }

  Future<int> insertJournalEntry(JournalEntry journalEntry) async {
    final db = await _databaseHelper.database;

    // التحقق من صحة رقم القيد
    final entryNumberValidation = ValidationService.validateJournalEntryNumber(
      journalEntry.entryNumber,
    );
    if (!entryNumberValidation.isValid) {
      throw ValidationException(entryNumberValidation.errorMessage!);
    }

    // التحقق من توازن القيد باستخدام ValidationService
    final detailsData = journalEntry.details
        .map(
          (detail) => {
            'debit_amount': detail.debitAmount,
            'credit_amount': detail.creditAmount,
          },
        )
        .toList();

    final balanceValidation = ValidationService.validateJournalEntryBalance(
      detailsData,
    );
    if (!balanceValidation.isValid) {
      throw ValidationException(balanceValidation.errorMessage!);
    }

    // التحقق من عدم تكرار رقم القيد
    final existingEntry = await getJournalEntryByNumber(
      journalEntry.entryNumber,
    );
    if (existingEntry != null) {
      throw ValidationException.duplicate(
        'رقم القيد',
        journalEntry.entryNumber,
      );
    }

    return await db.transaction((txn) async {
      // إدراج القيد الرئيسي
      final entryData = journalEntry.toMap();
      entryData.remove('id');
      final entryId = await txn.insert(
        AppConstants.journalEntriesTable,
        entryData,
      );

      // إدراج تفاصيل القيد
      for (final detail in journalEntry.details) {
        final detailData = detail.copyWith(journalEntryId: entryId).toMap();
        detailData.remove('id');
        await txn.insert(AppConstants.journalEntryDetailsTable, detailData);
      }

      return entryId;
    });
  }

  Future<int> updateJournalEntry(JournalEntry journalEntry) async {
    final db = await _databaseHelper.database;

    // التحقق من توازن القيد
    if (!journalEntry.isBalanced) {
      throw Exception(
        'القيد غير متوازن - يجب أن يكون مجموع المدين = مجموع الدائن',
      );
    }

    // التحقق من عدم ترحيل القيد
    if (journalEntry.isPosted) {
      throw Exception('لا يمكن تعديل قيد مرحل');
    }

    // التحقق من عدم تكرار رقم القيد مع قيود أخرى
    final existingEntry = await getJournalEntryByNumber(
      journalEntry.entryNumber,
    );
    if (existingEntry != null && existingEntry.id != journalEntry.id) {
      throw Exception('رقم القيد موجود مسبقاً');
    }

    return await db.transaction((txn) async {
      // تحديث القيد الرئيسي
      final entryData = journalEntry
          .copyWith(updatedAt: DateTime.now())
          .toMap();
      await txn.update(
        AppConstants.journalEntriesTable,
        entryData,
        where: 'id = ?',
        whereArgs: [journalEntry.id],
      );

      // حذف التفاصيل القديمة
      await txn.delete(
        AppConstants.journalEntryDetailsTable,
        where: 'journal_entry_id = ?',
        whereArgs: [journalEntry.id],
      );

      // إدراج التفاصيل الجديدة
      for (final detail in journalEntry.details) {
        final detailData = detail
            .copyWith(journalEntryId: journalEntry.id!)
            .toMap();
        detailData.remove('id');
        await txn.insert(AppConstants.journalEntryDetailsTable, detailData);
      }

      return 1;
    });
  }

  Future<int> deleteJournalEntry(int id) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم ترحيل القيد
    final entry = await getJournalEntryById(id);
    if (entry == null) {
      throw Exception('القيد غير موجود');
    }

    if (entry.isPosted) {
      throw Exception('لا يمكن حذف قيد مرحل');
    }

    return await db.transaction((txn) async {
      // حذف التفاصيل أولاً
      await txn.delete(
        AppConstants.journalEntryDetailsTable,
        where: 'journal_entry_id = ?',
        whereArgs: [id],
      );

      // حذف القيد الرئيسي
      return await txn.delete(
        AppConstants.journalEntriesTable,
        where: 'id = ?',
        whereArgs: [id],
      );
    });
  }

  Future<int> postJournalEntry(int id) async {
    final db = await _databaseHelper.database;

    final entry = await getJournalEntryById(id);
    if (entry == null) {
      throw Exception('القيد غير موجود');
    }

    if (entry.isPosted) {
      throw Exception('القيد مرحل مسبقاً');
    }

    if (!entry.isBalanced) {
      throw Exception('القيد غير متوازن');
    }

    return await db.transaction((txn) async {
      // ترحيل القيد
      await txn.update(
        AppConstants.journalEntriesTable,
        {'is_posted': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [id],
      );

      // تحديث أرصدة الحسابات
      for (final detail in entry.details) {
        if (detail.debitAmount > 0) {
          await _accountService.updateAccountBalance(
            detail.accountId,
            detail.debitAmount,
            true,
          );
        }
        if (detail.creditAmount > 0) {
          await _accountService.updateAccountBalance(
            detail.accountId,
            detail.creditAmount,
            false,
          );
        }
      }

      return 1;
    });
  }

  Future<JournalEntry?> getJournalEntryByNumber(String entryNumber) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.journalEntriesTable,
      where: 'entry_number = ?',
      whereArgs: [entryNumber],
    );

    if (maps.isNotEmpty) {
      final entry = JournalEntry.fromMap(maps.first);
      final details = await getJournalEntryDetails(entry.id!);
      return entry.copyWith(details: details);
    }
    return null;
  }

  Future<String> generateEntryNumber() async {
    final db = await _databaseHelper.database;
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');

    // البحث عن آخر رقم قيد في الشهر الحالي
    final result = await db.rawQuery('''
      SELECT entry_number FROM ${AppConstants.journalEntriesTable}
      WHERE entry_number LIKE '$year$month%'
      ORDER BY entry_number DESC
      LIMIT 1
    ''');

    int nextNumber = 1;
    if (result.isNotEmpty) {
      final lastNumber = result.first['entry_number'] as String;
      final numberPart = lastNumber.substring(6); // إزالة YYYYMM
      nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
    }

    return '$year$month${nextNumber.toString().padLeft(4, '0')}';
  }

  Future<List<JournalEntry>> searchJournalEntries(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.journalEntriesTable,
      where: 'entry_number LIKE ? OR description LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%'],
      orderBy: 'entry_date DESC, id DESC',
    );

    List<JournalEntry> entries = [];
    for (final map in maps) {
      final entry = JournalEntry.fromMap(map);
      final details = await getJournalEntryDetails(entry.id!);
      entries.add(entry.copyWith(details: details));
    }

    return entries;
  }

  /// إنشاء قيد محاسبي باستخدام transaction موجود
  Future<int> insertJournalEntryWithTransaction(
    JournalEntry journalEntry,
    dynamic txn,
  ) async {
    // التحقق من صحة رقم القيد
    final entryNumberValidation = ValidationService.validateJournalEntryNumber(
      journalEntry.entryNumber,
    );
    if (!entryNumberValidation.isValid) {
      throw ValidationException(entryNumberValidation.errorMessage!);
    }

    // التحقق من توازن القيد باستخدام ValidationService
    final detailsData = journalEntry.details
        .map(
          (detail) => {
            'debit_amount': detail.debitAmount,
            'credit_amount': detail.creditAmount,
          },
        )
        .toList();

    final balanceValidation = ValidationService.validateJournalEntryBalance(
      detailsData,
    );
    if (!balanceValidation.isValid) {
      throw ValidationException(balanceValidation.errorMessage!);
    }

    // التحقق من عدم تكرار رقم القيد
    final existingEntry = await getJournalEntryByNumber(
      journalEntry.entryNumber,
    );
    if (existingEntry != null) {
      throw ValidationException.duplicate(
        'رقم القيد',
        journalEntry.entryNumber,
      );
    }

    // إدراج القيد الرئيسي
    final entryData = journalEntry.toMap();
    entryData.remove('id');
    final entryId = await txn.insert(
      AppConstants.journalEntriesTable,
      entryData,
    );

    // إدراج تفاصيل القيد
    for (final detail in journalEntry.details) {
      final detailData = detail.copyWith(journalEntryId: entryId).toMap();
      detailData.remove('id');
      await txn.insert(AppConstants.journalEntryDetailsTable, detailData);
    }

    return entryId;
  }
}
