import 'permission.dart';

/// نموذج الدور (Role)
class Role {
  final int? id;
  final String name;
  final String description;
  final bool isActive;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<PermissionType> permissions;

  const Role({
    this.id,
    required this.name,
    required this.description,
    required this.isActive,
    required this.isDefault,
    required this.createdAt,
    required this.updatedAt,
    required this.permissions,
  });

  /// إنشاء دور من Map
  factory Role.fromMap(Map<String, dynamic> map) {
    // تحويل الصلاحيات من نص إلى قائمة
    List<PermissionType> permissionsList = [];
    if (map['permissions'] != null && map['permissions'] is String) {
      final permissionsString = map['permissions'] as String;
      if (permissionsString.isNotEmpty) {
        final permissionNames = permissionsString.split(',');
        permissionsList = permissionNames
            .map((name) => PermissionType.values.firstWhere(
                  (e) => e.toString().split('.').last == name.trim(),
                  orElse: () => PermissionType.viewAccounts,
                ))
            .toList();
      }
    }

    return Role(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      isActive: (map['is_active'] as int) == 1,
      isDefault: (map['is_default'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      permissions: permissionsList,
    );
  }

  /// تحويل الدور إلى Map
  Map<String, dynamic> toMap() {
    // تحويل الصلاحيات إلى نص مفصول بفواصل
    final permissionsString = permissions
        .map((permission) => permission.toString().split('.').last)
        .join(',');

    return {
      'id': id,
      'name': name,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'permissions': permissionsString,
    };
  }

  /// إنشاء نسخة محدثة من الدور
  Role copyWith({
    int? id,
    String? name,
    String? description,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<PermissionType>? permissions,
  }) {
    return Role(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      permissions: permissions ?? this.permissions,
    );
  }

  /// التحقق من وجود صلاحية معينة
  bool hasPermission(PermissionType permission) {
    return permissions.contains(permission);
  }

  /// إضافة صلاحية
  Role addPermission(PermissionType permission) {
    if (!hasPermission(permission)) {
      final newPermissions = List<PermissionType>.from(permissions)
        ..add(permission);
      return copyWith(
        permissions: newPermissions,
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  /// إزالة صلاحية
  Role removePermission(PermissionType permission) {
    if (hasPermission(permission)) {
      final newPermissions = List<PermissionType>.from(permissions)
        ..remove(permission);
      return copyWith(
        permissions: newPermissions,
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  /// الحصول على عدد الصلاحيات
  int get permissionCount => permissions.length;

  /// التحقق من صحة البيانات
  bool isValid() {
    return name.isNotEmpty && description.isNotEmpty;
  }

  @override
  String toString() {
    return 'Role{id: $id, name: $name, permissions: ${permissions.length}}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Role &&
           other.id == id &&
           other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

/// مساعد للأدوار الافتراضية
class RoleHelper {
  /// الحصول على الأدوار الافتراضية
  static List<Role> getDefaultRoles() {
    final now = DateTime.now();

    return [
      // دور المدير العام
      Role(
        name: 'مدير عام',
        description: 'صلاحيات كاملة لجميع وظائف النظام',
        isActive: true,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
        permissions: PermissionType.values, // جميع الصلاحيات
      ),

      // دور المحاسب
      Role(
        name: 'محاسب',
        description: 'صلاحيات المحاسبة الأساسية',
        isActive: true,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
        permissions: [
          PermissionType.viewAccounts,
          PermissionType.addAccounts,
          PermissionType.editAccounts,
          PermissionType.viewJournalEntries,
          PermissionType.addJournalEntries,
          PermissionType.editJournalEntries,
          PermissionType.postJournalEntries,
          PermissionType.viewReports,
          PermissionType.exportReports,
        ],
      ),

      // دور أمين المخزن
      Role(
        name: 'أمين مخزن',
        description: 'صلاحيات إدارة المخزون والأصناف',
        isActive: true,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
        permissions: [
          PermissionType.viewItems,
          PermissionType.addItems,
          PermissionType.editItems,
          PermissionType.viewInventoryReports,
          PermissionType.viewInvoices,
          PermissionType.addInvoices,
          PermissionType.editInvoices,
        ],
      ),

      // دور موظف المبيعات
      Role(
        name: 'موظف مبيعات',
        description: 'صلاحيات إدارة العملاء والمبيعات',
        isActive: true,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
        permissions: [
          PermissionType.viewCustomers,
          PermissionType.addCustomers,
          PermissionType.editCustomers,
          PermissionType.viewInvoices,
          PermissionType.addInvoices,
          PermissionType.editInvoices,
          PermissionType.viewItems,
          PermissionType.viewCustomerReports,
        ],
      ),

      // دور موظف المشتريات
      Role(
        name: 'موظف مشتريات',
        description: 'صلاحيات إدارة الموردين والمشتريات',
        isActive: true,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
        permissions: [
          PermissionType.viewSuppliers,
          PermissionType.addSuppliers,
          PermissionType.editSuppliers,
          PermissionType.viewInvoices,
          PermissionType.addInvoices,
          PermissionType.editInvoices,
          PermissionType.viewItems,
          PermissionType.viewSupplierReports,
        ],
      ),

      // دور مستخدم عادي
      Role(
        name: 'مستخدم عادي',
        description: 'صلاحيات العرض فقط',
        isActive: true,
        isDefault: true,
        createdAt: now,
        updatedAt: now,
        permissions: [
          PermissionType.viewAccounts,
          PermissionType.viewJournalEntries,
          PermissionType.viewCustomers,
          PermissionType.viewSuppliers,
          PermissionType.viewItems,
          PermissionType.viewInvoices,
          PermissionType.viewReports,
        ],
      ),
    ];
  }

  /// الحصول على دور افتراضي حسب الاسم
  static Role? getDefaultRoleByName(String name) {
    try {
      return getDefaultRoles().firstWhere((role) => role.name == name);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على دور المدير العام
  static Role getAdminRole() {
    return getDefaultRoles().first; // المدير العام هو الأول
  }

  /// الحصول على دور المستخدم العادي
  static Role getUserRole() {
    return getDefaultRoles().last; // المستخدم العادي هو الأخير
  }
}
