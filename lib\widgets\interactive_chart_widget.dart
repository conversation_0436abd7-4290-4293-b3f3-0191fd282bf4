import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../services/chart_service.dart';
import '../services/chart_export_service.dart';

/// مكون الرسم البياني التفاعلي
class InteractiveChartWidget extends StatefulWidget {
  final String reportType;
  final dynamic data;
  final ChartType initialChartType;
  final ChartConfiguration? configuration;
  final Function(ChartType)? onChartTypeChanged;

  const InteractiveChartWidget({
    super.key,
    required this.reportType,
    required this.data,
    this.initialChartType = ChartType.bar,
    this.configuration,
    this.onChartTypeChanged,
  });

  @override
  State<InteractiveChartWidget> createState() => _InteractiveChartWidgetState();
}

class _InteractiveChartWidgetState extends State<InteractiveChartWidget>
    with TickerProviderStateMixin {
  late ChartType _currentChartType;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isFullScreen = false;
  final GlobalKey _chartKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _currentChartType = widget.initialChartType;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _changeChartType(ChartType newType) {
    if (newType != _currentChartType) {
      setState(() {
        _currentChartType = newType;
      });
      widget.onChartTypeChanged?.call(newType);

      // إعادة تشغيل الرسوم المتحركة
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      _showFullScreenChart();
    }
  }

  void _showFullScreenChart() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FullScreenChartScreen(
          reportType: widget.reportType,
          data: widget.data,
          chartType: _currentChartType,
          configuration: widget.configuration,
        ),
        fullscreenDialog: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with controls
          _buildChartHeader(),

          // Chart content
          Expanded(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: RepaintBoundary(
                  key: _chartKey,
                  child: ChartService.createChart(
                    reportType: widget.reportType,
                    data: widget.data,
                    chartType: _currentChartType,
                    configuration: widget.configuration,
                  ),
                ),
              ),
            ),
          ),

          // Chart type selector
          _buildChartTypeSelector(),
        ],
      ),
    );
  }

  Widget _buildChartHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.bar_chart, color: AppColors.primary),
          const SizedBox(width: 8),
          Text(
            'الرسم البياني',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.save_alt),
            onPressed: _saveMainChart,
            tooltip: 'حفظ الرسم البياني',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareMainChart,
            tooltip: 'مشاركة الرسم البياني',
          ),
          IconButton(
            icon: const Icon(Icons.fullscreen),
            onPressed: _toggleFullScreen,
            tooltip: 'عرض بملء الشاشة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _animationController.reset();
              _animationController.forward();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildChartTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildChartTypeButton(ChartType.bar, Icons.bar_chart, 'أعمدة'),
          _buildChartTypeButton(ChartType.line, Icons.show_chart, 'خطي'),
          _buildChartTypeButton(ChartType.pie, Icons.pie_chart, 'دائري'),
          _buildChartTypeButton(ChartType.area, Icons.area_chart, 'منطقة'),
        ],
      ),
    );
  }

  Widget _buildChartTypeButton(ChartType type, IconData icon, String label) {
    final isSelected = _currentChartType == type;

    return InkWell(
      onTap: () => _changeChartType(type),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : Colors.grey.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey[600],
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[600],
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// حفظ الرسم البياني في الوضع العادي
  void _saveMainChart() async {
    try {
      final filePath = await ChartExportService.saveChartAsImage(
        repaintBoundaryKey: _chartKey,
        reportType: widget.reportType,
      );

      if (filePath != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ الرسم البياني بنجاح في: $filePath'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حفظ الرسم البياني'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الرسم البياني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// مشاركة الرسم البياني في الوضع العادي
  void _shareMainChart() async {
    try {
      final success = await ChartExportService.shareChart(
        repaintBoundaryKey: _chartKey,
        reportType: widget.reportType,
        customText: 'رسم بياني من Smart Ledger - ${widget.reportType}',
      );

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في مشاركة الرسم البياني'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة الرسم البياني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// شاشة الرسم البياني بملء الشاشة
class FullScreenChartScreen extends StatefulWidget {
  final String reportType;
  final dynamic data;
  final ChartType chartType;
  final ChartConfiguration? configuration;

  const FullScreenChartScreen({
    super.key,
    required this.reportType,
    required this.data,
    required this.chartType,
    this.configuration,
  });

  @override
  State<FullScreenChartScreen> createState() => _FullScreenChartScreenState();
}

class _FullScreenChartScreenState extends State<FullScreenChartScreen> {
  late ChartType _currentChartType;
  final GlobalKey _fullScreenChartKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _currentChartType = widget.chartType;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('الرسم البياني - ملء الشاشة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save_alt),
            onPressed: _saveChart,
            tooltip: 'حفظ الرسم البياني',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareChart,
            tooltip: 'مشاركة',
          ),
        ],
      ),
      body: Column(
        children: [
          // Chart type selector
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildFullScreenChartTypeButton(
                  ChartType.bar,
                  Icons.bar_chart,
                  'أعمدة',
                ),
                _buildFullScreenChartTypeButton(
                  ChartType.line,
                  Icons.show_chart,
                  'خطي',
                ),
                _buildFullScreenChartTypeButton(
                  ChartType.pie,
                  Icons.pie_chart,
                  'دائري',
                ),
                _buildFullScreenChartTypeButton(
                  ChartType.area,
                  Icons.area_chart,
                  'منطقة',
                ),
              ],
            ),
          ),

          // Chart
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: RepaintBoundary(
                key: _fullScreenChartKey,
                child: ChartService.createChart(
                  reportType: widget.reportType,
                  data: widget.data,
                  chartType: _currentChartType,
                  configuration: widget.configuration,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullScreenChartTypeButton(
    ChartType type,
    IconData icon,
    String label,
  ) {
    final isSelected = _currentChartType == type;

    return InkWell(
      onTap: () {
        setState(() {
          _currentChartType = type;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey[800],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveChart() async {
    try {
      final filePath = await ChartExportService.saveChartAsImage(
        repaintBoundaryKey: _fullScreenChartKey,
        reportType: widget.reportType,
      );

      if (filePath != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ الرسم البياني بنجاح في: $filePath'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حفظ الرسم البياني'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الرسم البياني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareChart() async {
    try {
      final success = await ChartExportService.shareChart(
        repaintBoundaryKey: _fullScreenChartKey,
        reportType: widget.reportType,
        customText: 'رسم بياني من Smart Ledger - ${widget.reportType}',
      );

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في مشاركة الرسم البياني'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة الرسم البياني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// مكون اختيار نوع الرسم البياني
class ChartTypePicker extends StatelessWidget {
  final ChartType selectedType;
  final Function(ChartType) onTypeChanged;
  final List<ChartType> availableTypes;

  const ChartTypePicker({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
    this.availableTypes = const [
      ChartType.bar,
      ChartType.line,
      ChartType.pie,
      ChartType.area,
    ],
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نوع الرسم البياني',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: availableTypes.map((type) {
              return _buildTypeChip(type);
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeChip(ChartType type) {
    final isSelected = selectedType == type;
    final typeInfo = _getChartTypeInfo(type);

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(typeInfo['icon'], size: 16),
          const SizedBox(width: 4),
          Text(typeInfo['label']),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onTypeChanged(type);
        }
      },
      selectedColor: AppColors.primary.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  Map<String, dynamic> _getChartTypeInfo(ChartType type) {
    switch (type) {
      case ChartType.bar:
        return {'icon': Icons.bar_chart, 'label': 'أعمدة'};
      case ChartType.line:
        return {'icon': Icons.show_chart, 'label': 'خطي'};
      case ChartType.pie:
        return {'icon': Icons.pie_chart, 'label': 'دائري'};
      case ChartType.area:
        return {'icon': Icons.area_chart, 'label': 'منطقة'};
      case ChartType.scatter:
        return {'icon': Icons.scatter_plot, 'label': 'نقطي'};
      case ChartType.radar:
        return {'icon': Icons.radar, 'label': 'رادار'};
    }
  }
}
