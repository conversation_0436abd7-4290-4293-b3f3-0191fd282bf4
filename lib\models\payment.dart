/// نموذج الدفعات وإدارة المدفوعات الجزئية
/// يحتوي على جميع أنواع الدفعات وطرق الدفع المختلفة
library;

/// طرق الدفع المختلفة
enum PaymentMethod {
  /// نقد
  cash('cash', 'نقد', 'دفع نقدي'),
  
  /// شيك
  check('check', 'شيك', 'دفع بشيك'),
  
  /// تحويل بنكي
  bankTransfer('bank_transfer', 'تحويل بنكي', 'تحويل بنكي'),
  
  /// بطاقة ائتمان
  creditCard('credit_card', 'بطاقة ائتمان', 'دفع ببطاقة ائتمان'),
  
  /// بطاقة خصم
  debitCard('debit_card', 'بطاقة خصم', 'دفع ببطاقة خصم'),
  
  /// محفظة إلكترونية
  eWallet('e_wallet', 'محفظة إلكترونية', 'دفع بمحفظة إلكترونية'),
  
  /// مقاصة
  clearing('clearing', 'مقاصة', 'مقاصة مع فواتير أخرى');

  const PaymentMethod(this.code, this.displayName, this.description);

  final String code;
  final String displayName;
  final String description;

  static PaymentMethod fromCode(String code) {
    return PaymentMethod.values.firstWhere(
      (method) => method.code == code,
      orElse: () => PaymentMethod.cash,
    );
  }

  /// التحقق من الحاجة لمعلومات إضافية
  bool get requiresAdditionalInfo {
    return [
      PaymentMethod.check,
      PaymentMethod.bankTransfer,
      PaymentMethod.creditCard,
      PaymentMethod.debitCard,
    ].contains(this);
  }

  /// الحصول على أيقونة طريقة الدفع
  String get iconCode {
    switch (this) {
      case PaymentMethod.cash:
        return 'money';
      case PaymentMethod.check:
        return 'receipt';
      case PaymentMethod.bankTransfer:
        return 'account_balance';
      case PaymentMethod.creditCard:
        return 'credit_card';
      case PaymentMethod.debitCard:
        return 'payment';
      case PaymentMethod.eWallet:
        return 'account_balance_wallet';
      case PaymentMethod.clearing:
        return 'compare_arrows';
    }
  }
}

/// حالة الدفعة
enum PaymentStatus {
  /// معلقة - في انتظار التأكيد
  pending('pending', 'معلقة'),
  
  /// مؤكدة - تم تأكيد الدفعة
  confirmed('confirmed', 'مؤكدة'),
  
  /// مرفوضة - تم رفض الدفعة
  rejected('rejected', 'مرفوضة'),
  
  /// ملغاة - تم إلغاء الدفعة
  cancelled('cancelled', 'ملغاة');

  const PaymentStatus(this.code, this.displayName);

  final String code;
  final String displayName;

  static PaymentStatus fromCode(String code) {
    return PaymentStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => PaymentStatus.pending,
    );
  }

  /// الحصول على لون الحالة
  String get colorCode {
    switch (this) {
      case PaymentStatus.pending:
        return '#FF9800'; // برتقالي
      case PaymentStatus.confirmed:
        return '#4CAF50'; // أخضر
      case PaymentStatus.rejected:
        return '#F44336'; // أحمر
      case PaymentStatus.cancelled:
        return '#9E9E9E'; // رمادي
    }
  }
}

/// نموذج الدفعة
class Payment {
  final int? id;
  final int invoiceId;
  final double amount;
  final PaymentMethod method;
  final PaymentStatus status;
  final DateTime paymentDate;
  final String? reference; // رقم الشيك، رقم التحويل، إلخ
  final String? bankName;
  final String? accountNumber;
  final String? notes;
  final String? userId; // المستخدم الذي سجل الدفعة
  final DateTime createdAt;
  final DateTime updatedAt;

  Payment({
    this.id,
    required this.invoiceId,
    required this.amount,
    required this.method,
    this.status = PaymentStatus.pending,
    DateTime? paymentDate,
    this.reference,
    this.bankName,
    this.accountNumber,
    this.notes,
    this.userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : paymentDate = paymentDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'amount': amount,
      'method': method.code,
      'status': status.code,
      'payment_date': paymentDate.toIso8601String(),
      'reference': reference,
      'bank_name': bankName,
      'account_number': accountNumber,
      'notes': notes,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id']?.toInt(),
      invoiceId: map['invoice_id']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      method: PaymentMethod.fromCode(map['method'] ?? 'cash'),
      status: PaymentStatus.fromCode(map['status'] ?? 'pending'),
      paymentDate: DateTime.parse(map['payment_date'] ?? DateTime.now().toIso8601String()),
      reference: map['reference'],
      bankName: map['bank_name'],
      accountNumber: map['account_number'],
      notes: map['notes'],
      userId: map['user_id'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Payment copyWith({
    int? id,
    int? invoiceId,
    double? amount,
    PaymentMethod? method,
    PaymentStatus? status,
    DateTime? paymentDate,
    String? reference,
    String? bankName,
    String? accountNumber,
    String? notes,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Payment(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      status: status ?? this.status,
      paymentDate: paymentDate ?? this.paymentDate,
      reference: reference ?? this.reference,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من صحة الدفعة
  bool get isValid {
    return amount > 0 && invoiceId > 0;
  }

  /// التحقق من إمكانية تعديل الدفعة
  bool get canEdit {
    return status == PaymentStatus.pending;
  }

  /// التحقق من إمكانية حذف الدفعة
  bool get canDelete {
    return [PaymentStatus.pending, PaymentStatus.cancelled].contains(status);
  }

  /// الحصول على وصف مختصر للدفعة
  String get shortDescription {
    final methodName = method.displayName;
    final amountText = amount.toStringAsFixed(2);
    
    if (reference != null && reference!.isNotEmpty) {
      return '$methodName - $amountText ل.س ($reference)';
    }
    
    return '$methodName - $amountText ل.س';
  }
}

/// ملخص الدفعات للفاتورة
class PaymentSummary {
  final int invoiceId;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final List<Payment> payments;
  final Payment? lastPayment;

  PaymentSummary({
    required this.invoiceId,
    required this.totalAmount,
    required this.paidAmount,
    required this.payments,
  }) : remainingAmount = totalAmount - paidAmount,
       lastPayment = payments.isNotEmpty 
           ? payments.reduce((a, b) => a.paymentDate.isAfter(b.paymentDate) ? a : b)
           : null;

  /// نسبة المدفوع
  double get paidPercentage {
    if (totalAmount <= 0) return 0.0;
    return (paidAmount / totalAmount) * 100;
  }

  /// التحقق من اكتمال الدفع
  bool get isFullyPaid {
    return remainingAmount <= 0.01; // هامش خطأ صغير
  }

  /// التحقق من وجود دفعات جزئية
  bool get hasPartialPayments {
    return paidAmount > 0.01 && !isFullyPaid;
  }

  /// عدد الدفعات المؤكدة
  int get confirmedPaymentsCount {
    return payments.where((p) => p.status == PaymentStatus.confirmed).length;
  }

  /// عدد الدفعات المعلقة
  int get pendingPaymentsCount {
    return payments.where((p) => p.status == PaymentStatus.pending).length;
  }

  /// الحصول على الدفعات حسب الحالة
  List<Payment> getPaymentsByStatus(PaymentStatus status) {
    return payments.where((p) => p.status == status).toList();
  }

  /// الحصول على الدفعات حسب طريقة الدفع
  List<Payment> getPaymentsByMethod(PaymentMethod method) {
    return payments.where((p) => p.method == method).toList();
  }

  /// الحصول على إجمالي المبلغ حسب طريقة الدفع
  double getTotalByMethod(PaymentMethod method) {
    return getPaymentsByMethod(method)
        .where((p) => p.status == PaymentStatus.confirmed)
        .fold(0.0, (sum, payment) => sum + payment.amount);
  }

  /// الحصول على تفاصيل الدفع بالطرق المختلفة
  Map<PaymentMethod, double> getPaymentBreakdown() {
    final breakdown = <PaymentMethod, double>{};
    
    for (final method in PaymentMethod.values) {
      final total = getTotalByMethod(method);
      if (total > 0) {
        breakdown[method] = total;
      }
    }
    
    return breakdown;
  }
}
