/// نماذج إعدادات الطباعة المتقدمة
/// يحتوي على جميع الإعدادات المتقدمة للطباعة مثل الشعار والتوقيعات والألوان المخصصة
library;

import 'package:pdf/pdf.dart';

/// إعدادات الشعار
class LogoSettings {
  final String? logoPath;
  final bool showLogo;
  final LogoPosition position;
  final double width;
  final double height;
  final double opacity;

  const LogoSettings({
    this.logoPath,
    this.showLogo = true,
    this.position = LogoPosition.topLeft,
    this.width = 100,
    this.height = 60,
    this.opacity = 1.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'logoPath': logoPath,
      'showLogo': showLogo,
      'position': position.name,
      'width': width,
      'height': height,
      'opacity': opacity,
    };
  }

  factory LogoSettings.fromMap(Map<String, dynamic> map) {
    return LogoSettings(
      logoPath: map['logoPath'],
      showLogo: map['showLogo'] ?? true,
      position: LogoPosition.values.firstWhere(
        (pos) => pos.name == map['position'],
        orElse: () => LogoPosition.topLeft,
      ),
      width: map['width']?.toDouble() ?? 100.0,
      height: map['height']?.toDouble() ?? 60.0,
      opacity: map['opacity']?.toDouble() ?? 1.0,
    );
  }

  LogoSettings copyWith({
    String? logoPath,
    bool? showLogo,
    LogoPosition? position,
    double? width,
    double? height,
    double? opacity,
  }) {
    return LogoSettings(
      logoPath: logoPath ?? this.logoPath,
      showLogo: showLogo ?? this.showLogo,
      position: position ?? this.position,
      width: width ?? this.width,
      height: height ?? this.height,
      opacity: opacity ?? this.opacity,
    );
  }
}

/// مواضع الشعار
enum LogoPosition {
  topLeft('أعلى اليسار'),
  topCenter('أعلى الوسط'),
  topRight('أعلى اليمين'),
  centerLeft('وسط اليسار'),
  center('الوسط'),
  centerRight('وسط اليمين'),
  bottomLeft('أسفل اليسار'),
  bottomCenter('أسفل الوسط'),
  bottomRight('أسفل اليمين');

  const LogoPosition(this.displayName);
  final String displayName;
}

/// إعدادات التوقيع
class SignatureSettings {
  final String? signaturePath;
  final bool showSignature;
  final String? signerName;
  final String? signerTitle;
  final SignaturePosition position;
  final double width;
  final double height;

  const SignatureSettings({
    this.signaturePath,
    this.showSignature = false,
    this.signerName,
    this.signerTitle,
    this.position = SignaturePosition.bottomRight,
    this.width = 150,
    this.height = 75,
  });

  Map<String, dynamic> toMap() {
    return {
      'signaturePath': signaturePath,
      'showSignature': showSignature,
      'signerName': signerName,
      'signerTitle': signerTitle,
      'position': position.name,
      'width': width,
      'height': height,
    };
  }

  factory SignatureSettings.fromMap(Map<String, dynamic> map) {
    return SignatureSettings(
      signaturePath: map['signaturePath'],
      showSignature: map['showSignature'] ?? false,
      signerName: map['signerName'],
      signerTitle: map['signerTitle'],
      position: SignaturePosition.values.firstWhere(
        (pos) => pos.name == map['position'],
        orElse: () => SignaturePosition.bottomRight,
      ),
      width: map['width']?.toDouble() ?? 150.0,
      height: map['height']?.toDouble() ?? 75.0,
    );
  }

  SignatureSettings copyWith({
    String? signaturePath,
    bool? showSignature,
    String? signerName,
    String? signerTitle,
    SignaturePosition? position,
    double? width,
    double? height,
  }) {
    return SignatureSettings(
      signaturePath: signaturePath ?? this.signaturePath,
      showSignature: showSignature ?? this.showSignature,
      signerName: signerName ?? this.signerName,
      signerTitle: signerTitle ?? this.signerTitle,
      position: position ?? this.position,
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }
}

/// مواضع التوقيع
enum SignaturePosition {
  bottomLeft('أسفل اليسار'),
  bottomCenter('أسفل الوسط'),
  bottomRight('أسفل اليمين'),
  afterSummary('بعد الملخص'),
  beforeTerms('قبل الشروط');

  const SignaturePosition(this.displayName);
  final String displayName;
}

/// إعدادات العلامة المائية
class WatermarkSettings {
  final bool showWatermark;
  final String text;
  final double opacity;
  final double rotation;
  final WatermarkPosition position;
  final PdfColor color;
  final double fontSize;

  const WatermarkSettings({
    this.showWatermark = false,
    this.text = 'مسودة',
    this.opacity = 0.1,
    this.rotation = -45,
    this.position = WatermarkPosition.center,
    this.color = PdfColors.grey,
    this.fontSize = 72,
  });

  Map<String, dynamic> toMap() {
    return {
      'showWatermark': showWatermark,
      'text': text,
      'opacity': opacity,
      'rotation': rotation,
      'position': position.name,
      'color': _colorToHex(color),
      'fontSize': fontSize,
    };
  }

  factory WatermarkSettings.fromMap(Map<String, dynamic> map) {
    return WatermarkSettings(
      showWatermark: map['showWatermark'] ?? false,
      text: map['text'] ?? 'مسودة',
      opacity: map['opacity']?.toDouble() ?? 0.1,
      rotation: map['rotation']?.toDouble() ?? -45.0,
      position: WatermarkPosition.values.firstWhere(
        (pos) => pos.name == map['position'],
        orElse: () => WatermarkPosition.center,
      ),
      color: _hexToColor(map['color'] ?? '#808080'),
      fontSize: map['fontSize']?.toDouble() ?? 72.0,
    );
  }

  WatermarkSettings copyWith({
    bool? showWatermark,
    String? text,
    double? opacity,
    double? rotation,
    WatermarkPosition? position,
    PdfColor? color,
    double? fontSize,
  }) {
    return WatermarkSettings(
      showWatermark: showWatermark ?? this.showWatermark,
      text: text ?? this.text,
      opacity: opacity ?? this.opacity,
      rotation: rotation ?? this.rotation,
      position: position ?? this.position,
      color: color ?? this.color,
      fontSize: fontSize ?? this.fontSize,
    );
  }

  static String _colorToHex(PdfColor color) {
    final r = (color.red * 255).round();
    final g = (color.green * 255).round();
    final b = (color.blue * 255).round();
    return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
  }

  static PdfColor _hexToColor(String hex) {
    final hexCode = hex.replaceAll('#', '');
    final r = int.parse(hexCode.substring(0, 2), radix: 16) / 255;
    final g = int.parse(hexCode.substring(2, 4), radix: 16) / 255;
    final b = int.parse(hexCode.substring(4, 6), radix: 16) / 255;
    return PdfColor(r, g, b);
  }
}

/// مواضع العلامة المائية
enum WatermarkPosition {
  center('الوسط'),
  topLeft('أعلى اليسار'),
  topRight('أعلى اليمين'),
  bottomLeft('أسفل اليسار'),
  bottomRight('أسفل اليمين'),
  diagonal('قطري');

  const WatermarkPosition(this.displayName);
  final String displayName;
}

/// إعدادات الطباعة المتقدمة الشاملة
class AdvancedPrintSettings {
  final LogoSettings logoSettings;
  final SignatureSettings signatureSettings;
  final WatermarkSettings watermarkSettings;
  final bool showPageNumbers;
  final bool showPrintDate;
  final bool showQRCode;
  final String? customFooterText;
  final bool enableColorPrinting;
  final PrintQuality quality;

  const AdvancedPrintSettings({
    this.logoSettings = const LogoSettings(),
    this.signatureSettings = const SignatureSettings(),
    this.watermarkSettings = const WatermarkSettings(),
    this.showPageNumbers = true,
    this.showPrintDate = true,
    this.showQRCode = false,
    this.customFooterText,
    this.enableColorPrinting = true,
    this.quality = PrintQuality.high,
  });

  Map<String, dynamic> toMap() {
    return {
      'logoSettings': logoSettings.toMap(),
      'signatureSettings': signatureSettings.toMap(),
      'watermarkSettings': watermarkSettings.toMap(),
      'showPageNumbers': showPageNumbers,
      'showPrintDate': showPrintDate,
      'showQRCode': showQRCode,
      'customFooterText': customFooterText,
      'enableColorPrinting': enableColorPrinting,
      'quality': quality.name,
    };
  }

  factory AdvancedPrintSettings.fromMap(Map<String, dynamic> map) {
    return AdvancedPrintSettings(
      logoSettings: LogoSettings.fromMap(map['logoSettings'] ?? {}),
      signatureSettings: SignatureSettings.fromMap(map['signatureSettings'] ?? {}),
      watermarkSettings: WatermarkSettings.fromMap(map['watermarkSettings'] ?? {}),
      showPageNumbers: map['showPageNumbers'] ?? true,
      showPrintDate: map['showPrintDate'] ?? true,
      showQRCode: map['showQRCode'] ?? false,
      customFooterText: map['customFooterText'],
      enableColorPrinting: map['enableColorPrinting'] ?? true,
      quality: PrintQuality.values.firstWhere(
        (q) => q.name == map['quality'],
        orElse: () => PrintQuality.high,
      ),
    );
  }

  AdvancedPrintSettings copyWith({
    LogoSettings? logoSettings,
    SignatureSettings? signatureSettings,
    WatermarkSettings? watermarkSettings,
    bool? showPageNumbers,
    bool? showPrintDate,
    bool? showQRCode,
    String? customFooterText,
    bool? enableColorPrinting,
    PrintQuality? quality,
  }) {
    return AdvancedPrintSettings(
      logoSettings: logoSettings ?? this.logoSettings,
      signatureSettings: signatureSettings ?? this.signatureSettings,
      watermarkSettings: watermarkSettings ?? this.watermarkSettings,
      showPageNumbers: showPageNumbers ?? this.showPageNumbers,
      showPrintDate: showPrintDate ?? this.showPrintDate,
      showQRCode: showQRCode ?? this.showQRCode,
      customFooterText: customFooterText ?? this.customFooterText,
      enableColorPrinting: enableColorPrinting ?? this.enableColorPrinting,
      quality: quality ?? this.quality,
    );
  }

  /// الإعدادات الافتراضية
  static const AdvancedPrintSettings defaultSettings = AdvancedPrintSettings();

  /// إعدادات للمسودات
  static AdvancedPrintSettings get draftSettings => const AdvancedPrintSettings(
    watermarkSettings: WatermarkSettings(
      showWatermark: true,
      text: 'مسودة',
      opacity: 0.2,
    ),
    enableColorPrinting: false,
    quality: PrintQuality.medium,
  );

  /// إعدادات رسمية
  static AdvancedPrintSettings get officialSettings => const AdvancedPrintSettings(
    logoSettings: LogoSettings(showLogo: true),
    signatureSettings: SignatureSettings(showSignature: true),
    showQRCode: true,
    quality: PrintQuality.high,
  );
}

/// جودة الطباعة
enum PrintQuality {
  low('منخفضة'),
  medium('متوسطة'),
  high('عالية'),
  ultra('فائقة');

  const PrintQuality(this.displayName);
  final String displayName;

  /// الحصول على DPI حسب الجودة
  double get dpi {
    switch (this) {
      case PrintQuality.low:
        return 150;
      case PrintQuality.medium:
        return 300;
      case PrintQuality.high:
        return 600;
      case PrintQuality.ultra:
        return 1200;
    }
  }
}
