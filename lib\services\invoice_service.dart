import '../database/database_helper.dart';
import '../models/invoice.dart';
import '../constants/app_constants.dart';

class InvoiceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<Invoice>> getAllInvoices() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      orderBy: 'invoice_date DESC, id DESC',
    );

    List<Invoice> invoices = [];
    for (final map in maps) {
      final invoice = Invoice.fromMap(map);
      final items = await getInvoiceItems(invoice.id!);
      invoices.add(invoice.copyWith(items: items));
    }

    return invoices;
  }

  Future<List<Invoice>> getInvoicesByType(String type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      where: 'type = ?',
      whereArgs: [type],
      orderBy: 'invoice_date DESC, id DESC',
    );

    List<Invoice> invoices = [];
    for (final map in maps) {
      final invoice = Invoice.fromMap(map);
      final items = await getInvoiceItems(invoice.id!);
      invoices.add(invoice.copyWith(items: items));
    }

    return invoices;
  }

  Future<List<Invoice>> getInvoicesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      where: 'invoice_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'invoice_date DESC, id DESC',
    );

    List<Invoice> invoices = [];
    for (final map in maps) {
      final invoice = Invoice.fromMap(map);
      final items = await getInvoiceItems(invoice.id!);
      invoices.add(invoice.copyWith(items: items));
    }

    return invoices;
  }

  Future<Invoice?> getInvoiceById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final invoice = Invoice.fromMap(maps.first);
      final items = await getInvoiceItems(id);
      return invoice.copyWith(items: items);
    }
    return null;
  }

  Future<List<InvoiceItem>> getInvoiceItems(int invoiceId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoiceItemsTable,
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'id ASC',
    );

    return List.generate(maps.length, (i) {
      return InvoiceItem.fromMap(maps[i]);
    });
  }

  Future<int> insertInvoice(Invoice invoice) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم تكرار رقم الفاتورة
    final existingInvoice = await getInvoiceByNumber(invoice.invoiceNumber);
    if (existingInvoice != null) {
      throw Exception('رقم الفاتورة موجود مسبقاً');
    }

    return await db.transaction((txn) async {
      // إدراج الفاتورة الرئيسية
      final invoiceData = invoice.toMap();
      invoiceData.remove('id');
      final invoiceId = await txn.insert(
        AppConstants.invoicesTable,
        invoiceData,
      );

      // إدراج عناصر الفاتورة
      for (final item in invoice.items) {
        final itemData = item.copyWith(invoiceId: invoiceId).toMap();
        itemData.remove('id');
        await txn.insert(AppConstants.invoiceItemsTable, itemData);
      }

      return invoiceId;
    });
  }

  Future<int> updateInvoice(Invoice invoice) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم تكرار رقم الفاتورة مع فواتير أخرى
    final existingInvoice = await getInvoiceByNumber(invoice.invoiceNumber);
    if (existingInvoice != null && existingInvoice.id != invoice.id) {
      throw Exception('رقم الفاتورة موجود مسبقاً');
    }

    return await db.transaction((txn) async {
      // تحديث الفاتورة الرئيسية
      final invoiceData = invoice.copyWith(updatedAt: DateTime.now()).toMap();
      await txn.update(
        AppConstants.invoicesTable,
        invoiceData,
        where: 'id = ?',
        whereArgs: [invoice.id],
      );

      // حذف العناصر القديمة
      await txn.delete(
        AppConstants.invoiceItemsTable,
        where: 'invoice_id = ?',
        whereArgs: [invoice.id],
      );

      // إدراج العناصر الجديدة
      for (final item in invoice.items) {
        final itemData = item.copyWith(invoiceId: invoice.id!).toMap();
        itemData.remove('id');
        await txn.insert(AppConstants.invoiceItemsTable, itemData);
      }

      return 1;
    });
  }

  Future<int> deleteInvoice(int id) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // حذف العناصر أولاً
      await txn.delete(
        AppConstants.invoiceItemsTable,
        where: 'invoice_id = ?',
        whereArgs: [id],
      );

      // حذف الفاتورة الرئيسية
      return await txn.delete(
        AppConstants.invoicesTable,
        where: 'id = ?',
        whereArgs: [id],
      );
    });
  }

  Future<Invoice?> getInvoiceByNumber(String invoiceNumber) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      where: 'invoice_number = ?',
      whereArgs: [invoiceNumber],
    );

    if (maps.isNotEmpty) {
      final invoice = Invoice.fromMap(maps.first);
      final items = await getInvoiceItems(invoice.id!);
      return invoice.copyWith(items: items);
    }
    return null;
  }

  Future<String> generateInvoiceNumber(String type) async {
    final db = await _databaseHelper.database;
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');

    String prefix;
    switch (type) {
      case AppConstants.invoiceTypeSale:
        prefix = 'S';
        break;
      case AppConstants.invoiceTypePurchase:
        prefix = 'P';
        break;
      case AppConstants.invoiceTypeSaleReturn:
        prefix = 'SR';
        break;
      case AppConstants.invoiceTypePurchaseReturn:
        prefix = 'PR';
        break;
      default:
        prefix = 'INV';
    }

    // البحث عن آخر رقم فاتورة من نفس النوع في الشهر الحالي
    final result = await db.rawQuery(
      '''
      SELECT invoice_number FROM ${AppConstants.invoicesTable}
      WHERE type = ? AND invoice_number LIKE '$prefix$year$month%'
      ORDER BY invoice_number DESC
      LIMIT 1
    ''',
      [type],
    );

    int nextNumber = 1;
    if (result.isNotEmpty) {
      final lastNumber = result.first['invoice_number'] as String;
      final numberPart = lastNumber.substring(
        prefix.length + 6,
      ); // إزالة PREFIX + YYYYMM
      nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
    }

    return '$prefix$year$month${nextNumber.toString().padLeft(4, '0')}';
  }

  Future<List<Invoice>> searchInvoices(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      where: 'invoice_number LIKE ? OR notes LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%'],
      orderBy: 'invoice_date DESC, id DESC',
    );

    List<Invoice> invoices = [];
    for (final map in maps) {
      final invoice = Invoice.fromMap(map);
      final items = await getInvoiceItems(invoice.id!);
      invoices.add(invoice.copyWith(items: items));
    }

    return invoices;
  }

  Future<Map<String, double>> getInvoicesSummary() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT 
        type,
        COUNT(*) as count,
        SUM(total_amount) as total_amount,
        SUM(paid_amount) as paid_amount
      FROM ${AppConstants.invoicesTable}
      GROUP BY type
    ''');

    Map<String, double> summary = {};
    for (final row in result) {
      final type = row['type'] as String;
      summary['${type}_count'] = (row['count'] as num?)?.toDouble() ?? 0.0;
      summary['${type}_total'] =
          (row['total_amount'] as num?)?.toDouble() ?? 0.0;
      summary['${type}_paid'] = (row['paid_amount'] as num?)?.toDouble() ?? 0.0;
    }

    return summary;
  }

  Future<List<Invoice>> getUnpaidInvoices() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.invoicesTable,
      where: 'remaining_amount > 0 AND status != ?',
      whereArgs: ['cancelled'],
      orderBy: 'invoice_date ASC',
    );

    List<Invoice> invoices = [];
    for (final map in maps) {
      final invoice = Invoice.fromMap(map);
      final items = await getInvoiceItems(invoice.id!);
      invoices.add(invoice.copyWith(items: items));
    }

    return invoices;
  }
}
