/// خدمة إدارة حالات الفواتير وتتبع التغييرات
/// تتعامل مع تغيير حالات الفواتير وحفظ سجل التغييرات
library;

import '../database/database_helper.dart';
import '../models/invoice.dart';
import '../models/invoice_status.dart';
import '../services/invoice_service.dart';
import '../services/logging_service.dart';

class InvoiceStatusService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();

  /// تغيير حالة الفاتورة
  Future<void> changeInvoiceStatus({
    required int invoiceId,
    required InvoiceStatus newStatus,
    required StatusChangeReason reason,
    String? notes,
    String? userId,
  }) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // الحصول على الفاتورة الحالية
      final invoice = await _invoiceService.getInvoiceById(invoiceId);
      if (invoice == null) {
        throw Exception('الفاتورة غير موجودة');
      }

      final currentStatus = invoice.status;

      // التحقق من صحة التغيير
      if (!InvoiceStatusManager.validateStatusChange(
        fromStatus: currentStatus,
        toStatus: newStatus,
        reason: reason,
      )) {
        throw Exception(
          'لا يمكن تغيير حالة الفاتورة من ${currentStatus.displayName} إلى ${newStatus.displayName}',
        );
      }

      // تحديث حالة الفاتورة
      await txn.update(
        'invoices',
        {
          'status': newStatus.code,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [invoiceId],
      );

      // إضافة سجل التغيير
      await txn.insert('invoice_status_history', {
        'invoice_id': invoiceId,
        'from_status': currentStatus.code,
        'to_status': newStatus.code,
        'reason': reason.code,
        'notes': notes,
        'user_id': userId,
        'changed_at': DateTime.now().toIso8601String(),
      });

      // تنفيذ إجراءات إضافية حسب الحالة الجديدة
      await _executeStatusChangeActions(invoice, newStatus, txn);
    });
  }

  /// تأكيد الفاتورة (من مسودة إلى مؤكدة)
  Future<void> confirmInvoice(
    int invoiceId, {
    String? userId,
    String? notes,
  }) async {
    await changeInvoiceStatus(
      invoiceId: invoiceId,
      newStatus: InvoiceStatus.confirmed,
      reason: StatusChangeReason.confirmation,
      notes: notes,
      userId: userId,
    );
  }

  /// إلغاء الفاتورة
  Future<void> cancelInvoice(
    int invoiceId, {
    String? userId,
    String? notes,
  }) async {
    await changeInvoiceStatus(
      invoiceId: invoiceId,
      newStatus: InvoiceStatus.cancelled,
      reason: StatusChangeReason.cancellation,
      notes: notes,
      userId: userId,
    );
  }

  /// تحديث حالة الفواتير المتأخرة تلقائياً
  Future<void> updateOverdueInvoices() async {
    final db = await _databaseHelper.database;

    // البحث عن الفواتير المؤكدة أو المدفوعة جزئياً التي تجاوزت تاريخ الاستحقاق
    final overdueInvoices = await db.rawQuery(
      '''
      SELECT id FROM invoices 
      WHERE (status = ? OR status = ?) 
      AND due_date IS NOT NULL 
      AND due_date < ?
    ''',
      [
        InvoiceStatus.confirmed.code,
        InvoiceStatus.partiallyPaid.code,
        DateTime.now().toIso8601String(),
      ],
    );

    for (final row in overdueInvoices) {
      final invoiceId = row['id'] as int;
      try {
        await changeInvoiceStatus(
          invoiceId: invoiceId,
          newStatus: InvoiceStatus.overdue,
          reason: StatusChangeReason.overdueDate,
          notes: 'تحديث تلقائي - تجاوز تاريخ الاستحقاق',
        );
      } catch (e) {
        // تجاهل الأخطاء وتابع مع الفواتير الأخرى
        LoggingService.error(
          'خطأ في تحديث الفاتورة $invoiceId: $e',
          category: 'InvoiceStatusService',
          data: {'invoiceId': invoiceId, 'error': e.toString()},
        );
      }
    }
  }

  /// الحصول على سجل تغييرات حالة الفاتورة
  Future<List<InvoiceStatusHistory>> getInvoiceStatusHistory(
    int invoiceId,
  ) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'invoice_status_history',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'changed_at DESC',
    );

    return maps.map((map) => InvoiceStatusHistory.fromMap(map)).toList();
  }

  /// الحصول على إحصائيات الحالات
  Future<Map<InvoiceStatus, int>> getStatusStatistics() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT status, COUNT(*) as count 
      FROM invoices 
      GROUP BY status
    ''');

    final statistics = <InvoiceStatus, int>{};

    for (final row in result) {
      final status = InvoiceStatus.fromCode(row['status'] as String);
      final count = row['count'] as int;
      statistics[status] = count;
    }

    return statistics;
  }

  /// الحصول على الفواتير حسب الحالة
  Future<List<Invoice>> getInvoicesByStatus(
    InvoiceStatus status, {
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'invoices',
      where: 'status = ?',
      whereArgs: [status.code],
      orderBy: 'invoice_date DESC, id DESC',
      limit: limit,
      offset: offset,
    );

    List<Invoice> invoices = [];
    for (final map in maps) {
      final invoice = Invoice.fromMap(map);
      final items = await _invoiceService.getInvoiceItems(invoice.id!);
      invoices.add(invoice.copyWith(items: items));
    }

    return invoices;
  }

  /// الحصول على الفواتير المتأخرة
  Future<List<Invoice>> getOverdueInvoices({int? limit, int? offset}) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'invoices',
      where:
          'status = ? OR (due_date IS NOT NULL AND due_date < ? AND status IN (?, ?))',
      whereArgs: [
        InvoiceStatus.overdue.code,
        DateTime.now().toIso8601String(),
        InvoiceStatus.confirmed.code,
        InvoiceStatus.partiallyPaid.code,
      ],
      orderBy: 'due_date ASC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => Invoice.fromMap(map)).toList();
  }

  /// الحصول على الفواتير المستحقة قريباً
  Future<List<Invoice>> getUpcomingDueInvoices({
    int daysAhead = 7,
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    final futureDate = DateTime.now().add(Duration(days: daysAhead));

    final maps = await db.query(
      'invoices',
      where: '''
        due_date IS NOT NULL 
        AND due_date BETWEEN ? AND ? 
        AND status IN (?, ?)
      ''',
      whereArgs: [
        DateTime.now().toIso8601String(),
        futureDate.toIso8601String(),
        InvoiceStatus.confirmed.code,
        InvoiceStatus.partiallyPaid.code,
      ],
      orderBy: 'due_date ASC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => Invoice.fromMap(map)).toList();
  }

  /// تنفيذ إجراءات إضافية عند تغيير الحالة
  Future<void> _executeStatusChangeActions(
    Invoice invoice,
    InvoiceStatus newStatus,
    dynamic txn,
  ) async {
    switch (newStatus) {
      case InvoiceStatus.confirmed:
        // عند تأكيد الفاتورة، تحديث المخزون
        await _updateInventoryOnConfirmation(invoice, txn);
        break;

      case InvoiceStatus.cancelled:
        // عند إلغاء الفاتورة، إرجاع المخزون
        await _revertInventoryOnCancellation(invoice, txn);
        break;

      default:
        // لا توجد إجراءات إضافية للحالات الأخرى
        break;
    }
  }

  /// تحديث المخزون عند تأكيد الفاتورة
  Future<void> _updateInventoryOnConfirmation(
    Invoice invoice,
    dynamic txn,
  ) async {
    if (invoice.type == 'sale' || invoice.type == 'purchase_return') {
      // تقليل الكمية من المخزون
      for (final item in invoice.items) {
        await txn.rawUpdate(
          '''
          UPDATE items 
          SET quantity = quantity - ?, updated_at = ?
          WHERE id = ?
        ''',
          [item.quantity, DateTime.now().toIso8601String(), item.itemId],
        );
      }
    } else if (invoice.type == 'purchase' || invoice.type == 'sale_return') {
      // زيادة الكمية في المخزون
      for (final item in invoice.items) {
        await txn.rawUpdate(
          '''
          UPDATE items 
          SET quantity = quantity + ?, updated_at = ?
          WHERE id = ?
        ''',
          [item.quantity, DateTime.now().toIso8601String(), item.itemId],
        );
      }
    }
  }

  /// إرجاع المخزون عند إلغاء الفاتورة
  Future<void> _revertInventoryOnCancellation(
    Invoice invoice,
    dynamic txn,
  ) async {
    // عكس العملية التي تمت عند التأكيد
    if (invoice.type == 'sale' || invoice.type == 'purchase_return') {
      // إرجاع الكمية للمخزون
      for (final item in invoice.items) {
        await txn.rawUpdate(
          '''
          UPDATE items 
          SET quantity = quantity + ?, updated_at = ?
          WHERE id = ?
        ''',
          [item.quantity, DateTime.now().toIso8601String(), item.itemId],
        );
      }
    } else if (invoice.type == 'purchase' || invoice.type == 'sale_return') {
      // تقليل الكمية من المخزون
      for (final item in invoice.items) {
        await txn.rawUpdate(
          '''
          UPDATE items 
          SET quantity = quantity - ?, updated_at = ?
          WHERE id = ?
        ''',
          [item.quantity, DateTime.now().toIso8601String(), item.itemId],
        );
      }
    }
  }

  /// الحصول على ملخص حالات الفواتير للوحة التحكم
  Future<Map<String, dynamic>> getStatusSummary() async {
    final db = await _databaseHelper.database;

    // إحصائيات الحالات
    final statusStats = await getStatusStatistics();

    // إجمالي قيمة الفواتير حسب الحالة
    final valueResult = await db.rawQuery('''
      SELECT status, SUM(total_amount) as total_value, SUM(remaining_amount) as remaining_value
      FROM invoices 
      GROUP BY status
    ''');

    final valueByStatus = <InvoiceStatus, Map<String, double>>{};
    for (final row in valueResult) {
      final status = InvoiceStatus.fromCode(row['status'] as String);
      valueByStatus[status] = {
        'total': row['total_value'] as double? ?? 0.0,
        'remaining': row['remaining_value'] as double? ?? 0.0,
      };
    }

    // الفواتير المتأخرة
    final overdueCount = await db.rawQuery(
      '''
      SELECT COUNT(*) as count, SUM(remaining_amount) as total_overdue
      FROM invoices 
      WHERE status = ? OR (due_date IS NOT NULL AND due_date < ? AND status IN (?, ?))
    ''',
      [
        InvoiceStatus.overdue.code,
        DateTime.now().toIso8601String(),
        InvoiceStatus.confirmed.code,
        InvoiceStatus.partiallyPaid.code,
      ],
    );

    return {
      'statusCounts': statusStats,
      'valueByStatus': valueByStatus,
      'overdueCount': overdueCount.first['count'] as int? ?? 0,
      'overdueValue': overdueCount.first['total_overdue'] as double? ?? 0.0,
    };
  }
}
