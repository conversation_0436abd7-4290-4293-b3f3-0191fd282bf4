import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/widgets/item_selection_dialog.dart';
import 'package:smart_ledger/models/item.dart';
import 'package:smart_ledger/constants/app_constants.dart';

void main() {
  group('ItemSelectionDialog Tests', () {
    setUp(() {
      // إعداد البيانات التجريبية إذا لزم الأمر
    });

    testWidgets('should display dialog with correct title', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const ItemSelectionDialog(
                    title: 'اختيار صنف للفاتورة',
                    invoiceType: AppConstants.invoiceTypeSale,
                  ),
                ),
                child: const Text('Open Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Dialog'));
      await tester.pumpAndSettle();

      expect(find.text('اختيار صنف للفاتورة'), findsOneWidget);
      expect(find.byIcon(Icons.inventory_2), findsOneWidget);
    });

    testWidgets('should show search field and filters', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const ItemSelectionDialog(
                    invoiceType: AppConstants.invoiceTypeSale,
                  ),
                ),
                child: const Text('Open Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Dialog'));
      await tester.pumpAndSettle();

      // التحقق من وجود حقل البحث
      expect(find.byType(TextField), findsAtLeast(1));
      expect(find.text('البحث بالاسم أو الكود...'), findsOneWidget);

      // التحقق من وجود أزرار الفلترة
      expect(find.text('متاح'), findsOneWidget);
      expect(find.text('مخزون منخفض'), findsOneWidget);
      expect(find.text('الكل'), findsOneWidget);
    });

    testWidgets('should show quantity input for sales invoice', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const ItemSelectionDialog(
                    invoiceType: AppConstants.invoiceTypeSale,
                    showQuantityInput: true,
                    showPriceInput: true,
                  ),
                ),
                child: const Text('Open Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Dialog'));
      await tester.pumpAndSettle();

      // يجب أن تظهر حقول الإدخال عند اختيار صنف
      // هذا الاختبار يتطلب محاكاة اختيار صنف أولاً
    });

    testWidgets('should validate quantity input', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const ItemSelectionDialog(
                    invoiceType: AppConstants.invoiceTypeSale,
                  ),
                ),
                child: const Text('Open Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Dialog'));
      await tester.pumpAndSettle();

      // محاولة إضافة بدون اختيار صنف
      await tester.tap(find.text('إضافة'));
      await tester.pumpAndSettle();

      // يجب أن تظهر رسالة خطأ
      expect(find.text('يرجى اختيار صنف'), findsOneWidget);
    });

    testWidgets('should close dialog when cancel is pressed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const ItemSelectionDialog(
                    invoiceType: AppConstants.invoiceTypeSale,
                  ),
                ),
                child: const Text('Open Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Dialog'));
      await tester.pumpAndSettle();

      expect(find.byType(ItemSelectionDialog), findsOneWidget);

      await tester.tap(find.text('إلغاء'));
      await tester.pumpAndSettle();

      expect(find.byType(ItemSelectionDialog), findsNothing);
    });

    testWidgets('should close dialog when close icon is pressed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const ItemSelectionDialog(
                    invoiceType: AppConstants.invoiceTypeSale,
                  ),
                ),
                child: const Text('Open Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Dialog'));
      await tester.pumpAndSettle();

      expect(find.byType(ItemSelectionDialog), findsOneWidget);

      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      expect(find.byType(ItemSelectionDialog), findsNothing);
    });

    testWidgets('should filter items based on search term', (
      WidgetTester tester,
    ) async {
      // هذا الاختبار يتطلب محاكاة خدمة الأصناف
      // يمكن تطويره لاحقاً مع إضافة mock للخدمات
    });

    testWidgets('should calculate totals correctly', (
      WidgetTester tester,
    ) async {
      // اختبار حساب الإجماليات
      const quantity = 5.0;
      const unitPrice = 100.0;
      const discountPercentage = 10.0;
      const taxPercentage = 5.0;

      const totalPrice = quantity * unitPrice; // 500
      const discountAmount = totalPrice * (discountPercentage / 100); // 50
      const subtotal = totalPrice - discountAmount; // 450
      const taxAmount = subtotal * (taxPercentage / 100); // 22.5
      const netAmount = subtotal + taxAmount; // 472.5

      expect(totalPrice, equals(500.0));
      expect(discountAmount, equals(50.0));
      expect(subtotal, equals(450.0));
      expect(taxAmount, equals(22.5));
      expect(netAmount, equals(472.5));
    });
  });

  group('Price Selection Tests', () {
    testWidgets('should use selling price for sales invoice', (
      WidgetTester tester,
    ) async {
      const invoiceType = AppConstants.invoiceTypeSale;
      final item = Item(
        id: 1,
        code: 'I001',
        name: 'صنف تجريبي',
        unit: 'قطعة',
        quantity: 100.0,
        minQuantity: 10.0,
        costPrice: 50.0,
        sellingPrice: 75.0,
        currencyId: 1,
        isActive: true,
      );

      // للمبيعات يجب استخدام سعر البيع
      if (invoiceType == AppConstants.invoiceTypeSale ||
          invoiceType == AppConstants.invoiceTypeSaleReturn) {
        expect(item.sellingPrice, equals(75.0));
      } else {
        expect(item.costPrice, equals(50.0));
      }
    });

    testWidgets('should use cost price for purchase invoice', (
      WidgetTester tester,
    ) async {
      const invoiceType = AppConstants.invoiceTypePurchase;
      final item = Item(
        id: 1,
        code: 'I001',
        name: 'صنف تجريبي',
        unit: 'قطعة',
        quantity: 100.0,
        minQuantity: 10.0,
        costPrice: 50.0,
        sellingPrice: 75.0,
        currencyId: 1,
        isActive: true,
      );

      // للمشتريات يجب استخدام سعر التكلفة
      if (invoiceType == AppConstants.invoiceTypePurchase ||
          invoiceType == AppConstants.invoiceTypePurchaseReturn) {
        expect(item.costPrice, equals(50.0));
      } else {
        expect(item.sellingPrice, equals(75.0));
      }
    });
  });
}
