import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'logging_service.dart';

/// خدمة التشفير وإدارة كلمات المرور
/// تدير تشفير قاعدة البيانات وحماية البيانات الحساسة
class EncryptionService {
  static const String _passwordKey = 'db_password_hash';
  static const String _saltKey = 'db_salt';
  static const String _isSetupKey = 'encryption_setup';
  
  /// التحقق من وجود إعداد التشفير
  static Future<bool> isEncryptionSetup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isSetupKey) ?? false;
    } catch (e) {
      LoggingService.error(
        'فشل في التحقق من إعداد التشفير',
        category: 'Encryption',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// إعداد كلمة مرور قاعدة البيانات لأول مرة
  static Future<bool> setupDatabasePassword(String password) async {
    try {
      if (await isEncryptionSetup()) {
        LoggingService.warning(
          'محاولة إعداد كلمة مرور مع وجود إعداد سابق',
          category: 'Encryption',
        );
        return false;
      }

      // التحقق من قوة كلمة المرور
      if (!_isPasswordStrong(password)) {
        LoggingService.warning(
          'كلمة مرور ضعيفة',
          category: 'Encryption',
        );
        return false;
      }

      // إنشاء salt عشوائي
      final salt = _generateSalt();
      
      // تشفير كلمة المرور
      final hashedPassword = _hashPassword(password, salt);
      
      // حفظ البيانات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_passwordKey, hashedPassword);
      await prefs.setString(_saltKey, salt);
      await prefs.setBool(_isSetupKey, true);
      
      LoggingService.security(
        'تم إعداد تشفير قاعدة البيانات بنجاح',
        category: 'Encryption',
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في إعداد كلمة مرور قاعدة البيانات',
        category: 'Encryption',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من كلمة مرور قاعدة البيانات
  static Future<bool> verifyDatabasePassword(String password) async {
    try {
      if (!await isEncryptionSetup()) {
        LoggingService.warning(
          'محاولة التحقق من كلمة مرور بدون إعداد',
          category: 'Encryption',
        );
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final storedHash = prefs.getString(_passwordKey);
      final salt = prefs.getString(_saltKey);
      
      if (storedHash == null || salt == null) {
        LoggingService.error(
          'بيانات التشفير مفقودة',
          category: 'Encryption',
        );
        return false;
      }

      final hashedInput = _hashPassword(password, salt);
      final isValid = hashedInput == storedHash;
      
      if (isValid) {
        LoggingService.security(
          'تم التحقق من كلمة مرور قاعدة البيانات بنجاح',
          category: 'Encryption',
        );
      } else {
        LoggingService.security(
          'فشل في التحقق من كلمة مرور قاعدة البيانات',
          category: 'Encryption',
        );
      }
      
      return isValid;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من كلمة مرور قاعدة البيانات',
        category: 'Encryption',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// تغيير كلمة مرور قاعدة البيانات
  static Future<bool> changeDatabasePassword(
    String oldPassword,
    String newPassword,
  ) async {
    try {
      // التحقق من كلمة المرور القديمة
      if (!await verifyDatabasePassword(oldPassword)) {
        LoggingService.security(
          'فشل في تغيير كلمة المرور - كلمة المرور القديمة خاطئة',
          category: 'Encryption',
        );
        return false;
      }

      // التحقق من قوة كلمة المرور الجديدة
      if (!_isPasswordStrong(newPassword)) {
        LoggingService.warning(
          'كلمة المرور الجديدة ضعيفة',
          category: 'Encryption',
        );
        return false;
      }

      // إنشاء salt جديد
      final newSalt = _generateSalt();
      
      // تشفير كلمة المرور الجديدة
      final newHashedPassword = _hashPassword(newPassword, newSalt);
      
      // حفظ البيانات الجديدة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_passwordKey, newHashedPassword);
      await prefs.setString(_saltKey, newSalt);
      
      LoggingService.security(
        'تم تغيير كلمة مرور قاعدة البيانات بنجاح',
        category: 'Encryption',
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في تغيير كلمة مرور قاعدة البيانات',
        category: 'Encryption',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على كلمة مرور قاعدة البيانات للاستخدام
  static Future<String?> getDatabasePassword(String userPassword) async {
    try {
      if (await verifyDatabasePassword(userPassword)) {
        // إنشاء كلمة مرور مشتقة للاستخدام مع SQLCipher
        final prefs = await SharedPreferences.getInstance();
        final salt = prefs.getString(_saltKey);
        if (salt != null) {
          return _deriveKey(userPassword, salt);
        }
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'فشل في الحصول على كلمة مرور قاعدة البيانات',
        category: 'Encryption',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إعادة تعيين التشفير (للطوارئ فقط)
  static Future<bool> resetEncryption() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_passwordKey);
      await prefs.remove(_saltKey);
      await prefs.remove(_isSetupKey);
      
      LoggingService.security(
        'تم إعادة تعيين إعدادات التشفير',
        category: 'Encryption',
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في إعادة تعيين التشفير',
        category: 'Encryption',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  // ===============================
  // دوال مساعدة خاصة
  // ===============================

  /// إنشاء salt عشوائي
  static String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// تشفير كلمة المرور باستخدام PBKDF2
  static String _hashPassword(String password, String salt) {
    final saltBytes = base64.decode(salt);
    final passwordBytes = utf8.encode(password);
    
    // استخدام PBKDF2 مع 10000 تكرار
    final hmac = Hmac(sha256, saltBytes);
    var hash = hmac.convert(passwordBytes);
    
    // تكرار إضافي للأمان
    for (int i = 0; i < 9999; i++) {
      hash = hmac.convert(hash.bytes);
    }
    
    return base64.encode(hash.bytes);
  }

  /// اشتقاق مفتاح للاستخدام مع قاعدة البيانات
  static String _deriveKey(String password, String salt) {
    final saltBytes = base64.decode(salt);
    final passwordBytes = utf8.encode(password);
    
    final hmac = Hmac(sha512, saltBytes);
    final hash = hmac.convert(passwordBytes);
    
    return base64.encode(hash.bytes);
  }

  /// التحقق من قوة كلمة المرور
  static bool _isPasswordStrong(String password) {
    if (password.length < 8) return false;
    
    bool hasUpper = password.contains(RegExp(r'[A-Z]'));
    bool hasLower = password.contains(RegExp(r'[a-z]'));
    bool hasDigit = password.contains(RegExp(r'[0-9]'));
    bool hasSpecial = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUpper && hasLower && hasDigit && hasSpecial;
  }

  /// الحصول على متطلبات كلمة المرور
  static String getPasswordRequirements() {
    return '''
كلمة المرور يجب أن تحتوي على:
• 8 أحرف على الأقل
• حرف كبير واحد على الأقل (A-Z)
• حرف صغير واحد على الأقل (a-z)
• رقم واحد على الأقل (0-9)
• رمز خاص واحد على الأقل (!@#\$%^&*(),.?":{}|<>)
    ''';
  }
}
