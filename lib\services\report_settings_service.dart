import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/interactive_report_models.dart';
import 'logging_service.dart';

/// خدمة حفظ واستعادة إعدادات التقارير
class ReportSettingsService {
  static const String _keyPrefix = 'report_settings_';
  static const String _savedConfigsKey = 'saved_report_configs';

  /// حفظ إعدادات تقرير
  Future<bool> saveReportSettings({
    required String reportType,
    required ReportFilters filters,
    required ReportConfiguration configuration,
    String? configName,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final settings = ReportSettings(
        reportType: reportType,
        filters: filters,
        configuration: configuration,
        configName: configName ?? 'إعدادات افتراضية',
        savedAt: DateTime.now(),
      );

      final key = '$_keyPrefix$reportType';
      final jsonString = jsonEncode(settings.toMap());
      
      final success = await prefs.setString(key, jsonString);
      
      if (success) {
        LoggingService.info(
          'تم حفظ إعدادات التقرير',
          category: 'ReportSettings',
          data: {
            'reportType': reportType,
            'configName': configName,
          },
        );
      }
      
      return success;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ إعدادات التقرير',
        category: 'ReportSettings',
        data: {
          'reportType': reportType,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// استعادة إعدادات تقرير
  Future<ReportSettings?> loadReportSettings(String reportType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_keyPrefix$reportType';
      final jsonString = prefs.getString(key);
      
      if (jsonString == null) {
        return null;
      }
      
      final map = jsonDecode(jsonString) as Map<String, dynamic>;
      return ReportSettings.fromMap(map);
    } catch (e) {
      LoggingService.error(
        'خطأ في استعادة إعدادات التقرير',
        category: 'ReportSettings',
        data: {
          'reportType': reportType,
          'error': e.toString(),
        },
      );
      return null;
    }
  }

  /// حفظ إعدادات مخصصة بالاسم
  Future<bool> saveNamedConfiguration({
    required String reportType,
    required String configName,
    required ReportFilters filters,
    required ReportConfiguration configuration,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // الحصول على الإعدادات المحفوظة الحالية
      final savedConfigs = await getSavedConfigurations(reportType);
      
      // إضافة الإعدادات الجديدة
      final newConfig = SavedReportConfiguration(
        name: configName,
        filters: filters,
        configuration: configuration,
        savedAt: DateTime.now(),
      );
      
      savedConfigs[configName] = newConfig;
      
      // حفظ الإعدادات المحدثة
      final key = '${_savedConfigsKey}_$reportType';
      final jsonString = jsonEncode(
        savedConfigs.map((name, config) => MapEntry(name, config.toMap())),
      );
      
      final success = await prefs.setString(key, jsonString);
      
      if (success) {
        LoggingService.info(
          'تم حفظ إعدادات مخصصة',
          category: 'ReportSettings',
          data: {
            'reportType': reportType,
            'configName': configName,
          },
        );
      }
      
      return success;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ الإعدادات المخصصة',
        category: 'ReportSettings',
        data: {
          'reportType': reportType,
          'configName': configName,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// الحصول على الإعدادات المحفوظة
  Future<Map<String, SavedReportConfiguration>> getSavedConfigurations(
    String reportType,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '${_savedConfigsKey}_$reportType';
      final jsonString = prefs.getString(key);
      
      if (jsonString == null) {
        return {};
      }
      
      final map = jsonDecode(jsonString) as Map<String, dynamic>;
      return map.map(
        (name, configMap) => MapEntry(
          name,
          SavedReportConfiguration.fromMap(configMap as Map<String, dynamic>),
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في استعادة الإعدادات المحفوظة',
        category: 'ReportSettings',
        data: {
          'reportType': reportType,
          'error': e.toString(),
        },
      );
      return {};
    }
  }

  /// حذف إعدادات مخصصة
  Future<bool> deleteNamedConfiguration({
    required String reportType,
    required String configName,
  }) async {
    try {
      final savedConfigs = await getSavedConfigurations(reportType);
      savedConfigs.remove(configName);
      
      final prefs = await SharedPreferences.getInstance();
      final key = '${_savedConfigsKey}_$reportType';
      
      if (savedConfigs.isEmpty) {
        return await prefs.remove(key);
      } else {
        final jsonString = jsonEncode(
          savedConfigs.map((name, config) => MapEntry(name, config.toMap())),
        );
        return await prefs.setString(key, jsonString);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الإعدادات المخصصة',
        category: 'ReportSettings',
        data: {
          'reportType': reportType,
          'configName': configName,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// حذف جميع إعدادات تقرير معين
  Future<bool> clearReportSettings(String reportType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final defaultKey = '$_keyPrefix$reportType';
      final savedKey = '${_savedConfigsKey}_$reportType';
      
      final success1 = await prefs.remove(defaultKey);
      final success2 = await prefs.remove(savedKey);
      
      return success1 && success2;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف إعدادات التقرير',
        category: 'ReportSettings',
        data: {
          'reportType': reportType,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// الحصول على قائمة بجميع أنواع التقارير المحفوظة
  Future<List<String>> getSavedReportTypes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      return keys
          .where((key) => key.startsWith(_keyPrefix))
          .map((key) => key.substring(_keyPrefix.length))
          .toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على أنواع التقارير المحفوظة',
        category: 'ReportSettings',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تصدير إعدادات التقارير
  Future<String?> exportSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      final reportKeys = keys.where(
        (key) => key.startsWith(_keyPrefix) || key.startsWith(_savedConfigsKey),
      );
      
      final exportData = <String, String>{};
      for (final key in reportKeys) {
        final value = prefs.getString(key);
        if (value != null) {
          exportData[key] = value;
        }
      }
      
      return jsonEncode(exportData);
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير إعدادات التقارير',
        category: 'ReportSettings',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// استيراد إعدادات التقارير
  Future<bool> importSettings(String jsonData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final importData = jsonDecode(jsonData) as Map<String, dynamic>;
      
      for (final entry in importData.entries) {
        await prefs.setString(entry.key, entry.value as String);
      }
      
      LoggingService.info(
        'تم استيراد إعدادات التقارير',
        category: 'ReportSettings',
        data: {'importedCount': importData.length},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في استيراد إعدادات التقارير',
        category: 'ReportSettings',
        data: {'error': e.toString()},
      );
      return false;
    }
  }
}

/// نموذج إعدادات التقرير
class ReportSettings {
  final String reportType;
  final ReportFilters filters;
  final ReportConfiguration configuration;
  final String configName;
  final DateTime savedAt;

  const ReportSettings({
    required this.reportType,
    required this.filters,
    required this.configuration,
    required this.configName,
    required this.savedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'reportType': reportType,
      'filters': filters.toMap(),
      'configuration': configuration.toMap(),
      'configName': configName,
      'savedAt': savedAt.toIso8601String(),
    };
  }

  factory ReportSettings.fromMap(Map<String, dynamic> map) {
    return ReportSettings(
      reportType: map['reportType'] as String,
      filters: ReportFilters.fromMap(map['filters'] as Map<String, dynamic>),
      configuration: ReportConfiguration.fromMap(map['configuration'] as Map<String, dynamic>),
      configName: map['configName'] as String,
      savedAt: DateTime.parse(map['savedAt'] as String),
    );
  }
}

/// نموذج الإعدادات المحفوظة
class SavedReportConfiguration {
  final String name;
  final ReportFilters filters;
  final ReportConfiguration configuration;
  final DateTime savedAt;

  const SavedReportConfiguration({
    required this.name,
    required this.filters,
    required this.configuration,
    required this.savedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'filters': filters.toMap(),
      'configuration': configuration.toMap(),
      'savedAt': savedAt.toIso8601String(),
    };
  }

  factory SavedReportConfiguration.fromMap(Map<String, dynamic> map) {
    return SavedReportConfiguration(
      name: map['name'] as String,
      filters: ReportFilters.fromMap(map['filters'] as Map<String, dynamic>),
      configuration: ReportConfiguration.fromMap(map['configuration'] as Map<String, dynamic>),
      savedAt: DateTime.parse(map['savedAt'] as String),
    );
  }
}
