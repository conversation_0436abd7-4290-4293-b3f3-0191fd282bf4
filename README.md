# smart_ledger

A new Flutter project.

## Getting Started

# Smart Ledger - دفتر الحسابات الذكي

تطبيق محاسبة متكامل وأوفلاين مصمم خصيصاً للشركات السورية

## المطور
**مجد محمد زياد يسير**

## الوصف
Smart Ledger هو تطبيق محاسبة شامل ومتطور يعمل بشكل كامل أوفلاين، مصمم خصيصاً لتلبية احتياجات الشركات والمؤسسات في سوريا. يتميز التطبيق بواجهات جميلة وسهلة الاستخدام مع ألوان مريحة للعين.

## الميزات الرئيسية

### 📊 دليل الحسابات
- إدارة شاملة للحسابات المحاسبية
- تصنيف الحسابات (أصول، خصوم، إيرادات، مصروفات، مشتريات، مبيعات، مخزون)
- البحث والفلترة المتقدمة
- عرض الأرصدة الحالية

### 📝 القيود المحاسبية
- إدخال وإدارة القيود المحاسبية
- التحقق من توازن القيود تلقائياً
- ترحيل القيود وتحديث الأرصدة
- البحث في القيود حسب التاريخ والوصف

### ♿ إمكانية الوصول (Accessibility)
- **دعم كامل لقارئ الشاشة** (TalkBack, VoiceOver)
- **التنقل بلوحة المفاتيح** مع اختصارات مفيدة
- **Semantic Labels** شاملة لجميع العناصر
- **إعلانات صوتية** للعمليات والتحديثات
- **تباين ألوان مناسب** للأشخاص ذوي الإعاقة البصرية
- **أحجام عناصر مناسبة** للمس والتفاعل
- **توافق مع معايير WCAG 2.1 Level AA**

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء والموردين
- تتبع الأرصدة والديون
- إدارة حدود الائتمان للعملاء
- معلومات الاتصال والعناوين

### 📈 التقارير المالية
- ميزان المراجعة
- قائمة الدخل
- الميزانية العمومية
- كشوف الحسابات
- تقارير العملاء والموردين
- اليومية العامة والأستاذ العام

### 💰 العملات
- دعم الليرة السورية كعملة أساسية
- دعم العملات المتعددة (دولار، يورو)
- أسعار الصرف القابلة للتحديث

## التقنيات المستخدمة
- **Flutter**: لتطوير واجهة المستخدم
- **SQLite**: لقاعدة البيانات المحلية
- **Dart**: لغة البرمجة
- **Material Design**: للتصميم الجميل والمتجاوب

## المتطلبات
- Flutter SDK 3.0 أو أحدث
- Dart SDK 2.17 أو أحدث
- Windows 10/11 أو Android 6.0+

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/majd-yaser/smart_ledger.git
cd smart_ledger
```

### 2. تثبيت المكتبات
```bash
flutter pub get
```

### 3. تشغيل التطبيق
```bash
# للتشغيل على Windows
flutter run -d windows

# للتشغيل على Android
flutter run -d android

# للتشغيل في المتصفح
flutter run -d chrome
```

## اختصارات لوحة المفاتيح

### اختصارات عامة
- `Tab` - الانتقال للعنصر التالي
- `Shift + Tab` - الانتقال للعنصر السابق
- `Enter / Space` - تفعيل العنصر المحدد
- `Escape` - إغلاق الحوار أو العودة

### اختصارات النماذج
- `Ctrl + S` - حفظ النموذج
- `Ctrl + N` - إضافة عنصر جديد
- `F2` - تعديل العنصر المحدد
- `Delete` - حذف العنصر المحدد

### اختصارات التنقل
- `Arrow Up/Down` - التنقل في القوائم
- `Home` - الانتقال لأول عنصر
- `End` - الانتقال لآخر عنصر
- `Ctrl + F` - فتح البحث

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **SQLite** - قاعدة البيانات المحلية
- **Provider** - إدارة الحالة
- **Material Design** - تصميم واجهة المستخدم
- **Dart** - لغة البرمجة
- **Accessibility Services** - خدمات إمكانية الوصول

## هيكل المشروع
```
lib/
├── constants/          # الثوابت والألوان والثيمات
│   ├── app_colors.dart
│   ├── app_constants.dart
│   └── accessibility_constants.dart  # ثوابت إمكانية الوصول
├── database/           # إعداد قاعدة البيانات
├── models/             # نماذج البيانات
├── screens/            # شاشات التطبيق
├── services/           # خدمات التطبيق
│   ├── account_service.dart
│   ├── accessibility_service.dart     # خدمة إمكانية الوصول
│   ├── focus_management_service.dart  # خدمة إدارة التركيز
│   └── screen_reader_service.dart     # خدمة قارئ الشاشة
├── widgets/            # المكونات المخصصة
├── test/               # الاختبارات
│   └── accessibility_test.dart        # اختبارات إمكانية الوصول
├── docs/               # الوثائق
│   ├── accessibility_testing_guide.md
│   ├── accessibility_test_results.md
│   └── accessibility_implementation_summary.md
└── main.dart           # نقطة البداية
```

## إمكانية الوصول (Accessibility)

Smart Ledger مصمم ليكون قابلاً للاستخدام من قبل جميع المستخدمين، بما في ذلك الأشخاص ذوي الاحتياجات الخاصة.

### ✅ الميزات المطبقة
- **دعم قارئ الشاشة**: متوافق مع TalkBack (Android) و VoiceOver (iOS)
- **التنقل بلوحة المفاتيح**: تنقل كامل باستخدام Tab والاختصارات
- **Semantic Labels**: تسميات واضحة لجميع العناصر
- **إعلانات صوتية**: تنبيهات صوتية للعمليات والتحديثات
- **تباين ألوان مناسب**: ألوان واضحة للقراءة
- **أحجام عناصر مناسبة**: أهداف لمس كبيرة ومريحة

### 📋 معايير الامتثال
- **WCAG 2.1 Level AA**: متوافق مع المعايير الدولية
- **نتيجة إمكانية الوصول**: 87.5% (ممتاز)
- **دعم المنصات**: Android, iOS, Web

### 🧪 الاختبار
```bash
# تشغيل اختبارات إمكانية الوصول
flutter test test/accessibility_test.dart
```

للمزيد من المعلومات، راجع [دليل اختبار إمكانية الوصول](docs/accessibility_testing_guide.md).

## الميزات المستقبلية
- [ ] نظام الفواتير المتكامل
- [ ] إدارة المخزون
- [ ] النسخ الاحتياطي والاستعادة
- [ ] التصدير إلى Excel/PDF
- [ ] نظام المستخدمين والصلاحيات
- [ ] التقارير التفاعلية مع الرسوم البيانية
- [ ] تحسينات إضافية لإمكانية الوصول

## الدعم والمساهمة
هذا التطبيق مطور بواسطة **مجد محمد زياد يسير** خصيصاً للسوق السوري.

## الترخيص
جميع الحقوق محفوظة © 2024 مجد محمد زياد يسير

---

**Smart Ledger** - نحو محاسبة أذكى وأسهل 🚀
