class JournalEntry {
  final int? id;
  final String entryNumber;
  final DateTime entryDate;
  final String description;
  final String type;
  final double totalDebit;
  final double totalCredit;
  final int currencyId;
  final String? referenceType;
  final int? referenceId;
  final bool isPosted;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<JournalEntryDetail> details;

  JournalEntry({
    this.id,
    required this.entryNumber,
    required this.entryDate,
    required this.description,
    required this.type,
    this.totalDebit = 0.0,
    this.totalCredit = 0.0,
    required this.currencyId,
    this.referenceType,
    this.referenceId,
    this.isPosted = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.details = const [],
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entry_number': entryNumber,
      'entry_date': entryDate.toIso8601String().split('T')[0],
      'description': description,
      'type': type,
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'currency_id': currencyId,
      'reference_type': referenceType,
      'reference_id': referenceId,
      'is_posted': isPosted ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory JournalEntry.fromMap(Map<String, dynamic> map) {
    return JournalEntry(
      id: map['id']?.toInt(),
      entryNumber: map['entry_number'] ?? '',
      entryDate: DateTime.parse(map['entry_date'] ?? DateTime.now().toIso8601String()),
      description: map['description'] ?? '',
      type: map['type'] ?? '',
      totalDebit: map['total_debit']?.toDouble() ?? 0.0,
      totalCredit: map['total_credit']?.toDouble() ?? 0.0,
      currencyId: map['currency_id']?.toInt() ?? 1,
      referenceType: map['reference_type'],
      referenceId: map['reference_id']?.toInt(),
      isPosted: (map['is_posted'] ?? 0) == 1,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  JournalEntry copyWith({
    int? id,
    String? entryNumber,
    DateTime? entryDate,
    String? description,
    String? type,
    double? totalDebit,
    double? totalCredit,
    int? currencyId,
    String? referenceType,
    int? referenceId,
    bool? isPosted,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<JournalEntryDetail>? details,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      entryDate: entryDate ?? this.entryDate,
      description: description ?? this.description,
      type: type ?? this.type,
      totalDebit: totalDebit ?? this.totalDebit,
      totalCredit: totalCredit ?? this.totalCredit,
      currencyId: currencyId ?? this.currencyId,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      isPosted: isPosted ?? this.isPosted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      details: details ?? this.details,
    );
  }

  bool get isBalanced {
    return (totalDebit - totalCredit).abs() < 0.01;
  }

  String get typeArabic {
    switch (type) {
      case 'general':
        return 'قيد عام';
      case 'sale':
        return 'قيد مبيعات';
      case 'purchase':
        return 'قيد مشتريات';
      case 'payment':
        return 'قيد دفع';
      case 'receipt':
        return 'قيد قبض';
      default:
        return 'غير محدد';
    }
  }

  String get statusArabic {
    return isPosted ? 'مرحل' : 'مسودة';
  }
}

class JournalEntryDetail {
  final int? id;
  final int journalEntryId;
  final int accountId;
  final double debitAmount;
  final double creditAmount;
  final String? description;
  final DateTime createdAt;

  JournalEntryDetail({
    this.id,
    required this.journalEntryId,
    required this.accountId,
    this.debitAmount = 0.0,
    this.creditAmount = 0.0,
    this.description,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'journal_entry_id': journalEntryId,
      'account_id': accountId,
      'debit_amount': debitAmount,
      'credit_amount': creditAmount,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory JournalEntryDetail.fromMap(Map<String, dynamic> map) {
    return JournalEntryDetail(
      id: map['id']?.toInt(),
      journalEntryId: map['journal_entry_id']?.toInt() ?? 0,
      accountId: map['account_id']?.toInt() ?? 0,
      debitAmount: map['debit_amount']?.toDouble() ?? 0.0,
      creditAmount: map['credit_amount']?.toDouble() ?? 0.0,
      description: map['description'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  JournalEntryDetail copyWith({
    int? id,
    int? journalEntryId,
    int? accountId,
    double? debitAmount,
    double? creditAmount,
    String? description,
    DateTime? createdAt,
  }) {
    return JournalEntryDetail(
      id: id ?? this.id,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      accountId: accountId ?? this.accountId,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  double get amount {
    return debitAmount > 0 ? debitAmount : creditAmount;
  }

  bool get isDebit {
    return debitAmount > 0;
  }

  bool get isCredit {
    return creditAmount > 0;
  }
}
