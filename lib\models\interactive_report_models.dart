/// نماذج البيانات للتقارير التفاعلية
library;

import 'report_models.dart';

/// فلاتر التقارير
class ReportFilters {
  final DateTime? fromDate;
  final DateTime? toDate;
  final List<String> accountTypes;
  final List<String> accountCodes;
  final List<int> accountIds;
  final List<int> customerIds;
  final List<int> supplierIds;
  final List<int> itemIds;
  final double? minAmount;
  final double? maxAmount;
  final String? searchText;
  final bool includeInactive;
  final Map<String, dynamic> customFilters;

  const ReportFilters({
    this.fromDate,
    this.toDate,
    this.accountTypes = const [],
    this.accountCodes = const [],
    this.accountIds = const [],
    this.customerIds = const [],
    this.supplierIds = const [],
    this.itemIds = const [],
    this.minAmount,
    this.maxAmount,
    this.searchText,
    this.includeInactive = false,
    this.customFilters = const {},
  });

  /// إنشاء فلاتر فارغة
  factory ReportFilters.empty() {
    return const ReportFilters();
  }

  /// إنشاء فلاتر بتاريخ محدد
  factory ReportFilters.dateRange(DateTime fromDate, DateTime toDate) {
    return ReportFilters(fromDate: fromDate, toDate: toDate);
  }

  /// نسخ مع تعديل
  ReportFilters copyWith({
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? accountTypes,
    List<String>? accountCodes,
    List<int>? accountIds,
    List<int>? customerIds,
    List<int>? supplierIds,
    List<int>? itemIds,
    double? minAmount,
    double? maxAmount,
    String? searchText,
    bool? includeInactive,
    Map<String, dynamic>? customFilters,
  }) {
    return ReportFilters(
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      accountTypes: accountTypes ?? this.accountTypes,
      accountCodes: accountCodes ?? this.accountCodes,
      accountIds: accountIds ?? this.accountIds,
      customerIds: customerIds ?? this.customerIds,
      supplierIds: supplierIds ?? this.supplierIds,
      itemIds: itemIds ?? this.itemIds,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      searchText: searchText ?? this.searchText,
      includeInactive: includeInactive ?? this.includeInactive,
      customFilters: customFilters ?? this.customFilters,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'accountTypes': accountTypes,
      'accountCodes': accountCodes,
      'accountIds': accountIds,
      'customerIds': customerIds,
      'supplierIds': supplierIds,
      'itemIds': itemIds,
      'minAmount': minAmount,
      'maxAmount': maxAmount,
      'searchText': searchText,
      'includeInactive': includeInactive,
      'customFilters': customFilters,
    };
  }

  /// إنشاء من Map
  factory ReportFilters.fromMap(Map<String, dynamic> map) {
    return ReportFilters(
      fromDate: map['fromDate'] != null
          ? DateTime.parse(map['fromDate'])
          : null,
      toDate: map['toDate'] != null ? DateTime.parse(map['toDate']) : null,
      accountTypes: List<String>.from(map['accountTypes'] ?? []),
      accountCodes: List<String>.from(map['accountCodes'] ?? []),
      accountIds: List<int>.from(map['accountIds'] ?? []),
      customerIds: List<int>.from(map['customerIds'] ?? []),
      supplierIds: List<int>.from(map['supplierIds'] ?? []),
      itemIds: List<int>.from(map['itemIds'] ?? []),
      minAmount: map['minAmount']?.toDouble(),
      maxAmount: map['maxAmount']?.toDouble(),
      searchText: map['searchText'],
      includeInactive: map['includeInactive'] ?? false,
      customFilters: Map<String, dynamic>.from(map['customFilters'] ?? {}),
    );
  }
}

/// إعدادات التقرير
class ReportConfiguration {
  final String? sortBy;
  final String? sortDirection;
  final int? pageSize;
  final bool showTotals;
  final bool showPercentages;
  final bool groupByType;
  final List<String> visibleColumns;
  final Map<String, dynamic> chartSettings;
  final Map<String, dynamic> exportSettings;

  const ReportConfiguration({
    this.sortBy,
    this.sortDirection,
    this.pageSize,
    this.showTotals = true,
    this.showPercentages = false,
    this.groupByType = false,
    this.visibleColumns = const [],
    this.chartSettings = const {},
    this.exportSettings = const {},
  });

  /// إعدادات افتراضية
  factory ReportConfiguration.defaultConfig() {
    return const ReportConfiguration(
      sortBy: 'code',
      sortDirection: 'ASC',
      pageSize: 50,
      showTotals: true,
      showPercentages: false,
      groupByType: false,
      visibleColumns: [],
      chartSettings: {},
      exportSettings: {},
    );
  }

  /// نسخ مع تعديل
  ReportConfiguration copyWith({
    String? sortBy,
    String? sortDirection,
    int? pageSize,
    bool? showTotals,
    bool? showPercentages,
    bool? groupByType,
    List<String>? visibleColumns,
    Map<String, dynamic>? chartSettings,
    Map<String, dynamic>? exportSettings,
  }) {
    return ReportConfiguration(
      sortBy: sortBy ?? this.sortBy,
      sortDirection: sortDirection ?? this.sortDirection,
      pageSize: pageSize ?? this.pageSize,
      showTotals: showTotals ?? this.showTotals,
      showPercentages: showPercentages ?? this.showPercentages,
      groupByType: groupByType ?? this.groupByType,
      visibleColumns: visibleColumns ?? this.visibleColumns,
      chartSettings: chartSettings ?? this.chartSettings,
      exportSettings: exportSettings ?? this.exportSettings,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'sortBy': sortBy,
      'sortDirection': sortDirection,
      'pageSize': pageSize,
      'showTotals': showTotals,
      'showPercentages': showPercentages,
      'groupByType': groupByType,
      'visibleColumns': visibleColumns,
      'chartSettings': chartSettings,
      'exportSettings': exportSettings,
    };
  }

  /// إنشاء من Map
  factory ReportConfiguration.fromMap(Map<String, dynamic> map) {
    return ReportConfiguration(
      sortBy: map['sortBy'],
      sortDirection: map['sortDirection'],
      pageSize: map['pageSize'],
      showTotals: map['showTotals'] ?? true,
      showPercentages: map['showPercentages'] ?? false,
      groupByType: map['groupByType'] ?? false,
      visibleColumns: List<String>.from(map['visibleColumns'] ?? []),
      chartSettings: Map<String, dynamic>.from(map['chartSettings'] ?? {}),
      exportSettings: Map<String, dynamic>.from(map['exportSettings'] ?? {}),
    );
  }
}

/// نتيجة التقرير التفاعلي
class InteractiveReportResult {
  final String reportType;
  final String title;
  final dynamic data;
  final ReportFilters filters;
  final ReportConfiguration configuration;
  final ReportMetadata metadata;

  const InteractiveReportResult({
    required this.reportType,
    required this.title,
    required this.data,
    required this.filters,
    required this.configuration,
    required this.metadata,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'reportType': reportType,
      'title': title,
      'data': data,
      'filters': filters.toMap(),
      'configuration': configuration.toMap(),
      'metadata': metadata.toMap(),
    };
  }
}

/// معلومات إضافية عن التقرير
class ReportMetadata {
  final int totalRecords;
  final DateTime generatedAt;
  final int executionTimeMs;
  final Map<String, dynamic> additionalInfo;

  const ReportMetadata({
    required this.totalRecords,
    required this.generatedAt,
    required this.executionTimeMs,
    this.additionalInfo = const {},
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'totalRecords': totalRecords,
      'generatedAt': generatedAt.toIso8601String(),
      'executionTimeMs': executionTimeMs,
      'additionalInfo': additionalInfo,
    };
  }
}

/// بيانات تقرير قائمة الدخل
class ProfitLossReportData {
  final DateTime fromDate;
  final DateTime toDate;
  final List<ProfitLossItem> revenueItems;
  final List<ProfitLossItem> expenseItems;
  final double totalRevenue;
  final double totalExpense;
  final double netProfit;

  const ProfitLossReportData({
    required this.fromDate,
    required this.toDate,
    required this.revenueItems,
    required this.expenseItems,
    required this.totalRevenue,
    required this.totalExpense,
    required this.netProfit,
  });

  Map<String, dynamic> toMap() {
    return {
      'fromDate': fromDate.toIso8601String(),
      'toDate': toDate.toIso8601String(),
      'revenueItems': revenueItems.map((item) => item.toMap()).toList(),
      'expenseItems': expenseItems.map((item) => item.toMap()).toList(),
      'totalRevenue': totalRevenue,
      'totalExpense': totalExpense,
      'netProfit': netProfit,
    };
  }
}

/// بيانات تقرير الميزانية العمومية
class BalanceSheetReportData {
  final DateTime asOfDate;
  final List<BalanceSheetItem> assets;
  final List<BalanceSheetItem> liabilities;
  final List<BalanceSheetItem> equity;
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;

  const BalanceSheetReportData({
    required this.asOfDate,
    required this.assets,
    required this.liabilities,
    required this.equity,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
  });

  Map<String, dynamic> toMap() {
    return {
      'asOfDate': asOfDate.toIso8601String(),
      'assets': assets.map((item) => item.toMap()).toList(),
      'liabilities': liabilities.map((item) => item.toMap()).toList(),
      'equity': equity.map((item) => item.toMap()).toList(),
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
      'totalEquity': totalEquity,
    };
  }
}
