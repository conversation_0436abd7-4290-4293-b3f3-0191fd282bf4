/// استثناء خاص بأخطاء التحقق من صحة البيانات
class ValidationException implements Exception {
  final String message;
  final String? field;
  final dynamic value;
  
  const ValidationException(
    this.message, {
    this.field,
    this.value,
  });
  
  @override
  String toString() {
    if (field != null) {
      return 'ValidationException: $field - $message';
    }
    return 'ValidationException: $message';
  }
  
  /// إنشاء استثناء للحقل المطلوب
  factory ValidationException.required(String fieldName) {
    return ValidationException(
      '$fieldName مطلوب',
      field: fieldName,
    );
  }
  
  /// إنشاء استثناء للطول غير الصحيح
  factory ValidationException.invalidLength(
    String fieldName, 
    int currentLength, 
    {int? minLength, int? maxLength}
  ) {
    String message;
    if (minLength != null && maxLength != null) {
      message = '$fieldName يجب أن يكون بين $minLength و $maxLength حرف';
    } else if (minLength != null) {
      message = '$fieldName يجب أن يكون على الأقل $minLength أحرف';
    } else if (maxLength != null) {
      message = '$fieldName يجب أن لا يزيد عن $maxLength حرف';
    } else {
      message = '$fieldName طوله غير صحيح';
    }
    
    return ValidationException(
      message,
      field: fieldName,
      value: currentLength,
    );
  }
  
  /// إنشاء استثناء للتنسيق غير الصحيح
  factory ValidationException.invalidFormat(String fieldName, String expectedFormat) {
    return ValidationException(
      '$fieldName تنسيقه غير صحيح. التنسيق المطلوب: $expectedFormat',
      field: fieldName,
    );
  }
  
  /// إنشاء استثناء للقيمة خارج النطاق
  factory ValidationException.outOfRange(
    String fieldName, 
    dynamic value, 
    {dynamic min, dynamic max}
  ) {
    String message;
    if (min != null && max != null) {
      message = '$fieldName يجب أن يكون بين $min و $max';
    } else if (min != null) {
      message = '$fieldName يجب أن يكون أكبر من أو يساوي $min';
    } else if (max != null) {
      message = '$fieldName يجب أن يكون أقل من أو يساوي $max';
    } else {
      message = '$fieldName قيمته خارج النطاق المسموح';
    }
    
    return ValidationException(
      message,
      field: fieldName,
      value: value,
    );
  }
  
  /// إنشاء استثناء للقيمة المكررة
  factory ValidationException.duplicate(String fieldName, dynamic value) {
    return ValidationException(
      '$fieldName "$value" موجود مسبقاً',
      field: fieldName,
      value: value,
    );
  }
  
  /// إنشاء استثناء للعملية غير المسموحة
  factory ValidationException.notAllowed(String operation, String reason) {
    return ValidationException(
      'العملية "$operation" غير مسموحة: $reason',
    );
  }
}

/// استثناء خاص بأخطاء العمليات التجارية
class BusinessRuleException implements Exception {
  final String message;
  final String? ruleCode;
  
  const BusinessRuleException(this.message, {this.ruleCode});
  
  @override
  String toString() {
    if (ruleCode != null) {
      return 'BusinessRuleException [$ruleCode]: $message';
    }
    return 'BusinessRuleException: $message';
  }
  
  /// قاعدة توازن القيد المحاسبي
  factory BusinessRuleException.unbalancedEntry(double debit, double credit) {
    return BusinessRuleException(
      'القيد غير متوازن - المدين: ${debit.toStringAsFixed(2)}, الدائن: ${credit.toStringAsFixed(2)}',
      ruleCode: 'UNBALANCED_ENTRY',
    );
  }
  
  /// قاعدة منع حذف البيانات المرتبطة
  factory BusinessRuleException.hasRelatedData(String entityType, String relatedType) {
    return BusinessRuleException(
      'لا يمكن حذف $entityType لوجود $relatedType مرتبطة به',
      ruleCode: 'HAS_RELATED_DATA',
    );
  }
  
  /// قاعدة منع تعديل البيانات المرحلة
  factory BusinessRuleException.alreadyPosted(String entityType) {
    return BusinessRuleException(
      'لا يمكن تعديل $entityType بعد ترحيله',
      ruleCode: 'ALREADY_POSTED',
    );
  }
  
  /// قاعدة عدم كفاية المخزون
  factory BusinessRuleException.insufficientStock(String itemName, double available, double required) {
    return BusinessRuleException(
      'المخزون غير كافي للصنف "$itemName" - المتاح: $available, المطلوب: $required',
      ruleCode: 'INSUFFICIENT_STOCK',
    );
  }
}
