/// نماذج البيانات للتقارير المالية
/// يحتوي على جميع النماذج المستخدمة في التقارير المختلفة
library;

/// عنصر ميزان المراجعة
class TrialBalanceItem {
  final int id;
  final String code;
  final String name;
  final String type;
  final double openingBalance;
  final double totalDebit;
  final double totalCredit;
  final double closingBalance;

  TrialBalanceItem({
    required this.id,
    required this.code,
    required this.name,
    required this.type,
    required this.openingBalance,
    required this.totalDebit,
    required this.totalCredit,
    required this.closingBalance,
  });

  factory TrialBalanceItem.fromMap(Map<String, dynamic> map) {
    final openingBalance = (map['opening_balance'] as num).toDouble();
    final totalDebit = (map['total_debit'] as num).toDouble();
    final totalCredit = (map['total_credit'] as num).toDouble();

    return TrialBalanceItem(
      id: map['id'] as int,
      code: map['code'] as String,
      name: map['name'] as String,
      type: map['type'] as String,
      openingBalance: openingBalance,
      totalDebit: totalDebit,
      totalCredit: totalCredit,
      closingBalance: openingBalance + totalDebit - totalCredit,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'type': type,
      'openingBalance': openingBalance,
      'totalDebit': totalDebit,
      'totalCredit': totalCredit,
      'closingBalance': closingBalance,
    };
  }
}

/// عنصر حركة الحساب
class AccountMovementItem {
  final DateTime entryDate;
  final String entryNumber;
  final String entryDescription;
  final double debitAmount;
  final double creditAmount;
  final String? detailDescription;

  AccountMovementItem({
    required this.entryDate,
    required this.entryNumber,
    required this.entryDescription,
    required this.debitAmount,
    required this.creditAmount,
    this.detailDescription,
  });

  factory AccountMovementItem.fromMap(Map<String, dynamic> map) {
    return AccountMovementItem(
      entryDate: DateTime.parse(map['entry_date'] as String),
      entryNumber: map['entry_number'] as String,
      entryDescription: map['entry_description'] as String,
      debitAmount: (map['debit_amount'] as num).toDouble(),
      creditAmount: (map['credit_amount'] as num).toDouble(),
      detailDescription: map['detail_description'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'entryDate': entryDate.toIso8601String(),
      'entryNumber': entryNumber,
      'entryDescription': entryDescription,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'detailDescription': detailDescription,
    };
  }
}

/// عنصر تقرير أعمار الديون
class AgingReportItem {
  final int id;
  final String code;
  final String name;
  final double balance;
  final double currentAmount;
  final double days31To60;
  final double days61To90;
  final double over90Days;

  AgingReportItem({
    required this.id,
    required this.code,
    required this.name,
    required this.balance,
    required this.currentAmount,
    required this.days31To60,
    required this.days61To90,
    required this.over90Days,
  });

  factory AgingReportItem.fromMap(Map<String, dynamic> map) {
    return AgingReportItem(
      id: map['id'] as int,
      code: map['code'] as String,
      name: map['name'] as String,
      balance: (map['balance'] as num).toDouble(),
      currentAmount: (map['current_amount'] as num).toDouble(),
      days31To60: (map['days_31_60'] as num).toDouble(),
      days61To90: (map['days_61_90'] as num).toDouble(),
      over90Days: (map['over_90_days'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'balance': balance,
      'currentAmount': currentAmount,
      'days31To60': days31To60,
      'days61To90': days61To90,
      'over90Days': over90Days,
    };
  }
}

/// عنصر تقرير المخزون
class InventoryReportItem {
  final int id;
  final String code;
  final String name;
  final String unit;
  final double quantity;
  final double minQuantity;
  final double costPrice;
  final double sellingPrice;
  final double totalCostValue;
  final double totalSellingValue;
  final String stockStatus;

  InventoryReportItem({
    required this.id,
    required this.code,
    required this.name,
    required this.unit,
    required this.quantity,
    required this.minQuantity,
    required this.costPrice,
    required this.sellingPrice,
    required this.totalCostValue,
    required this.totalSellingValue,
    required this.stockStatus,
  });

  factory InventoryReportItem.fromMap(Map<String, dynamic> map) {
    return InventoryReportItem(
      id: map['id'] as int,
      code: map['code'] as String,
      name: map['name'] as String,
      unit: map['unit'] as String,
      quantity: (map['quantity'] as num).toDouble(),
      minQuantity: (map['min_quantity'] as num).toDouble(),
      costPrice: (map['cost_price'] as num).toDouble(),
      sellingPrice: (map['selling_price'] as num).toDouble(),
      totalCostValue: (map['total_cost_value'] as num).toDouble(),
      totalSellingValue: (map['total_selling_value'] as num).toDouble(),
      stockStatus: map['stock_status'] as String,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'unit': unit,
      'quantity': quantity,
      'minQuantity': minQuantity,
      'costPrice': costPrice,
      'sellingPrice': sellingPrice,
      'totalCostValue': totalCostValue,
      'totalSellingValue': totalSellingValue,
      'stockStatus': stockStatus,
    };
  }
}

/// عنصر قائمة الدخل
class ProfitLossItem {
  final int accountId;
  final String accountCode;
  final String accountName;
  final double amount;

  const ProfitLossItem({
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    required this.amount,
  });

  factory ProfitLossItem.fromMap(Map<String, dynamic> map) {
    return ProfitLossItem(
      accountId: map['account_id'] as int,
      accountCode: map['account_code'] as String,
      accountName: map['account_name'] as String,
      amount: (map['amount'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'accountId': accountId,
      'accountCode': accountCode,
      'accountName': accountName,
      'amount': amount,
    };
  }
}

/// عنصر الميزانية العمومية
class BalanceSheetItem {
  final int accountId;
  final String accountCode;
  final String accountName;
  final double balance;

  const BalanceSheetItem({
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    required this.balance,
  });

  factory BalanceSheetItem.fromMap(Map<String, dynamic> map) {
    return BalanceSheetItem(
      accountId: map['account_id'] as int,
      accountCode: map['account_code'] as String,
      accountName: map['account_name'] as String,
      balance: (map['balance'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'accountId': accountId,
      'accountCode': accountCode,
      'accountName': accountName,
      'balance': balance,
    };
  }
}

/// عنصر أعمار العملاء
class CustomerAgingItem {
  final int id;
  final String code;
  final String name;
  final double balance;
  final double currentAmount;
  final double days31To60;
  final double days61To90;
  final double over90Days;

  const CustomerAgingItem({
    required this.id,
    required this.code,
    required this.name,
    required this.balance,
    required this.currentAmount,
    required this.days31To60,
    required this.days61To90,
    required this.over90Days,
  });

  factory CustomerAgingItem.fromMap(Map<String, dynamic> map) {
    return CustomerAgingItem(
      id: map['id'] as int,
      code: map['code'] as String,
      name: map['name'] as String,
      balance: (map['balance'] as num).toDouble(),
      currentAmount: (map['current_amount'] as num).toDouble(),
      days31To60: (map['days_31_60'] as num).toDouble(),
      days61To90: (map['days_61_90'] as num).toDouble(),
      over90Days: (map['over_90_days'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'balance': balance,
      'currentAmount': currentAmount,
      'days31To60': days31To60,
      'days61To90': days61To90,
      'over90Days': over90Days,
    };
  }
}

/// عنصر تحليل المبيعات
class SalesAnalysisItem {
  final String saleDate;
  final int invoiceCount;
  final double totalSales;
  final double averageInvoice;
  final double totalTax;

  const SalesAnalysisItem({
    required this.saleDate,
    required this.invoiceCount,
    required this.totalSales,
    required this.averageInvoice,
    required this.totalTax,
  });

  factory SalesAnalysisItem.fromMap(Map<String, dynamic> map) {
    return SalesAnalysisItem(
      saleDate: map['sale_date'] as String,
      invoiceCount: map['invoice_count'] as int,
      totalSales: (map['total_sales'] as num).toDouble(),
      averageInvoice: (map['average_invoice'] as num).toDouble(),
      totalTax: (map['total_tax'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'saleDate': saleDate,
      'invoiceCount': invoiceCount,
      'totalSales': totalSales,
      'averageInvoice': averageInvoice,
      'totalTax': totalTax,
    };
  }
}

/// عنصر تحليل المشتريات
class PurchaseAnalysisItem {
  final String purchaseDate;
  final int invoiceCount;
  final double totalPurchases;
  final double averageInvoice;
  final double totalTax;

  const PurchaseAnalysisItem({
    required this.purchaseDate,
    required this.invoiceCount,
    required this.totalPurchases,
    required this.averageInvoice,
    required this.totalTax,
  });

  factory PurchaseAnalysisItem.fromMap(Map<String, dynamic> map) {
    return PurchaseAnalysisItem(
      purchaseDate: map['purchase_date'] as String,
      invoiceCount: map['invoice_count'] as int,
      totalPurchases: (map['total_purchases'] as num).toDouble(),
      averageInvoice: (map['average_invoice'] as num).toDouble(),
      totalTax: (map['total_tax'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'purchaseDate': purchaseDate,
      'invoiceCount': invoiceCount,
      'totalPurchases': totalPurchases,
      'averageInvoice': averageInvoice,
      'totalTax': totalTax,
    };
  }
}
