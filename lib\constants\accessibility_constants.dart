import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// ثوابت إمكانية الوصول (Accessibility Constants)
/// تحتوي على جميع النصوص والتسميات المستخدمة لدعم قارئ الشاشة
/// مع ميزات متقدمة لتحسين تجربة المستخدمين ذوي الاحتياجات الخاصة
class AccessibilityConstants {
  // ===============================
  // إعدادات إمكانية الوصول المتقدمة
  // ===============================

  // أحجام الخط لإمكانية الوصول
  static const double minFontSize = 12.0;
  static const double defaultFontSize = 16.0;
  static const double largeFontSize = 20.0;
  static const double extraLargeFontSize = 24.0;

  // نسب التباين المطلوبة
  static const double minimumContrastRatio = 4.5;
  static const double enhancedContrastRatio = 7.0;

  // أحجام المناطق القابلة للنقر
  static const double minTouchTargetSize = 48.0;
  static const double recommendedTouchTargetSize = 56.0;

  // مدة الرسوم المتحركة لإمكانية الوصول
  static const Duration reducedMotionDuration = Duration(milliseconds: 100);
  static const Duration normalMotionDuration = Duration(milliseconds: 300);
  // ===============================
  // تسميات الشاشات الرئيسية
  // ===============================

  static const String homeScreenTitle = 'الشاشة الرئيسية لدفتر الحسابات الذكي';
  static const String accountsScreenTitle = 'شاشة دليل الحسابات';
  static const String journalEntriesScreenTitle = 'شاشة القيود المحاسبية';
  static const String invoicesScreenTitle = 'شاشة الفواتير';
  static const String reportsScreenTitle = 'شاشة التقارير';
  static const String settingsScreenTitle = 'شاشة الإعدادات';

  // ===============================
  // تسميات الأزرار العامة
  // ===============================

  static const String addButton = 'زر إضافة';
  static const String editButton = 'زر تعديل';
  static const String deleteButton = 'زر حذف';
  static const String saveButton = 'زر حفظ';
  static const String cancelButton = 'زر إلغاء';
  static const String refreshButton = 'زر تحديث';
  static const String searchButton = 'زر البحث';
  static const String clearButton = 'زر مسح';
  static const String backButton = 'زر العودة';
  static const String nextButton = 'زر التالي';
  static const String previousButton = 'زر السابق';

  // ===============================
  // تسميات حقول الإدخال
  // ===============================

  static const String searchField = 'حقل البحث';
  static const String nameField = 'حقل الاسم';
  static const String codeField = 'حقل الرمز';
  static const String amountField = 'حقل المبلغ';
  static const String descriptionField = 'حقل الوصف';
  static const String dateField = 'حقل التاريخ';
  static const String phoneField = 'حقل رقم الهاتف';
  static const String emailField = 'حقل البريد الإلكتروني';
  static const String addressField = 'حقل العنوان';

  // ===============================
  // تسميات البطاقات والعناصر
  // ===============================

  static const String dashboardCard = 'بطاقة لوحة التحكم';
  static const String accountCard = 'بطاقة حساب';
  static const String journalEntryCard = 'بطاقة قيد محاسبي';
  static const String invoiceCard = 'بطاقة فاتورة';
  static const String customerCard = 'بطاقة عميل';
  static const String supplierCard = 'بطاقة مورد';

  // ===============================
  // تسميات الإحصائيات
  // ===============================

  static const String totalAssets = 'إجمالي الأصول';
  static const String todaySales = 'مبيعات اليوم';
  static const String accountsCount = 'عدد الحسابات';
  static const String quickStats = 'إحصائيات سريعة';

  // ===============================
  // تسميات الفلاتر
  // ===============================

  static const String filterAll = 'فلتر الكل';
  static const String filterAssets = 'فلتر الأصول';
  static const String filterLiabilities = 'فلتر الخصوم';
  static const String filterRevenues = 'فلتر الإيرادات';
  static const String filterExpenses = 'فلتر المصروفات';
  static const String filterPurchases = 'فلتر المشتريات';
  static const String filterSales = 'فلتر المبيعات';
  static const String filterInventory = 'فلتر المخزون';

  // ===============================
  // رسائل التلميحات (Hints)
  // ===============================

  static const String tapToNavigate = 'اضغط للانتقال';
  static const String tapToEdit = 'اضغط للتعديل';
  static const String tapToDelete = 'اضغط للحذف';
  static const String tapToAdd = 'اضغط للإضافة';
  static const String tapToSave = 'اضغط للحفظ';
  static const String tapToCancel = 'اضغط للإلغاء';
  static const String tapToRefresh = 'اضغط للتحديث';
  static const String tapToSearch = 'اضغط للبحث';
  static const String tapToClear = 'اضغط للمسح';
  static const String typeToSearch = 'اكتب للبحث';
  static const String selectFromList = 'اختر من القائمة';
  static const String enterValue = 'أدخل القيمة';

  // ===============================
  // تسميات الحالات
  // ===============================

  static const String selectedState = 'مُحدد';
  static const String unselectedState = 'غير مُحدد';
  static const String enabledState = 'مُفعل';
  static const String disabledState = 'مُعطل';
  static const String loadingState = 'جاري التحميل';
  static const String emptyState = 'فارغ';
  static const String errorState = 'خطأ';

  // ===============================
  // تسميات التنقل
  // ===============================

  static const String mainNavigation = 'التنقل الرئيسي';
  static const String breadcrumb = 'مسار التنقل';
  static const String tabNavigation = 'تنقل الألسنة';
  static const String pageNavigation = 'تنقل الصفحات';

  // ===============================
  // تسميات القوائم والجداول
  // ===============================

  static const String accountsList = 'قائمة الحسابات';
  static const String journalEntriesList = 'قائمة القيود المحاسبية';
  static const String invoicesList = 'قائمة الفواتير';
  static const String customersList = 'قائمة العملاء';
  static const String suppliersList = 'قائمة الموردين';
  static const String dataTable = 'جدول البيانات';
  static const String listItem = 'عنصر في القائمة';
  static const String tableRow = 'صف في الجدول';
  static const String tableHeader = 'رأس الجدول';

  // ===============================
  // تسميات النماذج والحوارات
  // ===============================

  static const String addForm = 'نموذج إضافة';
  static const String editForm = 'نموذج تعديل';
  static const String deleteDialog = 'حوار الحذف';
  static const String confirmDialog = 'حوار التأكيد';
  static const String alertDialog = 'حوار تنبيه';
  static const String infoDialog = 'حوار معلومات';

  // ===============================
  // تسميات الأيقونات
  // ===============================

  static const String logoIcon = 'أيقونة الشعار';
  static const String menuIcon = 'أيقونة القائمة';
  static const String homeIcon = 'أيقونة الرئيسية';
  static const String accountsIcon = 'أيقونة الحسابات';
  static const String journalIcon = 'أيقونة القيود';
  static const String invoicesIcon = 'أيقونة الفواتير';
  static const String reportsIcon = 'أيقونة التقارير';
  static const String settingsIcon = 'أيقونة الإعدادات';
  static const String addIcon = 'أيقونة إضافة';
  static const String editIcon = 'أيقونة تعديل';
  static const String deleteIcon = 'أيقونة حذف';
  static const String searchIcon = 'أيقونة البحث';
  static const String clearIcon = 'أيقونة المسح';
  static const String refreshIcon = 'أيقونة التحديث';

  // ===============================
  // دوال مساعدة لبناء التسميات
  // ===============================

  /// بناء تسمية للبطاقة
  static String buildCardLabel(String type, String title) {
    return 'بطاقة $type: $title';
  }

  /// بناء تسمية للزر
  static String buildButtonLabel(String action, String target) {
    return 'زر $action $target';
  }

  /// بناء تسمية للحقل
  static String buildFieldLabel(String fieldName, String hint) {
    return 'حقل $fieldName - $hint';
  }

  /// بناء تسمية للفلتر
  static String buildFilterLabel(String filterType, bool isSelected) {
    final state = isSelected ? selectedState : unselectedState;
    return 'فلتر $filterType - $state';
  }

  /// بناء تسمية للحالة
  static String buildStateLabel(String item, String state) {
    return '$item - $state';
  }

  /// بناء تلميح للإجراء
  static String buildActionHint(String action, String target) {
    return 'اضغط $action $target';
  }

  /// بناء وصف للقائمة
  static String buildListDescription(String listType, int itemCount) {
    return '$listType تحتوي على $itemCount عنصر';
  }

  /// بناء تسمية للتنقل
  static String buildNavigationLabel(String from, String to) {
    return 'الانتقال من $from إلى $to';
  }

  // ===============================
  // دوال إمكانية الوصول المتقدمة
  // ===============================

  /// الحصول على حجم الخط المناسب لإمكانية الوصول
  static double getAccessibleFontSize(
    double baseSize, {
    bool isLargeText = false,
  }) {
    if (isLargeText) {
      return (baseSize * 1.3).clamp(minFontSize, extraLargeFontSize);
    }
    return baseSize.clamp(minFontSize, largeFontSize);
  }

  /// التحقق من حجم الهدف القابل للنقر
  static bool isValidTouchTarget(double size) {
    return size >= minTouchTargetSize;
  }

  /// إنشاء خصائص إمكانية الوصول للمكون
  static SemanticsProperties createSemanticsProperties({
    String? label,
    String? hint,
    String? value,
    bool? button,
    bool? textField,
    bool? focusable,
    bool? focused,
    bool? enabled,
    bool? checked,
    bool? selected,
    bool? expanded,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    ValueChanged<String>? onSetText,
  }) {
    return SemanticsProperties(
      label: label,
      hint: hint,
      value: value,
      button: button,
      textField: textField,
      focusable: focusable,
      focused: focused,
      enabled: enabled,
      checked: checked,
      selected: selected,
      expanded: expanded,
      onTap: onTap,
      onLongPress: onLongPress,
      onSetText: onSetText,
    );
  }

  /// مكون مخصص لإمكانية الوصول
  static Widget accessibleWidget({
    required Widget child,
    String? label,
    String? hint,
    String? value,
    bool excludeSemantics = false,
    bool? button,
    bool? textField,
    bool? focusable,
    bool? enabled,
    VoidCallback? onTap,
  }) {
    if (excludeSemantics) {
      return ExcludeSemantics(child: child);
    }

    return Semantics(
      label: label,
      hint: hint,
      value: value,
      button: button,
      textField: textField,
      focusable: focusable,
      enabled: enabled,
      onTap: onTap,
      child: child,
    );
  }

  /// إعلان رسالة لقارئ الشاشة
  static void announceMessage(String message) {
    SemanticsService.announce(message, TextDirection.rtl);
  }

  /// إعلان تغيير في التركيز
  static void announceFocusChange(String message) {
    SemanticsService.announce('تغيير التركيز: $message', TextDirection.rtl);
  }

  /// إعلان تغيير في التحديد
  static void announceSelectionChange(String message) {
    SemanticsService.announce('تغيير التحديد: $message', TextDirection.rtl);
  }

  /// إعلان حالة التحميل
  static void announceLoadingState(bool isLoading) {
    final message = isLoading ? loadingState : 'انتهى التحميل';
    SemanticsService.announce(message, TextDirection.rtl);
  }

  /// إعلان نتائج البحث
  static void announceSearchResults(int count) {
    final message = count > 0
        ? 'تم العثور على $count نتيجة'
        : 'لم يتم العثور على نتائج';
    SemanticsService.announce(message, TextDirection.rtl);
  }
}
