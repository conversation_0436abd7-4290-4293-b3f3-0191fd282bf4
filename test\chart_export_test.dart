import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/services/chart_export_service.dart';
import 'package:smart_ledger/widgets/interactive_chart_widget.dart';
import 'package:smart_ledger/services/chart_service.dart';

void main() {
  group('Chart Export Service Tests', () {
    testWidgets('Chart export service should handle null boundary gracefully', (
      WidgetTester tester,
    ) async {
      // إنشاء GlobalKey فارغ
      final GlobalKey testKey = GlobalKey();

      // محاولة حفظ رسم بياني بدون RepaintBoundary
      final result = await ChartExportService.saveChartAsImage(
        repaintBoundaryKey: testKey,
        reportType: 'test_chart',
      );

      // يجب أن يكون النتيجة null لأن المفتاح لا يحتوي على RepaintBoundary
      expect(result, isNull);
    });

    testWidgets('Interactive chart widget should have save and share buttons', (
      WidgetTester tester,
    ) async {
      // إنشاء بيانات تجريبية
      final testData = [
        {'name': 'البند الأول', 'value': 100.0},
        {'name': 'البند الثاني', 'value': 200.0},
        {'name': 'البند الثالث', 'value': 150.0},
      ];

      // بناء الويدجت
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InteractiveChartWidget(
              reportType: 'test_report',
              data: testData,
              initialChartType: ChartType.bar,
            ),
          ),
        ),
      );

      // التحقق من وجود أزرار الحفظ والمشاركة
      expect(find.byIcon(Icons.save_alt), findsOneWidget);
      expect(find.byIcon(Icons.share), findsOneWidget);
      expect(find.byIcon(Icons.fullscreen), findsOneWidget);
    });

    testWidgets('Chart widget should be wrapped with RepaintBoundary', (
      WidgetTester tester,
    ) async {
      // إنشاء بيانات تجريبية
      final testData = [
        {'name': 'البند الأول', 'value': 100.0},
        {'name': 'البند الثاني', 'value': 200.0},
      ];

      // بناء الويدجت
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InteractiveChartWidget(
              reportType: 'test_report',
              data: testData,
              initialChartType: ChartType.bar,
            ),
          ),
        ),
      );

      // التحقق من وجود RepaintBoundary
      expect(find.byType(RepaintBoundary), findsAtLeastNWidgets(1));
    });

    test('Chart export service should handle image format enum', () {
      // اختبار تنسيقات الصور المختلفة
      expect(ImageFormat.png, isNotNull);
      expect(ImageFormat.jpg, isNotNull);
      expect(ImageFormat.values.length, equals(2));
    });
  });

  group('Chart Export Integration Tests', () {
    testWidgets('Save button should trigger save functionality', (
      WidgetTester tester,
    ) async {
      // إنشاء بيانات تجريبية
      final testData = [
        {'name': 'البند الأول', 'value': 100.0},
      ];

      // بناء الويدجت
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InteractiveChartWidget(
              reportType: 'test_report',
              data: testData,
              initialChartType: ChartType.bar,
            ),
          ),
        ),
      );

      // العثور على زر الحفظ والنقر عليه
      final saveButton = find.byIcon(Icons.save_alt);
      expect(saveButton, findsOneWidget);

      await tester.tap(saveButton);
      await tester.pump();

      // التحقق من عدم وجود أخطاء في التطبيق
      expect(tester.takeException(), isNull);
    });

    testWidgets('Share button should trigger share functionality', (
      WidgetTester tester,
    ) async {
      // إنشاء بيانات تجريبية
      final testData = [
        {'name': 'البند الأول', 'value': 100.0},
      ];

      // بناء الويدجت
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InteractiveChartWidget(
              reportType: 'test_report',
              data: testData,
              initialChartType: ChartType.bar,
            ),
          ),
        ),
      );

      // العثور على زر المشاركة والنقر عليه
      final shareButton = find.byIcon(Icons.share);
      expect(shareButton, findsOneWidget);

      await tester.tap(shareButton);
      await tester.pump();

      // التحقق من عدم وجود أخطاء في التطبيق
      expect(tester.takeException(), isNull);
    });
  });
}
