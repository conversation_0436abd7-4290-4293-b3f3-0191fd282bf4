# تحسينات واجهة المستخدم وتجربة الاستخدام - Smart Ledger

## 🎨 تحسينات إمكانية الوصول (Accessibility)

### 1. إضافة Semantic Labels

```dart
// الكود الحالي (بدون accessibility):
Widget _buildDashboardCard() {
  return Card(
    child: InkWell(
      onTap: widget.onTap,
      child: Column(
        children: [
          Icon(widget.icon),
          Text(widget.title),
          Text(widget.subtitle),
        ],
      ),
    ),
  );
}

// الكود المحسن (مع accessibility):
Widget _buildDashboardCard() {
  return Semantics(
    label: 'بطاقة ${widget.title}',
    hint: '${widget.subtitle}. اضغط للانتقال إلى ${widget.title}',
    button: true,
    enabled: true,
    child: Card(
      child: InkWell(
        onTap: widget.onTap,
        child: <PERSON>umn(
          children: [
            Semantics(
              excludeSemantics: true, // تجاهل الأيقونة في قارئ الشاشة
              child: Icon(widget.icon),
            ),
            Text(
              widget.title,
              semanticsLabel: widget.title,
            ),
            Text(
              widget.subtitle,
              semanticsLabel: widget.subtitle,
            ),
          ],
        ),
      ),
    ),
  );
}
```

### 2. تحسين حقول الإدخال للوصول

```dart
class AccessibleTextFormField extends StatelessWidget {
  final String label;
  final String? hint;
  final String? semanticLabel;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final bool readOnly;
  
  const AccessibleTextFormField({
    super.key,
    required this.label,
    this.hint,
    this.semanticLabel,
    this.controller,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.readOnly = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? label,
      hint: hint,
      textField: true,
      child: TextFormField(
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        obscureText: obscureText,
        onTap: onTap,
        readOnly: readOnly,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: const OutlineInputBorder(),
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          // تحسين التباين للوضوح
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: AppColors.primary,
              width: 2.0,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: AppColors.error,
              width: 2.0,
            ),
          ),
        ),
        // إعدادات إمكانية الوصول
        enableInteractiveSelection: true,
        toolbarOptions: const ToolbarOptions(
          copy: true,
          cut: true,
          paste: true,
          selectAll: true,
        ),
      ),
    );
  }
}
```

### 3. إضافة Focus Management

```dart
class AccessibleFormScreen extends StatefulWidget {
  @override
  State<AccessibleFormScreen> createState() => _AccessibleFormScreenState();
}

class _AccessibleFormScreenState extends State<AccessibleFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final List<FocusNode> _focusNodes = [];
  
  @override
  void initState() {
    super.initState();
    // إنشاء focus nodes للحقول
    for (int i = 0; i < 5; i++) {
      _focusNodes.add(FocusNode());
    }
  }
  
  @override
  void dispose() {
    for (final node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }
  
  void _moveToNextField(int currentIndex) {
    if (currentIndex < _focusNodes.length - 1) {
      FocusScope.of(context).requestFocus(_focusNodes[currentIndex + 1]);
    } else {
      FocusScope.of(context).unfocus();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نموذج محسن لإمكانية الوصول'),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              AccessibleTextFormField(
                label: 'اسم الحساب',
                hint: 'أدخل اسم الحساب',
                semanticLabel: 'حقل اسم الحساب، مطلوب',
                focusNode: _focusNodes[0],
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (_) => _moveToNextField(0),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'اسم الحساب مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AccessibleTextFormField(
                label: 'كود الحساب',
                hint: 'أدخل كود الحساب',
                semanticLabel: 'حقل كود الحساب، مطلوب',
                focusNode: _focusNodes[1],
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (_) => _moveToNextField(1),
                keyboardType: TextInputType.text,
              ),
              // المزيد من الحقول...
            ],
          ),
        ),
      ),
    );
  }
}
```

## 📱 تحسينات التصميم المتجاوب

### 4. إضافة Responsive Layout

```dart
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= 768) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}

// استخدام التصميم المتجاوب
class ResponsiveHomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ResponsiveLayout(
      mobile: _buildMobileLayout(),
      tablet: _buildTabletLayout(),
      desktop: _buildDesktopLayout(),
    );
  }
  
  Widget _buildMobileLayout() {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.1,
      children: _buildDashboardCards(),
    );
  }
  
  Widget _buildTabletLayout() {
    return GridView.count(
      crossAxisCount: 3,
      crossAxisSpacing: 20,
      mainAxisSpacing: 20,
      childAspectRatio: 1.2,
      children: _buildDashboardCards(),
    );
  }
  
  Widget _buildDesktopLayout() {
    return GridView.count(
      crossAxisCount: 4,
      crossAxisSpacing: 24,
      mainAxisSpacing: 24,
      childAspectRatio: 1.3,
      children: _buildDashboardCards(),
    );
  }
}
```

### 5. تحسين الأحجام والمسافات

```dart
class AppDimensions {
  // أحجام متجاوبة بناءً على حجم الشاشة
  static double getResponsiveSize(BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1200) {
      return desktop ?? tablet ?? mobile;
    } else if (screenWidth >= 768) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
  
  // مسافات متجاوبة
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1200) {
      return const EdgeInsets.all(32);
    } else if (screenWidth >= 768) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(16);
    }
  }
  
  // أحجام النصوص المتجاوبة
  static double getResponsiveFontSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= 1200) {
      return baseSize * 1.2;
    } else if (screenWidth >= 768) {
      return baseSize * 1.1;
    } else {
      return baseSize;
    }
  }
}

// استخدام الأحجام المتجاوبة
class ResponsiveCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  
  const ResponsiveCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: AppDimensions.getResponsivePadding(context),
      child: Column(
        children: [
          Icon(
            icon,
            size: AppDimensions.getResponsiveSize(
              context,
              mobile: 32,
              tablet: 40,
              desktop: 48,
            ),
          ),
          SizedBox(
            height: AppDimensions.getResponsiveSize(
              context,
              mobile: 8,
              tablet: 12,
              desktop: 16,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: AppDimensions.getResponsiveFontSize(context, 16),
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: AppDimensions.getResponsiveFontSize(context, 14),
            ),
          ),
        ],
      ),
    );
  }
}
```

## ⌨️ إضافة Keyboard Shortcuts

### 6. نظام اختصارات لوحة المفاتيح

```dart
class KeyboardShortcuts extends StatefulWidget {
  final Widget child;
  
  const KeyboardShortcuts({super.key, required this.child});
  
  @override
  State<KeyboardShortcuts> createState() => _KeyboardShortcutsState();
}

class _KeyboardShortcutsState extends State<KeyboardShortcuts> {
  final Map<LogicalKeySet, VoidCallback> _shortcuts = {};
  
  @override
  void initState() {
    super.initState();
    _initializeShortcuts();
  }
  
  void _initializeShortcuts() {
    _shortcuts.addAll({
      // Ctrl+N: إضافة جديد
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN): () {
        _handleNewAction();
      },
      
      // Ctrl+S: حفظ
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS): () {
        _handleSaveAction();
      },
      
      // Ctrl+F: بحث
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF): () {
        _handleSearchAction();
      },
      
      // F1: مساعدة
      LogicalKeySet(LogicalKeyboardKey.f1): () {
        _showHelpDialog();
      },
      
      // Escape: إلغاء
      LogicalKeySet(LogicalKeyboardKey.escape): () {
        _handleCancelAction();
      },
      
      // Ctrl+Z: تراجع
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyZ): () {
        _handleUndoAction();
      },
      
      // Ctrl+Y: إعادة
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyY): () {
        _handleRedoAction();
      },
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: _shortcuts,
      child: Actions(
        actions: {
          NewIntent: CallbackAction<NewIntent>(
            onInvoke: (intent) => _handleNewAction(),
          ),
          SaveIntent: CallbackAction<SaveIntent>(
            onInvoke: (intent) => _handleSaveAction(),
          ),
          SearchIntent: CallbackAction<SearchIntent>(
            onInvoke: (intent) => _handleSearchAction(),
          ),
        },
        child: Focus(
          autofocus: true,
          child: widget.child,
        ),
      ),
    );
  }
  
  void _handleNewAction() {
    // منطق إضافة جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة جديد (Ctrl+N)')),
    );
  }
  
  void _handleSaveAction() {
    // منطق الحفظ
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('حفظ (Ctrl+S)')),
    );
  }
  
  void _handleSearchAction() {
    // منطق البحث
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('بحث (Ctrl+F)')),
    );
  }
  
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختصارات لوحة المفاتيح'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ctrl+N: إضافة جديد'),
            Text('Ctrl+S: حفظ'),
            Text('Ctrl+F: بحث'),
            Text('F1: مساعدة'),
            Text('Escape: إلغاء'),
            Text('Ctrl+Z: تراجع'),
            Text('Ctrl+Y: إعادة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
  
  void _handleCancelAction() {
    Navigator.of(context).maybePop();
  }
  
  void _handleUndoAction() {
    // منطق التراجع
    UndoRedoService.undo();
  }
  
  void _handleRedoAction() {
    // منطق الإعادة
    UndoRedoService.redo();
  }
}

// تعريف الأوامر
class NewIntent extends Intent {}
class SaveIntent extends Intent {}
class SearchIntent extends Intent {}
```

### 7. نظام Undo/Redo

```dart
class UndoRedoService {
  static final List<UndoableAction> _undoStack = [];
  static final List<UndoableAction> _redoStack = [];
  static const int maxStackSize = 50;
  
  static void executeAction(UndoableAction action) {
    action.execute();
    _undoStack.add(action);
    _redoStack.clear(); // مسح redo stack عند تنفيذ عمل جديد
    
    // الحفاظ على حجم محدود للذاكرة
    if (_undoStack.length > maxStackSize) {
      _undoStack.removeAt(0);
    }
  }
  
  static bool canUndo() => _undoStack.isNotEmpty;
  static bool canRedo() => _redoStack.isNotEmpty;
  
  static void undo() {
    if (canUndo()) {
      final action = _undoStack.removeLast();
      action.undo();
      _redoStack.add(action);
    }
  }
  
  static void redo() {
    if (canRedo()) {
      final action = _redoStack.removeLast();
      action.execute();
      _undoStack.add(action);
    }
  }
  
  static void clear() {
    _undoStack.clear();
    _redoStack.clear();
  }
}

abstract class UndoableAction {
  void execute();
  void undo();
  String get description;
}

class AddAccountAction extends UndoableAction {
  final Account account;
  final AccountService accountService;
  int? accountId;
  
  AddAccountAction(this.account, this.accountService);
  
  @override
  void execute() async {
    accountId = await accountService.insertAccount(account);
  }
  
  @override
  void undo() async {
    if (accountId != null) {
      await accountService.deleteAccount(accountId!);
    }
  }
  
  @override
  String get description => 'إضافة حساب: ${account.name}';
}

class UpdateAccountAction extends UndoableAction {
  final Account oldAccount;
  final Account newAccount;
  final AccountService accountService;
  
  UpdateAccountAction(this.oldAccount, this.newAccount, this.accountService);
  
  @override
  void execute() async {
    await accountService.updateAccount(newAccount);
  }
  
  @override
  void undo() async {
    await accountService.updateAccount(oldAccount);
  }
  
  @override
  String get description => 'تعديل حساب: ${newAccount.name}';
}
```

## 🎯 تحسينات تجربة المستخدم

### 8. تحسين رسائل الخطأ والتحميل

```dart
class UserFriendlyErrorHandler {
  static String getLocalizedErrorMessage(dynamic error) {
    if (error is ValidationException) {
      return error.message;
    } else if (error is DatabaseException) {
      return _getDatabaseErrorMessage(error);
    } else if (error is FormatException) {
      return 'تنسيق البيانات غير صحيح';
    } else if (error is TimeoutException) {
      return 'انتهت مهلة العملية، يرجى المحاولة مرة أخرى';
    } else {
      return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
  }
  
  static String _getDatabaseErrorMessage(DatabaseException error) {
    final message = error.toString().toLowerCase();
    
    if (message.contains('unique constraint')) {
      return 'هذا الكود موجود مسبقاً، يرجى استخدام كود آخر';
    } else if (message.contains('foreign key constraint')) {
      return 'لا يمكن حذف هذا العنصر لأنه مرتبط ببيانات أخرى';
    } else if (message.contains('not null constraint')) {
      return 'يرجى ملء جميع الحقول المطلوبة';
    } else {
      return 'حدث خطأ في قاعدة البيانات';
    }
  }
  
  static void showErrorSnackBar(BuildContext context, dynamic error) {
    final message = getLocalizedErrorMessage(error);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'موافق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

// مكون تحميل محسن
class SmartLoadingWidget extends StatelessWidget {
  final String? message;
  final double? progress;
  
  const SmartLoadingWidget({
    super.key,
    this.message,
    this.progress,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (progress != null)
                CircularProgressIndicator(value: progress)
              else
                const CircularProgressIndicator(),
              
              if (message != null) ...[
                const SizedBox(height: 16),
                Text(
                  message!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
```

---

**ملاحظة:** هذه التحسينات ستجعل التطبيق أكثر سهولة في الاستخدام وإمكانية وصول لجميع المستخدمين، بما في ذلك ذوي الاحتياجات الخاصة.
