# هيكل مشروع Smart Ledger

## 📁 الهيكل العام للمشروع

```
smart_ledger/
├── 📁 android/              # ملفات Android
├── 📁 build/                # ملفات البناء (مؤقتة)
├── 📁 docs/                 # الوثائق والتقارير
├── 📁 ios/                  # ملفات iOS
├── 📁 lib/                  # الكود الرئيسي للتطبيق
│   ├── 📁 constants/        # الثوابت والإعدادات
│   ├── 📁 database/         # إعداد قاعدة البيانات
│   ├── 📁 exceptions/       # الاستثناءات المخصصة
│   ├── 📁 models/           # نماذج البيانات
│   ├── 📁 screens/          # شاشات التطبيق
│   ├── 📁 services/         # خدمات قاعدة البيانات والمنطق
│   ├── 📁 utils/            # الأدوات المساعدة
│   ├── 📁 widgets/          # المكونات المخصصة
│   └── 📄 main.dart         # نقطة البداية
├── 📁 linux/                # ملفات Linux
├── 📁 macos/                # ملفات macOS
├── 📁 test/                 # ملفات الاختبار
├── 📁 web/                  # ملفات الويب
├── 📁 windows/              # ملفات Windows
├── 📄 pubspec.yaml          # تبعيات المشروع
├── 📄 README.md             # وصف المشروع
└── 📄 .gitignore            # ملفات مستبعدة من Git
```

## 📂 تفاصيل مجلد lib/

### 🔧 constants/
- `app_constants.dart` - ثوابت التطبيق العامة
- `app_theme.dart` - ثيم وألوان التطبيق

### 🗄️ database/
- `database_helper.dart` - مساعد قاعدة البيانات الرئيسي

### ⚠️ exceptions/
- `validation_exception.dart` - استثناءات التحقق من البيانات

### 📊 models/
- `account.dart` - نموذج الحساب
- `customer.dart` - نموذج العميل
- `supplier.dart` - نموذج المورد
- `item.dart` - نموذج الصنف
- `journal_entry.dart` - نموذج القيد المحاسبي
- `invoice.dart` - نموذج الفاتورة

### 📱 screens/
- `home_screen.dart` - الشاشة الرئيسية
- `accounts_screen.dart` - شاشة الحسابات
- `customers_screen.dart` - شاشة العملاء
- `suppliers_screen.dart` - شاشة الموردين
- `items_screen.dart` - شاشة الأصناف
- `journal_entries_screen.dart` - شاشة القيود المحاسبية
- `invoices_screen.dart` - شاشة الفواتير
- `reports_screen.dart` - شاشة التقارير

### 🔧 services/
- `account_service.dart` - خدمة الحسابات
- `customer_service.dart` - خدمة العملاء
- `supplier_service.dart` - خدمة الموردين
- `item_service.dart` - خدمة الأصناف
- `journal_entry_service.dart` - خدمة القيود المحاسبية
- `invoice_service.dart` - خدمة الفواتير
- `validation_service.dart` - خدمة التحقق من البيانات
- `data_sanitization_service.dart` - خدمة تنظيف البيانات
- `data_protection_service.dart` - خدمة حماية البيانات
- `logging_service.dart` - خدمة التسجيل
- `error_message_service.dart` - خدمة رسائل الخطأ
- `settings_service.dart` - خدمة الإعدادات

### 🛠️ utils/
- `form_validators.dart` - مدققات النماذج
- `form_helper.dart` - مساعد النماذج

### 🧩 widgets/
- `custom_text_field.dart` - حقل نص مخصص
- `custom_button.dart` - زر مخصص
- `loading_widget.dart` - مكون التحميل

## 📋 ملفات الاختبار

### test/
- `widget_test.dart` - اختبارات واجهة المستخدم الأساسية
- `services/` - اختبارات الخدمات
  - `account_service_test_fixed.dart`
  - `customer_service_test_fixed.dart`
  - `item_service_test.dart`
  - `journal_entry_service_test.dart`
  - `supplier_service_test.dart`

## 📚 الوثائق

### docs/
- `comprehensive_code_review_report.md` - تقرير مراجعة الكود الشامل
- `final_summary.md` - الملخص النهائي
- `implementation_roadmap.md` - خارطة طريق التنفيذ
- `performance_fixes_applied.md` - التحسينات المطبقة
- `performance_improvements.md` - تحسينات الأداء
- `security_improvements.md` - التحسينات الأمنية
- `ui_ux_improvements.md` - تحسينات واجهة المستخدم
- `validation_system_guide.md` - دليل نظام التحقق
- `project_structure.md` - هيكل المشروع (هذا الملف)

## 🔄 التحديثات الأخيرة

### ✅ تم إنجازه:
1. **حذف المجلدات المكررة**: إزالة `libconstants/`, `libdatabase/`, إلخ
2. **تنظيف ملفات الاختبار**: تحديث `widget_test.dart` ليناسب التطبيق
3. **تحديث .gitignore**: إضافة ملفات خاصة بـ Smart Ledger
4. **إنشاء وثائق الهيكل**: هذا الملف

### 🎯 المعايير المتبعة:
- **فصل الاهتمامات**: كل مجلد له غرض محدد
- **التسمية الواضحة**: أسماء ملفات ومجلدات واضحة
- **التنظيم المنطقي**: ترتيب منطقي للملفات
- **سهولة الصيانة**: هيكل يسهل إضافة ميزات جديدة

## 📈 التوصيات للمستقبل

1. **إضافة مجلد `assets/`** للصور والخطوط
2. **إنشاء مجلد `l10n/`** للترجمة المتعددة
3. **إضافة مجلد `config/`** لملفات الإعداد
4. **إنشاء `scripts/`** للسكريبتات المساعدة

---

**آخر تحديث**: 13 يوليو 2025  
**المطور**: مجد محمد زياد يسير  
**الحالة**: ✅ مكتمل ومنظم
