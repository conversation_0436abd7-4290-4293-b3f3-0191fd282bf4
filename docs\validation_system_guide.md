# دليل نظام التحقق من صحة البيانات - Smart Ledger

## 📋 نظرة عامة

تم تطوير نظام شامل للتحقق من صحة البيانات وتنظيفها في تطبيق Smart Ledger لضمان جودة البيانات وحماية التطبيق من التهديدات الأمنية.

## 🏗️ مكونات النظام

### 1. ValidationService
خدمة مركزية للتحقق من صحة البيانات تحتوي على:
- التحقق من الحقول الأساسية (مطلوب، طول النص)
- التحقق من البيانات المالية (المبالغ، الكميات)
- التحقق من الأكواد (حسابات، عملاء، موردين، أصناف)
- التحقق من بيانات الاتصال (هاتف، بريد إلكتروني)
- التحقق من التواريخ
- التحقق من القيود المحاسبية
- التحقق من الفواتير
- التحقق من القواعد التجارية

### 2. ErrorMessageService
خدمة رسائل الخطأ الموحدة تحتوي على:
- رسائل الخطأ العامة
- رسائل التحقق من البيانات
- رسائل الأكواد
- رسائل بيانات الاتصال
- رسائل المبالغ والكميات
- رسائل التواريخ
- رسائل القيود المحاسبية
- رسائل الفواتير
- رسائل القواعد التجارية

### 3. DataSanitizationService
خدمة تنظيف البيانات المتقدمة تحتوي على:
- تنظيف النصوص الأساسي
- تنظيف الأرقام والمبالغ
- تنظيف الأكواد
- تنظيف بيانات الاتصال
- تنظيف العناوين والوصف
- تنظيف متقدم للأمان (SQL Injection, XSS)

### 4. DataProtectionService
خدمة حماية البيانات الشاملة تحتوي على:
- مستويات حماية مختلفة (أساسي، متوسط، صارم)
- مراقبة الأمان وتسجيل الأحداث
- إحصائيات الأمان
- حماية شاملة للكيانات

### 5. FormValidators
مجموعة من المدققات للاستخدام في النماذج:
- مدققات الحقول الأساسية
- مدققات الأكواد
- مدققات بيانات الاتصال
- مدققات المبالغ والكميات
- مدققات مخصصة للنماذج
- مدققات مركبة للكيانات

### 6. FormHelper
مساعد النماذج يوفر:
- إنشاء حقول النماذج مع تحقق مدمج
- إدارة النماذج
- تنظيف البيانات قبل الحفظ
- دوال مساعدة للتحقق والواجهة

## 🔧 كيفية الاستخدام

### استخدام ValidationService

```dart
// التحقق من كود الحساب
final validation = ValidationService.validateAccountCode('A1001');
if (!validation.isValid) {
  print(validation.errorMessage);
}

// التحقق من بيانات الحساب كاملة
final accountValidation = ValidationService.validateAccount(
  code: 'A1001',
  name: 'حساب النقدية',
  type: 'asset',
  balance: 1000.0,
);
```

### استخدام FormValidators في النماذج

```dart
TextFormField(
  controller: _codeController,
  validator: FormValidators.accountCode,
  decoration: InputDecoration(labelText: 'كود الحساب'),
)

TextFormField(
  controller: _nameController,
  validator: FormValidators.accountName,
  decoration: InputDecoration(labelText: 'اسم الحساب'),
)
```

### استخدام DataSanitizationService

```dart
// تنظيف بيانات الحساب
final sanitizedData = DataSanitizationService.sanitizeAccountData(
  code: userInput.code,
  name: userInput.name,
  description: userInput.description,
);
```

### استخدام DataProtectionService

```dart
// تنظيف محمي مع مستوى حماية صارم
final cleanText = DataProtectionService.protectedSanitizeText(
  userInput,
  'اسم العميل',
  level: DataProtectionService.ProtectionLevel.strict,
);
```

### استخدام FormHelper

```dart
// إنشاء حقل كود مع تحقق مدمج
FormHelper.buildCodeField(
  controller: _codeController,
  codeType: 'account',
  labelText: 'كود الحساب',
  onGeneratePressed: _generateCode,
)

// إنشاء حقل مبلغ مالي
FormHelper.buildAmountField(
  controller: _balanceController,
  labelText: 'الرصيد الافتتاحي',
  allowZero: true,
)
```

## 🛡️ الأمان

### مستويات الحماية

1. **أساسي (Basic)**: تنظيف أساسي للبيانات
2. **متوسط (Standard)**: تنظيف متوسط مع فحص أمني
3. **صارم (Strict)**: تنظيف صارم مع تسجيل المحاولات المشبوهة

### مراقبة الأمان

```dart
// الحصول على إحصائيات الأمان
final stats = DataProtectionService.getSecurityStats();
print('إجمالي الأحداث الأمنية: ${stats.totalEvents}');

// الحصول على الأحداث الأمنية الأخيرة
final events = DataProtectionService.getRecentSecurityEvents(limit: 10);
```

## 📊 التطبيق في الخدمات

تم تطبيق النظام في جميع الخدمات الأساسية:

### AccountService
- التحقق من صحة بيانات الحساب
- تنظيف البيانات قبل الحفظ
- التحقق من قواعد الحذف

### CustomerService
- التحقق من صحة بيانات العميل
- تنظيف البيانات قبل الحفظ
- التحقق من قواعد الحذف

### SupplierService
- التحقق من صحة بيانات المورد
- تنظيف البيانات قبل الحفظ

### ItemService
- التحقق من صحة بيانات الصنف
- تنظيف البيانات قبل الحفظ

### JournalEntryService
- التحقق من توازن القيد
- التحقق من صحة رقم القيد

## 🔍 استثناءات مخصصة

### ValidationException
```dart
// استثناء للحقل المطلوب
throw ValidationException.required('اسم الحساب');

// استثناء للقيمة المكررة
throw ValidationException.duplicate('كود الحساب', 'A1001');

// استثناء للتنسيق غير الصحيح
throw ValidationException.invalidFormat('رقم الهاتف', '0933123456');
```

### BusinessRuleException
```dart
// استثناء للقيد غير المتوازن
throw BusinessRuleException.unbalancedEntry(1000.0, 900.0);

// استثناء للبيانات المرتبطة
throw BusinessRuleException.hasRelatedData('الحساب', 'قيود محاسبية');
```

## 📈 الفوائد

1. **جودة البيانات**: ضمان صحة ونظافة جميع البيانات المدخلة
2. **الأمان**: حماية من SQL Injection و XSS وغيرها من التهديدات
3. **تجربة المستخدم**: رسائل خطأ واضحة ومفهومة باللغة العربية
4. **سهولة الصيانة**: نظام مركزي موحد للتحقق والتنظيف
5. **المراقبة**: تسجيل ومراقبة الأحداث الأمنية
6. **المرونة**: مستويات حماية مختلفة حسب الحاجة

## 🚀 التطوير المستقبلي

- إضافة المزيد من قواعد التحقق المخصصة
- تطوير واجهة إدارة الأمان
- إضافة تقارير أمنية مفصلة
- تطوير نظام تنبيهات أمنية
- إضافة دعم للتحقق المتقدم (regex مخصص)
