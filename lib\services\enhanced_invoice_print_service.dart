/// خدمة طباعة الفواتير المحسنة بقوالب احترافية متعددة
/// توفر طباعة فواتير بتصاميم مختلفة وقابلة للتخصيص
library;

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../models/invoice_template.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../services/customer_service.dart';
import '../services/supplier_service.dart';
import '../services/item_service.dart';
import '../services/invoice_template_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';

/// خدمة طباعة الفواتير المحسنة
class EnhancedInvoicePrintService {
  static final CustomerService _customerService = CustomerService();
  static final SupplierService _supplierService = SupplierService();
  static final ItemService _itemService = ItemService();
  static final InvoiceTemplateService _templateService =
      InvoiceTemplateService();

  /// طباعة الفاتورة مع معاينة باستخدام قالب محدد
  static Future<void> printInvoiceWithTemplate(
    BuildContext context,
    Invoice invoice, {
    InvoiceTemplate? template,
  }) async {
    try {
      LoggingService.info(
        'بدء طباعة الفاتورة مع قالب محدد',
        category: 'EnhancedInvoicePrint',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'templateId': template?.id,
        },
      );

      // الحصول على القالب المحدد أو الافتراضي
      final selectedTemplate =
          template ?? await _templateService.getDefaultTemplate();
      if (selectedTemplate == null) {
        throw Exception('لا يوجد قالب متاح للطباعة');
      }

      final pdf = await _generateInvoicePDFWithTemplate(
        invoice,
        selectedTemplate,
      );

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'فاتورة_${invoice.invoiceNumber}_${selectedTemplate.name}.pdf',
        format: PdfPageFormat.a4,
      );

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: AppConstants.auditEntityInvoice,
        entityId: invoice.id ?? 0,
        entityName: 'طباعة فاتورة ${invoice.invoiceNumber}',
        newValues: {
          'invoiceNumber': invoice.invoiceNumber,
          'templateId': selectedTemplate.id,
          'templateName': selectedTemplate.name,
          'printDate': DateTime.now().toIso8601String(),
          'printType': 'preview_with_template',
        },
        description: 'تم طباعة الفاتورة باستخدام قالب ${selectedTemplate.name}',
        category: 'EnhancedInvoicePrint',
      );

      LoggingService.info(
        'تم طباعة الفاتورة بنجاح',
        category: 'EnhancedInvoicePrint',
        data: {
          'invoiceNumber': invoice.invoiceNumber,
          'templateName': selectedTemplate.name,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في طباعة الفاتورة',
        category: 'EnhancedInvoicePrint',
        data: {'error': e.toString(), 'invoiceId': invoice.id},
      );
      rethrow;
    }
  }

  /// حفظ الفاتورة كملف PDF باستخدام قالب محدد
  static Future<String?> saveInvoiceAsPDFWithTemplate(
    Invoice invoice, {
    InvoiceTemplate? template,
  }) async {
    try {
      LoggingService.info(
        'بدء حفظ الفاتورة كـ PDF مع قالب',
        category: 'EnhancedInvoicePrint',
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'templateId': template?.id,
        },
      );

      // الحصول على القالب المحدد أو الافتراضي
      final selectedTemplate =
          template ?? await _templateService.getDefaultTemplate();
      if (selectedTemplate == null) {
        throw Exception('لا يوجد قالب متاح للحفظ');
      }

      final pdf = await _generateInvoicePDFWithTemplate(
        invoice,
        selectedTemplate,
      );
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'فاتورة_${invoice.invoiceNumber}_${selectedTemplate.name}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: AppConstants.auditEntityInvoice,
        entityId: invoice.id ?? 0,
        entityName: 'حفظ فاتورة ${invoice.invoiceNumber}',
        newValues: {
          'invoiceNumber': invoice.invoiceNumber,
          'templateId': selectedTemplate.id,
          'templateName': selectedTemplate.name,
          'filePath': filePath,
          'saveDate': DateTime.now().toIso8601String(),
        },
        description:
            'تم حفظ الفاتورة كملف PDF باستخدام قالب ${selectedTemplate.name}',
        category: 'EnhancedInvoicePrint',
      );

      LoggingService.info(
        'تم حفظ الفاتورة كـ PDF بنجاح',
        category: 'EnhancedInvoicePrint',
        data: {'filePath': filePath, 'templateName': selectedTemplate.name},
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ الفاتورة كـ PDF',
        category: 'EnhancedInvoicePrint',
        data: {'error': e.toString(), 'invoiceId': invoice.id},
      );
      return null;
    }
  }

  /// إنشاء PDF للفاتورة باستخدام قالب محدد
  static Future<pw.Document> _generateInvoicePDFWithTemplate(
    Invoice invoice,
    InvoiceTemplate template,
  ) async {
    final pdf = pw.Document();

    // جلب بيانات العميل/المورد
    Customer? customer;
    Supplier? supplier;

    if (invoice.customerId != null) {
      customer = await _customerService.getCustomerById(invoice.customerId!);
    }
    if (invoice.supplierId != null) {
      supplier = await _supplierService.getSupplierById(invoice.supplierId!);
    }

    // جلب بيانات الأصناف
    final List<Map<String, dynamic>> itemsWithDetails = [];
    for (final invoiceItem in invoice.items) {
      final item = await _itemService.getItemById(invoiceItem.itemId);
      itemsWithDetails.add({'invoiceItem': invoiceItem, 'item': item});
    }

    // إنشاء الصفحة حسب نوع القالب
    switch (template.type) {
      case InvoiceTemplateType.classic:
        pdf.addPage(
          _buildClassicTemplate(
            invoice,
            template,
            customer,
            supplier,
            itemsWithDetails,
          ),
        );
        break;
      case InvoiceTemplateType.modern:
        pdf.addPage(
          _buildModernTemplate(
            invoice,
            template,
            customer,
            supplier,
            itemsWithDetails,
          ),
        );
        break;
      case InvoiceTemplateType.minimal:
        pdf.addPage(
          _buildMinimalTemplate(
            invoice,
            template,
            customer,
            supplier,
            itemsWithDetails,
          ),
        );
        break;
      case InvoiceTemplateType.luxury:
        pdf.addPage(
          _buildLuxuryTemplate(
            invoice,
            template,
            customer,
            supplier,
            itemsWithDetails,
          ),
        );
        break;
      case InvoiceTemplateType.custom:
        pdf.addPage(
          _buildCustomTemplate(
            invoice,
            template,
            customer,
            supplier,
            itemsWithDetails,
          ),
        );
        break;
    }

    return pdf;
  }

  /// بناء القالب الكلاسيكي
  static pw.Page _buildClassicTemplate(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
    List<Map<String, dynamic>> itemsWithDetails,
  ) {
    return pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      margin: pw.EdgeInsets.all(template.layout.marginTop),
      header: (context) => _buildClassicHeader(invoice, template),
      footer: (context) => _buildClassicFooter(context, template),
      build: (context) => [
        // معلومات الشركة والعميل/المورد
        _buildClassicCompanyInfo(invoice, template, customer, supplier),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // جدول الأصناف
        _buildClassicItemsTable(itemsWithDetails, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // ملخص الفاتورة
        _buildClassicSummary(invoice, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // الشروط والملاحظات
        if (invoice.terms != null || invoice.notes != null)
          _buildClassicTermsAndNotes(invoice, template),
      ],
    );
  }

  /// بناء القالب الحديث
  static pw.Page _buildModernTemplate(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
    List<Map<String, dynamic>> itemsWithDetails,
  ) {
    return pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      margin: pw.EdgeInsets.all(template.layout.marginTop),
      header: (context) => _buildModernHeader(invoice, template),
      footer: (context) => _buildModernFooter(context, template),
      build: (context) => [
        // معلومات الشركة والعميل/المورد مع تصميم حديث
        _buildModernCompanyInfo(invoice, template, customer, supplier),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // جدول الأصناف بتصميم حديث
        _buildModernItemsTable(itemsWithDetails, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // ملخص الفاتورة بتصميم حديث
        _buildModernSummary(invoice, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // الشروط والملاحظات بتصميم حديث
        if (invoice.terms != null || invoice.notes != null)
          _buildModernTermsAndNotes(invoice, template),
      ],
    );
  }

  /// بناء القالب المبسط
  static pw.Page _buildMinimalTemplate(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
    List<Map<String, dynamic>> itemsWithDetails,
  ) {
    return pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      margin: pw.EdgeInsets.all(template.layout.marginTop),
      header: (context) => _buildMinimalHeader(invoice, template),
      footer: (context) => _buildMinimalFooter(context, template),
      build: (context) => [
        // معلومات مبسطة
        _buildMinimalCompanyInfo(invoice, template, customer, supplier),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // جدول مبسط
        _buildMinimalItemsTable(itemsWithDetails, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // ملخص مبسط
        _buildMinimalSummary(invoice, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // ملاحظات مبسطة
        if (invoice.notes != null) _buildMinimalNotes(invoice, template),
      ],
    );
  }

  /// بناء القالب الفاخر
  static pw.Page _buildLuxuryTemplate(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
    List<Map<String, dynamic>> itemsWithDetails,
  ) {
    return pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      margin: pw.EdgeInsets.all(template.layout.marginTop),
      header: (context) => _buildLuxuryHeader(invoice, template),
      footer: (context) => _buildLuxuryFooter(context, template),
      build: (context) => [
        // معلومات فاخرة
        _buildLuxuryCompanyInfo(invoice, template, customer, supplier),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // جدول فاخر
        _buildLuxuryItemsTable(itemsWithDetails, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // ملخص فاخر
        _buildLuxurySummary(invoice, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // شروط وملاحظات فاخرة
        if (invoice.terms != null || invoice.notes != null)
          _buildLuxuryTermsAndNotes(invoice, template),
      ],
    );
  }

  /// بناء القالب المخصص
  static pw.Page _buildCustomTemplate(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
    List<Map<String, dynamic>> itemsWithDetails,
  ) {
    // القالب المخصص يستخدم إعدادات القالب المحددة بالكامل
    return pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      margin: pw.EdgeInsets.only(
        top: template.layout.marginTop,
        bottom: template.layout.marginBottom,
        left: template.layout.marginLeft,
        right: template.layout.marginRight,
      ),
      header: (context) => _buildCustomHeader(invoice, template),
      footer: (context) => _buildCustomFooter(context, template),
      build: (context) => [
        // معلومات مخصصة
        _buildCustomCompanyInfo(invoice, template, customer, supplier),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // جدول مخصص
        _buildCustomItemsTable(itemsWithDetails, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // ملخص مخصص
        _buildCustomSummary(invoice, template),
        pw.SizedBox(height: template.layout.sectionSpacing),

        // شروط وملاحظات مخصصة
        if (invoice.terms != null || invoice.notes != null)
          _buildCustomTermsAndNotes(invoice, template),
      ],
    );
  }

  // ==================== طرق بناء القالب الكلاسيكي ====================

  /// بناء رأس القالب الكلاسيكي
  static pw.Widget _buildClassicHeader(
    Invoice invoice,
    InvoiceTemplate template,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: template.colors.headerBackground,
        border: pw.Border.all(color: template.colors.border),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // معلومات الشركة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                template.companySettings.name,
                style: pw.TextStyle(
                  fontSize: template.fonts.headerSize,
                  fontWeight: pw.FontWeight.bold,
                  color: template.colors.primary,
                ),
              ),
              if (template.companySettings.showContactInfo) ...[
                pw.SizedBox(height: 5),
                pw.Text(
                  template.companySettings.address,
                  style: pw.TextStyle(
                    fontSize: template.fonts.bodySize,
                    color: template.colors.text,
                  ),
                ),
              ],
            ],
          ),
          // معلومات الفاتورة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                invoice.typeArabic,
                style: pw.TextStyle(
                  fontSize: template.fonts.titleSize,
                  fontWeight: pw.FontWeight.bold,
                  color: template.colors.primary,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'رقم الفاتورة: ${invoice.invoiceNumber}',
                style: pw.TextStyle(
                  fontSize: template.fonts.subtitleSize,
                  color: template.colors.text,
                ),
              ),
              pw.Text(
                'التاريخ: ${DateFormat('yyyy/MM/dd').format(invoice.invoiceDate)}',
                style: pw.TextStyle(
                  fontSize: template.fonts.subtitleSize,
                  color: template.colors.text,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء تذييل القالب الكلاسيكي
  static pw.Widget _buildClassicFooter(
    pw.Context context,
    InvoiceTemplate template,
  ) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(top: 20),
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: template.colors.border)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'صفحة ${context.pageNumber} من ${context.pagesCount}',
            style: pw.TextStyle(
              fontSize: template.fonts.captionSize,
              color: template.colors.text,
            ),
          ),
          if (template.companySettings.website != null)
            pw.Text(
              template.companySettings.website!,
              style: pw.TextStyle(
                fontSize: template.fonts.captionSize,
                color: template.colors.accent,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء معلومات الشركة والعميل للقالب الكلاسيكي
  static pw.Widget _buildClassicCompanyInfo(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
  ) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // معلومات العميل/المورد
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: template.colors.border),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              color: template.layout.showBackground
                  ? template.colors.background
                  : null,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  customer != null ? 'بيانات العميل' : 'بيانات المورد',
                  style: pw.TextStyle(
                    fontSize: template.fonts.subtitleSize,
                    fontWeight: pw.FontWeight.bold,
                    color: template.colors.primary,
                  ),
                ),
                pw.SizedBox(height: 10),
                if (customer != null) ...[
                  pw.Text(
                    'الاسم: ${customer.name}',
                    style: pw.TextStyle(
                      fontSize: template.fonts.bodySize,
                      color: template.colors.text,
                    ),
                  ),
                  if (customer.phone?.isNotEmpty == true)
                    pw.Text(
                      'الهاتف: ${customer.phone}',
                      style: pw.TextStyle(
                        fontSize: template.fonts.bodySize,
                        color: template.colors.text,
                      ),
                    ),
                ] else if (supplier != null) ...[
                  pw.Text(
                    'الاسم: ${supplier.name}',
                    style: pw.TextStyle(
                      fontSize: template.fonts.bodySize,
                      color: template.colors.text,
                    ),
                  ),
                ] else ...[
                  pw.Text(
                    'لا توجد بيانات متاحة',
                    style: pw.TextStyle(
                      fontSize: template.fonts.bodySize,
                      color: template.colors.text,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  // ==================== طرق مساعدة مؤقتة ====================
  // هذه الطرق تحتاج إلى تطوير كامل لكل قالب

  static pw.Widget _buildClassicItemsTable(
    List<Map<String, dynamic>> items,
    InvoiceTemplate template,
  ) {
    return pw.Text('جدول الأصناف - قيد التطوير');
  }

  static pw.Widget _buildClassicSummary(
    Invoice invoice,
    InvoiceTemplate template,
  ) {
    return pw.Text('ملخص الفاتورة - قيد التطوير');
  }

  static pw.Widget _buildClassicTermsAndNotes(
    Invoice invoice,
    InvoiceTemplate template,
  ) {
    return pw.Text('الشروط والملاحظات - قيد التطوير');
  }

  // طرق القوالب الأخرى - مؤقتة
  static pw.Widget _buildModernHeader(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicHeader(invoice, template);
  static pw.Widget _buildModernFooter(
    pw.Context context,
    InvoiceTemplate template,
  ) => _buildClassicFooter(context, template);
  static pw.Widget _buildModernCompanyInfo(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
  ) => _buildClassicCompanyInfo(invoice, template, customer, supplier);
  static pw.Widget _buildModernItemsTable(
    List<Map<String, dynamic>> items,
    InvoiceTemplate template,
  ) => _buildClassicItemsTable(items, template);
  static pw.Widget _buildModernSummary(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicSummary(invoice, template);
  static pw.Widget _buildModernTermsAndNotes(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicTermsAndNotes(invoice, template);

  static pw.Widget _buildMinimalHeader(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicHeader(invoice, template);
  static pw.Widget _buildMinimalFooter(
    pw.Context context,
    InvoiceTemplate template,
  ) => _buildClassicFooter(context, template);
  static pw.Widget _buildMinimalCompanyInfo(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
  ) => _buildClassicCompanyInfo(invoice, template, customer, supplier);
  static pw.Widget _buildMinimalItemsTable(
    List<Map<String, dynamic>> items,
    InvoiceTemplate template,
  ) => _buildClassicItemsTable(items, template);
  static pw.Widget _buildMinimalSummary(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicSummary(invoice, template);
  static pw.Widget _buildMinimalNotes(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicTermsAndNotes(invoice, template);

  static pw.Widget _buildLuxuryHeader(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicHeader(invoice, template);
  static pw.Widget _buildLuxuryFooter(
    pw.Context context,
    InvoiceTemplate template,
  ) => _buildClassicFooter(context, template);
  static pw.Widget _buildLuxuryCompanyInfo(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
  ) => _buildClassicCompanyInfo(invoice, template, customer, supplier);
  static pw.Widget _buildLuxuryItemsTable(
    List<Map<String, dynamic>> items,
    InvoiceTemplate template,
  ) => _buildClassicItemsTable(items, template);
  static pw.Widget _buildLuxurySummary(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicSummary(invoice, template);
  static pw.Widget _buildLuxuryTermsAndNotes(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicTermsAndNotes(invoice, template);

  static pw.Widget _buildCustomHeader(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicHeader(invoice, template);
  static pw.Widget _buildCustomFooter(
    pw.Context context,
    InvoiceTemplate template,
  ) => _buildClassicFooter(context, template);
  static pw.Widget _buildCustomCompanyInfo(
    Invoice invoice,
    InvoiceTemplate template,
    Customer? customer,
    Supplier? supplier,
  ) => _buildClassicCompanyInfo(invoice, template, customer, supplier);
  static pw.Widget _buildCustomItemsTable(
    List<Map<String, dynamic>> items,
    InvoiceTemplate template,
  ) => _buildClassicItemsTable(items, template);
  static pw.Widget _buildCustomSummary(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicSummary(invoice, template);
  static pw.Widget _buildCustomTermsAndNotes(
    Invoice invoice,
    InvoiceTemplate template,
  ) => _buildClassicTermsAndNotes(invoice, template);
}
