import 'undo_redo_service.dart';
import 'account_service.dart';
import 'journal_entry_service.dart';
import 'invoice_service.dart';
import 'item_service.dart';
import 'customer_service.dart';
import 'supplier_service.dart';
import '../models/account.dart';
import '../models/journal_entry.dart';
import '../models/invoice.dart';
import '../models/item.dart';
import '../models/customer.dart';
import '../models/supplier.dart';

/// مساعدات لتطبيق نظام Undo/Redo على الخدمات المختلفة
class UndoRedoHelpers {
  static final UndoRedoService _undoRedoService = UndoRedoService();

  // ===============================
  // مساعدات الحسابات
  // ===============================

  /// إضافة حساب مع دعم Undo/Redo
  static Future<Map<String, dynamic>?> addAccountWithUndo({
    required Map<String, dynamic> accountData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Account
      final account = Account.fromMap(accountData);
      final accountService = AccountService();
      final accountId = await accountService.insertAccount(account);

      // إنشاء البيانات المرجعة
      final result = account.copyWith(id: accountId).toMap();

      final action = AccountAction(
        description: description,
        accountData: result,
        isAdd: true,
        addFunction: (data) async {
          final acc = Account.fromMap(data);
          final service = AccountService();
          final id = await service.insertAccount(acc);
          return acc.copyWith(id: id).toMap();
        },
        deleteFunction: (id) async {
          final service = AccountService();
          await service.deleteAccount(id);
          return true;
        },
      );
      _undoRedoService.addAction(action);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث حساب مع دعم Undo/Redo
  static Future<bool> updateAccountWithUndo({
    required Map<String, dynamic> oldData,
    required Map<String, dynamic> newData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Account
      final account = Account.fromMap(newData);
      final accountService = AccountService();
      final result = await accountService.updateAccount(account);
      final success = result > 0;

      if (success) {
        final action = UpdateAction(
          description: description,
          oldData: oldData,
          newData: newData,
          updateFunction: (data) async {
            final acc = Account.fromMap(data);
            final service = AccountService();
            final updateResult = await service.updateAccount(acc);
            return updateResult > 0;
          },
        );
        _undoRedoService.addAction(action);
      }
      return success;
    } catch (e) {
      rethrow;
    }
  }

  /// حذف حساب مع دعم Undo/Redo
  static Future<bool> deleteAccountWithUndo({
    required int accountId,
    required Map<String, dynamic> accountData,
    required String description,
  }) async {
    try {
      final accountService = AccountService();
      final result = await accountService.deleteAccount(accountId);
      final success = result > 0;

      if (success) {
        final action = AccountAction(
          description: description,
          accountData: accountData,
          isAdd: false,
          addFunction: (data) async {
            final acc = Account.fromMap(data);
            final service = AccountService();
            final id = await service.insertAccount(acc);
            return acc.copyWith(id: id).toMap();
          },
          deleteFunction: (id) async {
            final service = AccountService();
            final deleteResult = await service.deleteAccount(id);
            return deleteResult > 0;
          },
        );
        _undoRedoService.addAction(action);
      }
      return success;
    } catch (e) {
      rethrow;
    }
  }

  // ===============================
  // مساعدات القيود المحاسبية
  // ===============================

  /// إضافة قيد محاسبي مع دعم Undo/Redo
  static Future<Map<String, dynamic>?> addJournalEntryWithUndo({
    required Map<String, dynamic> journalData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن JournalEntry
      final journalEntry = JournalEntry.fromMap(journalData);
      final journalService = JournalEntryService();
      final journalId = await journalService.insertJournalEntry(journalEntry);

      // إنشاء البيانات المرجعة
      final result = journalEntry.copyWith(id: journalId).toMap();

      final action = JournalEntryAction(
        description: description,
        journalData: result,
        isPost: false,
        postFunction: (data) async {
          final entry = JournalEntry.fromMap(data);
          final service = JournalEntryService();
          final id = await service.insertJournalEntry(entry);
          return entry.copyWith(id: id).toMap();
        },
        unpostFunction: (id) async {
          final service = JournalEntryService();
          await service.deleteJournalEntry(id);
          return true;
        },
      );
      _undoRedoService.addAction(action);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// ترحيل قيد محاسبي مع دعم Undo/Redo
  static Future<bool> postJournalEntryWithUndo({
    required int journalId,
    required Map<String, dynamic> journalData,
    required String description,
  }) async {
    try {
      // هنا يجب إضافة منطق الترحيل الفعلي
      // final success = await JournalEntryService.postJournalEntry(journalId);
      final success = true; // مؤقت

      if (success) {
        final action = JournalEntryAction(
          description: description,
          journalData: journalData,
          isPost: true,
          postFunction: (data) async {
            // منطق الترحيل
            return data;
          },
          unpostFunction: (id) async {
            // منطق إلغاء الترحيل
            return true;
          },
        );
        _undoRedoService.addAction(action);
      }
      return success;
    } catch (e) {
      rethrow;
    }
  }

  // ===============================
  // مساعدات الفواتير
  // ===============================

  /// إضافة فاتورة مع دعم Undo/Redo
  static Future<Map<String, dynamic>?> addInvoiceWithUndo({
    required Map<String, dynamic> invoiceData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Invoice
      final invoice = Invoice.fromMap(invoiceData);
      final invoiceService = InvoiceService();
      final invoiceId = await invoiceService.insertInvoice(invoice);

      // إنشاء البيانات المرجعة
      final result = invoice.copyWith(id: invoiceId).toMap();

      final action = InvoiceAction(
        description: description,
        invoiceData: result,
        actionType: 'create',
        createFunction: (data) async {
          final inv = Invoice.fromMap(data);
          final service = InvoiceService();
          final id = await service.insertInvoice(inv);
          return inv.copyWith(id: id).toMap();
        },
        updateFunction: (data) async {
          final inv = Invoice.fromMap(data);
          final service = InvoiceService();
          final updateResult = await service.updateInvoice(inv);
          return updateResult > 0;
        },
        deleteFunction: (id) async {
          final service = InvoiceService();
          final deleteResult = await service.deleteInvoice(id);
          return deleteResult > 0;
        },
        postFunction: (id) async {
          // منطق ترحيل الفاتورة
          return true;
        },
      );
      _undoRedoService.addAction(action);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث فاتورة مع دعم Undo/Redo
  static Future<bool> updateInvoiceWithUndo({
    required Map<String, dynamic> oldData,
    required Map<String, dynamic> newData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Invoice
      final invoice = Invoice.fromMap(newData);
      final invoiceService = InvoiceService();
      final result = await invoiceService.updateInvoice(invoice);
      final success = result > 0;

      if (success) {
        final action = InvoiceAction(
          description: description,
          invoiceData: newData,
          actionType: 'update',
          createFunction: (data) async {
            final inv = Invoice.fromMap(data);
            final service = InvoiceService();
            final id = await service.insertInvoice(inv);
            return inv.copyWith(id: id).toMap();
          },
          updateFunction: (data) async {
            final inv = Invoice.fromMap(data);
            final service = InvoiceService();
            final updateResult = await service.updateInvoice(inv);
            return updateResult > 0;
          },
          deleteFunction: (id) async {
            final service = InvoiceService();
            final deleteResult = await service.deleteInvoice(id);
            return deleteResult > 0;
          },
          postFunction: (id) async => true,
          oldData: oldData,
        );
        _undoRedoService.addAction(action);
      }
      return success;
    } catch (e) {
      rethrow;
    }
  }

  // ===============================
  // مساعدات الأصناف
  // ===============================

  /// إضافة صنف مع دعم Undo/Redo
  static Future<Map<String, dynamic>?> addItemWithUndo({
    required Map<String, dynamic> itemData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Item
      final item = Item.fromMap(itemData);
      final itemService = ItemService();
      final itemId = await itemService.insertItem(item);

      // إنشاء البيانات المرجعة
      final result = item.copyWith(id: itemId).toMap();

      final action = ItemAction(
        description: description,
        itemData: result,
        actionType: 'create',
        createFunction: (data) async {
          final itm = Item.fromMap(data);
          final service = ItemService();
          final id = await service.insertItem(itm);
          return itm.copyWith(id: id).toMap();
        },
        updateFunction: (data) async {
          final itm = Item.fromMap(data);
          final service = ItemService();
          final updateResult = await service.updateItem(itm);
          return updateResult > 0;
        },
        deleteFunction: (id) async {
          final service = ItemService();
          final deleteResult = await service.deleteItem(id);
          return deleteResult > 0;
        },
      );
      _undoRedoService.addAction(action);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  // ===============================
  // مساعدات العملاء
  // ===============================

  /// إضافة عميل مع دعم Undo/Redo
  static Future<Map<String, dynamic>?> addCustomerWithUndo({
    required Map<String, dynamic> customerData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Customer
      final customer = Customer.fromMap(customerData);
      final customerService = CustomerService();
      final customerId = await customerService.insertCustomer(customer);

      // إنشاء البيانات المرجعة
      final result = customer.copyWith(id: customerId).toMap();

      final action = CustomerSupplierAction(
        description: description,
        data: result,
        actionType: 'create',
        entityType: 'customer',
        createFunction: (data) async {
          final cust = Customer.fromMap(data);
          final service = CustomerService();
          final id = await service.insertCustomer(cust);
          return cust.copyWith(id: id).toMap();
        },
        updateFunction: (data) async {
          final cust = Customer.fromMap(data);
          final service = CustomerService();
          final updateResult = await service.updateCustomer(cust);
          return updateResult > 0;
        },
        deleteFunction: (id) async {
          final service = CustomerService();
          final deleteResult = await service.deleteCustomer(id);
          return deleteResult > 0;
        },
      );
      _undoRedoService.addAction(action);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  // ===============================
  // مساعدات الموردين
  // ===============================

  /// إضافة مورد مع دعم Undo/Redo
  static Future<Map<String, dynamic>?> addSupplierWithUndo({
    required Map<String, dynamic> supplierData,
    required String description,
  }) async {
    try {
      // تحويل البيانات إلى كائن Supplier
      final supplier = Supplier.fromMap(supplierData);
      final supplierService = SupplierService();
      final supplierId = await supplierService.insertSupplier(supplier);

      // إنشاء البيانات المرجعة
      final result = supplier.copyWith(id: supplierId).toMap();

      final action = CustomerSupplierAction(
        description: description,
        data: result,
        actionType: 'create',
        entityType: 'supplier',
        createFunction: (data) async {
          final supp = Supplier.fromMap(data);
          final service = SupplierService();
          final id = await service.insertSupplier(supp);
          return supp.copyWith(id: id).toMap();
        },
        updateFunction: (data) async {
          final supp = Supplier.fromMap(data);
          final service = SupplierService();
          final updateResult = await service.updateSupplier(supp);
          return updateResult > 0;
        },
        deleteFunction: (id) async {
          final service = SupplierService();
          final deleteResult = await service.deleteSupplier(id);
          return deleteResult > 0;
        },
      );
      _undoRedoService.addAction(action);

      return result;
    } catch (e) {
      rethrow;
    }
  }

  // ===============================
  // عمليات مجمعة
  // ===============================

  /// بدء مجموعة عمليات
  static void beginOperationGroup(String groupName) {
    _undoRedoService.beginGroup(groupName);
  }

  /// إنهاء مجموعة العمليات
  static void endOperationGroup() {
    _undoRedoService.endGroup();
  }

  /// إلغاء مجموعة العمليات
  static void cancelOperationGroup() {
    _undoRedoService.cancelGroup();
  }

  // ===============================
  // معلومات الحالة
  // ===============================

  /// هل يمكن التراجع؟
  static bool get canUndo => _undoRedoService.canUndo;

  /// هل يمكن الإعادة؟
  static bool get canRedo => _undoRedoService.canRedo;

  /// تنفيذ التراجع
  static Future<bool> undo() => _undoRedoService.undo();

  /// تنفيذ الإعادة
  static Future<bool> redo() => _undoRedoService.redo();

  /// مسح التاريخ
  static void clearHistory() => _undoRedoService.clear();

  /// الحصول على تاريخ التراجع
  static List<String> getUndoHistory() => _undoRedoService.getUndoHistory();

  /// الحصول على تاريخ الإعادة
  static List<String> getRedoHistory() => _undoRedoService.getRedoHistory();
}
