/// شاشة الفواتير المتكررة
/// تعرض وتدير جميع الفواتير المتكررة والدورية
library;

import 'package:flutter/material.dart';
import '../models/recurring_invoice.dart';
import '../services/recurring_invoice_service.dart';
import '../services/logging_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';
import '../widgets/recurring_invoice_card.dart';

import 'add_recurring_invoice_screen.dart';
import 'recurring_invoice_details_screen.dart';

class RecurringInvoicesScreen extends StatefulWidget {
  const RecurringInvoicesScreen({super.key});

  @override
  State<RecurringInvoicesScreen> createState() =>
      _RecurringInvoicesScreenState();
}

class _RecurringInvoicesScreenState extends State<RecurringInvoicesScreen> {
  final RecurringInvoiceService _recurringInvoiceService =
      RecurringInvoiceService();
  List<RecurringInvoice> _recurringInvoices = [];
  List<RecurringInvoice> _filteredRecurringInvoices = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _searchQuery = '';
  bool? _selectedActiveStatus;

  @override
  void initState() {
    super.initState();
    _loadRecurringInvoices();
    _loadStatistics();
    // بدء جدولة الفواتير المتكررة
    _recurringInvoiceService.startScheduler();
  }

  @override
  void dispose() {
    _recurringInvoiceService.dispose();
    super.dispose();
  }

  Future<void> _loadRecurringInvoices() async {
    try {
      setState(() => _isLoading = true);

      final recurringInvoices = await _recurringInvoiceService
          .getAllRecurringInvoices();

      setState(() {
        _recurringInvoices = recurringInvoices;
        _filteredRecurringInvoices = recurringInvoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل الفواتير المتكررة: $e');

      LoggingService.error(
        'خطأ في تحميل الفواتير المتكررة',
        category: 'RecurringInvoicesScreen',
        data: {'error': e.toString()},
      );
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final statistics = await _recurringInvoiceService
          .getRecurringInvoiceStatistics();
      setState(() => _statistics = statistics);
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل إحصائيات الفواتير المتكررة',
        category: 'RecurringInvoicesScreen',
        data: {'error': e.toString()},
      );
    }
  }

  void _filterRecurringInvoices() {
    setState(() {
      _filteredRecurringInvoices = _recurringInvoices.where((recurringInvoice) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            recurringInvoice.templateName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            recurringInvoice.clientName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );

        final matchesStatus =
            _selectedActiveStatus == null ||
            recurringInvoice.isActive == _selectedActiveStatus;

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() => _searchQuery = query);
    _filterRecurringInvoices();
  }

  void _onStatusFilterChanged(bool? status) {
    setState(() => _selectedActiveStatus = status);
    _filterRecurringInvoices();
  }

  Future<void> _toggleStatus(RecurringInvoice recurringInvoice) async {
    try {
      await _recurringInvoiceService.toggleRecurringInvoiceStatus(
        recurringInvoice.id!,
        !recurringInvoice.isActive,
      );

      _showSuccessSnackBar(
        recurringInvoice.isActive
            ? 'تم إلغاء تفعيل الفاتورة المتكررة'
            : 'تم تفعيل الفاتورة المتكررة',
      );

      _loadRecurringInvoices();
      _loadStatistics();
    } catch (e) {
      _showErrorSnackBar('خطأ في تغيير حالة الفاتورة المتكررة: $e');
    }
  }

  Future<void> _generateInvoiceManually(
    RecurringInvoice recurringInvoice,
  ) async {
    final confirmed = await _showConfirmDialog(
      'إنشاء فاتورة',
      'هل تريد إنشاء فاتورة جديدة من النموذج "${recurringInvoice.templateName}"؟',
    );

    if (!confirmed) return;

    try {
      final invoiceId = await _recurringInvoiceService.generateInvoiceManually(
        recurringInvoice.id!,
      );

      _showSuccessSnackBar('تم إنشاء الفاتورة بنجاح (رقم: $invoiceId)');
      _loadRecurringInvoices();
      _loadStatistics();
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء الفاتورة: $e');
    }
  }

  Future<void> _processAllDueInvoices() async {
    final confirmed = await _showConfirmDialog(
      'معالجة الفواتير المستحقة',
      'هل تريد معالجة جميع الفواتير المتكررة المستحقة الآن؟',
    );

    if (!confirmed) return;

    try {
      setState(() => _isLoading = true);

      final generatedInvoiceIds = await _recurringInvoiceService
          .processRecurringInvoicesNow();

      setState(() => _isLoading = false);

      if (generatedInvoiceIds.isNotEmpty) {
        _showSuccessSnackBar(
          'تم إنشاء ${generatedInvoiceIds.length} فاتورة بنجاح',
        );
      } else {
        _showInfoSnackBar('لا توجد فواتير مستحقة للمعالجة');
      }

      _loadRecurringInvoices();
      _loadStatistics();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في معالجة الفواتير المستحقة: $e');
    }
  }

  Future<void> _deleteRecurringInvoice(
    RecurringInvoice recurringInvoice,
  ) async {
    final confirmed = await _showConfirmDialog(
      'حذف الفاتورة المتكررة',
      'هل تريد حذف الفاتورة المتكررة "${recurringInvoice.templateName}"؟\nلا يمكن التراجع عن هذا الإجراء.',
    );

    if (!confirmed) return;

    try {
      await _recurringInvoiceService.deleteRecurringInvoice(
        recurringInvoice.id!,
      );
      _showSuccessSnackBar('تم حذف الفاتورة المتكررة بنجاح');
      _loadRecurringInvoices();
      _loadStatistics();
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الفاتورة المتكررة: $e');
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.blue),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير المتكررة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_statistics['due_count'] != null && _statistics['due_count'] > 0)
            IconButton(
              icon: Badge(
                label: Text('${_statistics['due_count']}'),
                child: const Icon(Icons.play_arrow),
              ),
              onPressed: _processAllDueInvoices,
              tooltip: 'معالجة الفواتير المستحقة',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadRecurringInvoices();
              _loadStatistics();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatisticsCard(),
          _buildSearchAndFilter(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _filteredRecurringInvoices.isEmpty
                ? _buildEmptyState()
                : _buildRecurringInvoicesList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddRecurringInvoiceScreen(),
            ),
          );
          if (result == true) {
            _loadRecurringInvoices();
            _loadStatistics();
          }
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إحصائيات الفواتير المتكررة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'الإجمالي',
                      '${_statistics['total_count'] ?? 0}',
                      Icons.receipt_long,
                      AppColors.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'المفعلة',
                      '${_statistics['active_count'] ?? 0}',
                      Icons.check_circle,
                      AppColors.success,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'المستحقة',
                      '${_statistics['due_count'] ?? 0}',
                      Icons.schedule,
                      Colors.orange,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'المعطلة',
                      '${_statistics['inactive_count'] ?? 0}',
                      Icons.pause_circle,
                      Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في الفواتير المتكررة...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('الحالة: '),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButton<bool?>(
                  value: _selectedActiveStatus,
                  isExpanded: true,
                  hint: const Text('جميع الحالات'),
                  items: const [
                    DropdownMenuItem<bool?>(
                      value: null,
                      child: Text('جميع الحالات'),
                    ),
                    DropdownMenuItem<bool?>(value: true, child: Text('مفعلة')),
                    DropdownMenuItem<bool?>(value: false, child: Text('معطلة')),
                  ],
                  onChanged: _onStatusFilterChanged,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.repeat, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد فواتير متكررة',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة فاتورة متكررة جديدة',
            style: TextStyle(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildRecurringInvoicesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredRecurringInvoices.length,
      itemBuilder: (context, index) {
        final recurringInvoice = _filteredRecurringInvoices[index];
        return RecurringInvoiceCard(
          recurringInvoice: recurringInvoice,
          onTap: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RecurringInvoiceDetailsScreen(
                  recurringInvoice: recurringInvoice,
                ),
              ),
            );
            if (result == true) {
              _loadRecurringInvoices();
              _loadStatistics();
            }
          },
          onToggleStatus: () => _toggleStatus(recurringInvoice),
          onGenerateInvoice: () => _generateInvoiceManually(recurringInvoice),
          onDelete: () => _deleteRecurringInvoice(recurringInvoice),
        );
      },
    );
  }
}
