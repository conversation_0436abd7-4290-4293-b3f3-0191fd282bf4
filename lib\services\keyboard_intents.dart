import 'package:flutter/material.dart';

/// تعريف جميع Intent classes للاختصارات
/// هذه الفئات تمثل النوايا المختلفة للاختصارات

// ===============================
// اختصارات الملف
// ===============================

class NewIntent extends Intent {
  const NewIntent();
}

class OpenIntent extends Intent {
  const OpenIntent();
}

class SaveIntent extends Intent {
  const SaveIntent();
}

class SaveAsIntent extends Intent {
  const SaveAsIntent();
}

class PrintIntent extends Intent {
  const PrintIntent();
}

class PrintPreviewIntent extends Intent {
  const PrintPreviewIntent();
}

class QuitIntent extends Intent {
  const QuitIntent();
}

// ===============================
// اختصارات التحرير
// ===============================

class UndoIntent extends Intent {
  const UndoIntent();
}

class RedoIntent extends Intent {
  const RedoIntent();
}

class CopyIntent extends Intent {
  const CopyIntent();
}

class PasteIntent extends Intent {
  const PasteIntent();
}

class CutIntent extends Intent {
  const CutIntent();
}

class SelectAllIntent extends Intent {
  const SelectAllIntent();
}

class EditIntent extends Intent {
  const EditIntent();
}

class DeleteIntent extends Intent {
  const DeleteIntent();
}

class InsertIntent extends Intent {
  const InsertIntent();
}

// ===============================
// اختصارات البحث والتنقل
// ===============================

class SearchIntent extends Intent {
  const SearchIntent();
}

class FindNextIntent extends Intent {
  const FindNextIntent();
}

class FindPreviousIntent extends Intent {
  const FindPreviousIntent();
}

class ReplaceIntent extends Intent {
  const ReplaceIntent();
}

class GoToIntent extends Intent {
  const GoToIntent();
}

class FilterIntent extends Intent {
  const FilterIntent();
}

class SortIntent extends Intent {
  const SortIntent();
}

class GroupByIntent extends Intent {
  const GroupByIntent();
}

// ===============================
// اختصارات العرض
// ===============================

class FullScreenIntent extends Intent {
  const FullScreenIntent();
}

class ZoomInIntent extends Intent {
  const ZoomInIntent();
}

class ZoomOutIntent extends Intent {
  const ZoomOutIntent();
}

class ResetZoomIntent extends Intent {
  const ResetZoomIntent();
}

class RefreshIntent extends Intent {
  const RefreshIntent();
}

// ===============================
// اختصارات التنقل
// ===============================

class NavigateBackIntent extends Intent {
  const NavigateBackIntent();
}

class NavigateForwardIntent extends Intent {
  const NavigateForwardIntent();
}

class NextTabIntent extends Intent {
  const NextTabIntent();
}

class PreviousTabIntent extends Intent {
  const PreviousTabIntent();
}

class NextFieldIntent extends Intent {
  const NextFieldIntent();
}

class PreviousFieldIntent extends Intent {
  const PreviousFieldIntent();
}

class FirstRecordIntent extends Intent {
  const FirstRecordIntent();
}

class LastRecordIntent extends Intent {
  const LastRecordIntent();
}

class NextRecordIntent extends Intent {
  const NextRecordIntent();
}

class PreviousRecordIntent extends Intent {
  const PreviousRecordIntent();
}

// ===============================
// اختصارات التقارير
// ===============================

class RunReportIntent extends Intent {
  const RunReportIntent();
}

class ExportIntent extends Intent {
  const ExportIntent();
}

// ===============================
// اختصارات المحاسبة
// ===============================

class BalanceSheetIntent extends Intent {
  const BalanceSheetIntent();
}

class IncomeStatementIntent extends Intent {
  const IncomeStatementIntent();
}

class TrialBalanceIntent extends Intent {
  const TrialBalanceIntent();
}

class LedgerIntent extends Intent {
  const LedgerIntent();
}

class NewJournalEntryIntent extends Intent {
  const NewJournalEntryIntent();
}

class PostJournalIntent extends Intent {
  const PostJournalIntent();
}

class UnpostJournalIntent extends Intent {
  const UnpostJournalIntent();
}

class AddDebitLineIntent extends Intent {
  const AddDebitLineIntent();
}

class AddCreditLineIntent extends Intent {
  const AddCreditLineIntent();
}

class NewInvoiceIntent extends Intent {
  const NewInvoiceIntent();
}

class AddLineItemIntent extends Intent {
  const AddLineItemIntent();
}

class CalculateTotalIntent extends Intent {
  const CalculateTotalIntent();
}

class PrintInvoiceIntent extends Intent {
  const PrintInvoiceIntent();
}

class CalculateIntent extends Intent {
  const CalculateIntent();
}

// ===============================
// اختصارات عامة
// ===============================

class HelpIntent extends Intent {
  const HelpIntent();
}

class CancelIntent extends Intent {
  const CancelIntent();
}

class ConfirmIntent extends Intent {
  const ConfirmIntent();
}

// ===============================
// Intent مخصص مع بيانات
// ===============================

class CustomIntent extends Intent {
  final String action;
  final Map<String, dynamic>? data;
  
  const CustomIntent(this.action, {this.data});
}

class NavigateToScreenIntent extends Intent {
  final String screenName;
  final Map<String, dynamic>? arguments;
  
  const NavigateToScreenIntent(this.screenName, {this.arguments});
}

class ShowDialogIntent extends Intent {
  final String dialogType;
  final Map<String, dynamic>? data;
  
  const ShowDialogIntent(this.dialogType, {this.data});
}

class ExecuteActionIntent extends Intent {
  final String actionName;
  final Map<String, dynamic>? parameters;
  
  const ExecuteActionIntent(this.actionName, {this.parameters});
}
