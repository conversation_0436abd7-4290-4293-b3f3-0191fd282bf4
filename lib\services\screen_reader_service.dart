import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// خدمة قارئ الشاشة
/// تدير الإعلانات الصوتية والتفاعل مع قارئ الشاشة
class ScreenReaderService {
  static final ScreenReaderService _instance = ScreenReaderService._internal();
  factory ScreenReaderService() => _instance;
  ScreenReaderService._internal();

  // ===============================
  // إعدادات قارئ الشاشة
  // ===============================

  /// التحقق من تفعيل قارئ الشاشة
  static bool get isEnabled {
    return WidgetsBinding
        .instance
        .platformDispatcher
        .accessibilityFeatures
        .accessibleNavigation;
  }

  /// التحقق من دعم الإعلانات الصوتية
  static bool get supportsAnnouncements {
    return SemanticsBinding.instance.semanticsEnabled;
  }

  // ===============================
  // الإعلانات العامة
  // ===============================

  /// إعلان رسالة عامة
  static void announce(
    String message, {
    Assertiveness assertiveness = Assertiveness.polite,
  }) {
    if (!isEnabled) return;

    SemanticsService.announce(
      message,
      TextDirection.rtl,
      assertiveness: assertiveness,
    );
  }

  /// إعلان عاجل
  static void announceUrgent(String message) {
    announce(message, assertiveness: Assertiveness.assertive);
  }

  /// إعلان مهذب
  static void announcePolite(String message) {
    announce(message, assertiveness: Assertiveness.polite);
  }

  // ===============================
  // إعلانات العمليات
  // ===============================

  /// إعلان نجاح العملية
  static void announceSuccess(String operation, {String? details}) {
    final message = details != null
        ? 'تم $operation بنجاح. $details'
        : 'تم $operation بنجاح';
    announcePolite(message);
  }

  /// إعلان فشل العملية
  static void announceError(String operation, String error) {
    announceUrgent('فشل في $operation. $error');
  }

  /// إعلان تحذير
  static void announceWarning(String warning) {
    announceUrgent('تحذير: $warning');
  }

  /// إعلان معلومات
  static void announceInfo(String info) {
    announcePolite('معلومات: $info');
  }

  // ===============================
  // إعلانات التنقل
  // ===============================

  /// إعلان تغيير الشاشة
  static void announceScreenChange(String screenName) {
    announcePolite('تم الانتقال إلى شاشة $screenName');
  }

  /// إعلان فتح حوار
  static void announceDialogOpen(String dialogType) {
    announcePolite('تم فتح $dialogType');
  }

  /// إعلان إغلاق حوار
  static void announceDialogClose(String dialogType) {
    announcePolite('تم إغلاق $dialogType');
  }

  /// إعلان تغيير التبويب
  static void announceTabChange(String tabName) {
    announcePolite('تم الانتقال إلى تبويب $tabName');
  }

  // ===============================
  // إعلانات البيانات
  // ===============================

  /// إعلان بدء تحميل البيانات
  static void announceLoadingStart(String dataType) {
    announcePolite('جاري تحميل $dataType');
  }

  /// إعلان اكتمال تحميل البيانات
  static void announceLoadingComplete(String dataType, int count) {
    final message = count == 0
        ? 'لا توجد $dataType'
        : count == 1
        ? 'تم تحميل $dataType واحد'
        : 'تم تحميل $count من $dataType';
    announcePolite(message);
  }

  /// إعلان تحديث البيانات
  static void announceDataUpdate(String dataType, String action) {
    announcePolite('تم $action $dataType');
  }

  /// إعلان حذف البيانات
  static void announceDataDelete(String dataType, String itemName) {
    announcePolite('تم حذف $dataType: $itemName');
  }

  // ===============================
  // إعلانات النماذج
  // ===============================

  /// إعلان خطأ في النموذج
  static void announceFormError(String fieldName, String error) {
    announceUrgent('خطأ في حقل $fieldName: $error');
  }

  /// إعلان نجاح حفظ النموذج
  static void announceFormSaved(String formType) {
    announceSuccess('حفظ $formType');
  }

  /// إعلان إلغاء النموذج
  static void announceFormCancelled(String formType) {
    announcePolite('تم إلغاء $formType');
  }

  /// إعلان تغيير قيمة الحقل
  static void announceFieldChange(String fieldName, String newValue) {
    announcePolite('تم تغيير $fieldName إلى $newValue');
  }

  // ===============================
  // إعلانات التفاعل
  // ===============================

  /// إعلان تحديد عنصر
  static void announceSelection(String itemType, String itemName) {
    announcePolite('تم تحديد $itemType: $itemName');
  }

  /// إعلان إلغاء تحديد عنصر
  static void announceDeselection(String itemType, String itemName) {
    announcePolite('تم إلغاء تحديد $itemType: $itemName');
  }

  /// إعلان تفعيل خيار
  static void announceOptionEnabled(String optionName) {
    announcePolite('تم تفعيل $optionName');
  }

  /// إعلان إلغاء تفعيل خيار
  static void announceOptionDisabled(String optionName) {
    announcePolite('تم إلغاء تفعيل $optionName');
  }

  // ===============================
  // إعلانات البحث والفلترة
  // ===============================

  /// إعلان نتائج البحث
  static void announceSearchResults(int count, String searchTerm) {
    final message = count == 0
        ? 'لا توجد نتائج للبحث عن "$searchTerm"'
        : count == 1
        ? 'تم العثور على نتيجة واحدة للبحث عن "$searchTerm"'
        : 'تم العثور على $count نتيجة للبحث عن "$searchTerm"';
    announcePolite(message);
  }

  /// إعلان تطبيق فلتر
  static void announceFilterApplied(String filterType, int resultCount) {
    final message = resultCount == 0
        ? 'لا توجد نتائج بعد تطبيق فلتر $filterType'
        : 'تم تطبيق فلتر $filterType. $resultCount نتيجة متاحة';
    announcePolite(message);
  }

  /// إعلان إزالة فلتر
  static void announceFilterRemoved(String filterType, int resultCount) {
    announcePolite('تم إزالة فلتر $filterType. $resultCount نتيجة متاحة');
  }

  // ===============================
  // إعلانات الحالة
  // ===============================

  /// إعلان تغيير حالة التطبيق
  static void announceAppStateChange(String newState) {
    announcePolite('حالة التطبيق: $newState');
  }

  /// إعلان حالة الاتصال
  static void announceConnectionState(bool isConnected) {
    final message = isConnected ? 'متصل' : 'غير متصل';
    announcePolite('حالة الاتصال: $message');
  }

  /// إعلان حالة الحفظ
  static void announceSaveState(bool isSaving) {
    final message = isSaving ? 'جاري الحفظ...' : 'تم الحفظ';
    announcePolite(message);
  }

  // ===============================
  // دوال مساعدة للمحتوى
  // ===============================

  /// بناء وصف شامل للعنصر
  static String buildElementDescription({
    required String type,
    required String name,
    String? value,
    String? state,
    String? hint,
  }) {
    final parts = <String>['$type: $name'];

    if (value != null && value.isNotEmpty) {
      parts.add('القيمة: $value');
    }

    if (state != null && state.isNotEmpty) {
      parts.add('الحالة: $state');
    }

    if (hint != null && hint.isNotEmpty) {
      parts.add(hint);
    }

    return parts.join('. ');
  }

  /// بناء وصف للقائمة
  static String buildListDescription(
    String listType,
    int itemCount,
    int selectedIndex,
  ) {
    final countText = itemCount == 0
        ? 'فارغة'
        : itemCount == 1
        ? 'تحتوي على عنصر واحد'
        : 'تحتوي على $itemCount عنصر';

    final selectionText = selectedIndex >= 0
        ? '. العنصر المحدد: ${selectedIndex + 1}'
        : '';

    return '$listType $countText$selectionText';
  }

  /// بناء وصف للنموذج
  static String buildFormDescription(
    String formType,
    int fieldCount,
    int errorCount,
  ) {
    final fieldsText = fieldCount == 1
        ? 'يحتوي على حقل واحد'
        : 'يحتوي على $fieldCount حقل';

    final errorsText = errorCount > 0
        ? '. يوجد $errorCount خطأ'
        : '. جميع الحقول صحيحة';

    return '$formType $fieldsText$errorsText';
  }

  // ===============================
  // إعدادات متقدمة
  // ===============================

  /// تخصيص سرعة القراءة
  static void setSpeechRate(double rate) {
    // هذه الدالة للمستقبل - يمكن تطويرها مع مكتبات متخصصة
  }

  /// تخصيص نبرة الصوت
  static void setSpeechPitch(double pitch) {
    // هذه الدالة للمستقبل - يمكن تطويرها مع مكتبات متخصصة
  }

  /// إيقاف جميع الإعلانات مؤقتاً
  static void pauseAnnouncements() {
    // هذه الدالة للمستقبل - يمكن تطويرها مع مكتبات متخصصة
  }

  /// استئناف الإعلانات
  static void resumeAnnouncements() {
    // هذه الدالة للمستقبل - يمكن تطويرها مع مكتبات متخصصة
  }
}
