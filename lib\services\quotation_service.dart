/// خدمة عروض الأسعار
/// توفر جميع العمليات المتعلقة بإدارة عروض الأسعار
library;

import '../database/database_helper.dart';
import '../models/quotation.dart';
import '../models/invoice.dart';
import '../constants/app_constants.dart';
import '../services/invoice_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

class QuotationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InvoiceService _invoiceService = InvoiceService();

  /// الحصول على جميع عروض الأسعار
  Future<List<Quotation>> getAllQuotations() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT q.*, 
               c.name as customer_name,
               s.name as supplier_name
        FROM ${AppConstants.quotationsTable} q
        LEFT JOIN ${AppConstants.customersTable} c ON q.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON q.supplier_id = s.id
        ORDER BY q.quotation_date DESC, q.id DESC
      ''');

      List<Quotation> quotations = [];
      for (final map in maps) {
        final quotation = Quotation.fromMap(map);
        final items = await getQuotationItems(quotation.id!);
        quotations.add(quotation.copyWith(items: items));
      }

      return quotations;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب عروض الأسعار',
        category: 'QuotationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على عرض سعر بالمعرف
  Future<Quotation?> getQuotationById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
        '''
        SELECT q.*, 
               c.name as customer_name,
               s.name as supplier_name
        FROM ${AppConstants.quotationsTable} q
        LEFT JOIN ${AppConstants.customersTable} c ON q.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON q.supplier_id = s.id
        WHERE q.id = ?
      ''',
        [id],
      );

      if (maps.isNotEmpty) {
        final quotation = Quotation.fromMap(maps.first);
        final items = await getQuotationItems(id);
        return quotation.copyWith(items: items);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب عرض السعر',
        category: 'QuotationService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على عرض سعر بالرقم
  Future<Quotation?> getQuotationByNumber(String quotationNumber) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.quotationsTable,
        where: 'quotation_number = ?',
        whereArgs: [quotationNumber],
      );

      if (maps.isNotEmpty) {
        final quotation = Quotation.fromMap(maps.first);
        final items = await getQuotationItems(quotation.id!);
        return quotation.copyWith(items: items);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب عرض السعر بالرقم',
        category: 'QuotationService',
        data: {'quotationNumber': quotationNumber, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على أصناف عرض السعر
  Future<List<QuotationItem>> getQuotationItems(int quotationId) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
        '''
        SELECT qi.*, i.name as item_name, i.code as item_code
        FROM ${AppConstants.quotationItemsTable} qi
        LEFT JOIN ${AppConstants.itemsTable} i ON qi.item_id = i.id
        WHERE qi.quotation_id = ?
        ORDER BY qi.id ASC
      ''',
        [quotationId],
      );

      return List.generate(maps.length, (i) {
        return QuotationItem.fromMap(maps[i]);
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب أصناف عرض السعر',
        category: 'QuotationService',
        data: {'quotationId': quotationId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// إنشاء عرض سعر جديد
  Future<int> insertQuotation(Quotation quotation) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار رقم عرض السعر
      final existingQuotation = await getQuotationByNumber(
        quotation.quotationNumber,
      );
      if (existingQuotation != null) {
        throw Exception('رقم عرض السعر موجود مسبقاً');
      }

      return await db.transaction((txn) async {
        // إدراج عرض السعر الرئيسي
        final quotationData = quotation.toMap();
        quotationData.remove('id');
        final quotationId = await txn.insert(
          AppConstants.quotationsTable,
          quotationData,
        );

        // إدراج أصناف عرض السعر
        for (final item in quotation.items) {
          final itemData = item.copyWith(quotationId: quotationId).toMap();
          itemData.remove('id');
          await txn.insert(AppConstants.quotationItemsTable, itemData);
        }

        // تسجيل العملية
        await AuditService.log(
          action: 'INSERT',
          entityType: 'quotations',
          entityId: quotationId,
          entityName: quotation.quotationNumber,
          description: 'إنشاء عرض سعر جديد: ${quotation.quotationNumber}',
        );

        return quotationId;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء عرض السعر',
        category: 'QuotationService',
        data: {'quotation': quotation.quotationNumber, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث عرض سعر
  Future<void> updateQuotation(Quotation quotation) async {
    try {
      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // تحديث عرض السعر الرئيسي
        final quotationData = quotation.toMap();
        quotationData['updated_at'] = DateTime.now().toIso8601String();
        await txn.update(
          AppConstants.quotationsTable,
          quotationData,
          where: 'id = ?',
          whereArgs: [quotation.id],
        );

        // حذف الأصناف القديمة
        await txn.delete(
          AppConstants.quotationItemsTable,
          where: 'quotation_id = ?',
          whereArgs: [quotation.id],
        );

        // إدراج الأصناف الجديدة
        for (final item in quotation.items) {
          final itemData = item.copyWith(quotationId: quotation.id!).toMap();
          itemData.remove('id');
          await txn.insert(AppConstants.quotationItemsTable, itemData);
        }

        // تسجيل العملية
        await AuditService.log(
          action: 'UPDATE',
          entityType: 'quotations',
          entityId: quotation.id!,
          entityName: quotation.quotationNumber,
          description: 'تحديث عرض السعر: ${quotation.quotationNumber}',
        );
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث عرض السعر',
        category: 'QuotationService',
        data: {'quotation': quotation.quotationNumber, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف عرض سعر
  Future<void> deleteQuotation(int id) async {
    try {
      final quotation = await getQuotationById(id);
      if (quotation == null) {
        throw Exception('عرض السعر غير موجود');
      }

      // التحقق من إمكانية الحذف
      if (quotation.isConverted) {
        throw Exception('لا يمكن حذف عرض سعر محول إلى فاتورة');
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // حذف الأصناف
        await txn.delete(
          AppConstants.quotationItemsTable,
          where: 'quotation_id = ?',
          whereArgs: [id],
        );

        // حذف عرض السعر
        await txn.delete(
          AppConstants.quotationsTable,
          where: 'id = ?',
          whereArgs: [id],
        );

        // تسجيل العملية
        await AuditService.log(
          action: 'DELETE',
          entityType: 'quotations',
          entityId: id,
          entityName: quotation.quotationNumber,
          description: 'حذف عرض السعر: ${quotation.quotationNumber}',
        );
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف عرض السعر',
        category: 'QuotationService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث حالة عرض السعر
  Future<void> updateQuotationStatus(int id, QuotationStatus status) async {
    try {
      final db = await _databaseHelper.database;

      await db.update(
        AppConstants.quotationsTable,
        {'status': status.code, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'quotations',
        entityId: id,
        description: 'تحديث حالة عرض السعر إلى: ${status.displayName}',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة عرض السعر',
        category: 'QuotationService',
        data: {'id': id, 'status': status.code, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحويل عرض السعر إلى فاتورة
  Future<int> convertQuotationToInvoice(int quotationId) async {
    try {
      final quotation = await getQuotationById(quotationId);
      if (quotation == null) {
        throw Exception('عرض السعر غير موجود');
      }

      // التحقق من إمكانية التحويل
      if (!quotation.canBeConverted) {
        throw Exception(
          'لا يمكن تحويل عرض السعر. يجب أن يكون مقبولاً وغير محول مسبقاً',
        );
      }

      // إنشاء رقم فاتورة جديد
      final invoiceType = quotation.customerId != null ? 'sale' : 'purchase';
      final invoiceNumber = await _invoiceService.generateInvoiceNumber(
        invoiceType,
      );

      // تحويل أصناف عرض السعر إلى أصناف فاتورة
      final invoiceItems = quotation.items.map((quotationItem) {
        return InvoiceItem(
          invoiceId: 0, // سيتم تحديثه لاحقاً
          itemId: quotationItem.itemId,
          quantity: quotationItem.quantity,
          unitPrice: quotationItem.unitPrice,
          totalPrice: quotationItem.totalPrice,
          discountPercentage: quotationItem.discountPercentage,
          discountAmount: quotationItem.discountAmount,
          taxPercentage: quotationItem.taxPercentage,
          taxAmount: quotationItem.taxAmount,
          netAmount: quotationItem.netAmount,
        );
      }).toList();

      // إنشاء الفاتورة
      final invoice = Invoice(
        invoiceNumber: invoiceNumber,
        invoiceDate: DateTime.now(),
        type: quotation.customerId != null ? 'sale' : 'purchase',
        customerId: quotation.customerId,
        supplierId: quotation.supplierId,
        subtotal: quotation.subtotal,
        taxAmount: quotation.taxAmount,
        discountAmount: quotation.discountAmount,
        totalAmount: quotation.totalAmount,
        currencyId: quotation.currencyId,
        notes: 'محول من عرض السعر: ${quotation.quotationNumber}',
        terms: quotation.terms,
        reference: quotation.reference,
        items: invoiceItems,
      );

      // إدراج الفاتورة
      final invoiceId = await _invoiceService.insertInvoice(invoice);

      // تحديث عرض السعر لتسجيل التحويل
      final db = await _databaseHelper.database;
      await db.update(
        AppConstants.quotationsTable,
        {
          'status': QuotationStatus.converted.code,
          'converted_to_invoice': invoiceId,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [quotationId],
      );

      // تسجيل العملية
      await AuditService.log(
        action: 'CONVERT',
        entityType: 'quotations',
        entityId: quotationId,
        entityName: quotation.quotationNumber,
        description:
            'تحويل عرض السعر ${quotation.quotationNumber} إلى فاتورة $invoiceNumber',
        newValues: {
          'invoice_id': invoiceId,
          'invoice_number': invoiceNumber,
          'conversion_date': DateTime.now().toIso8601String(),
        },
      );

      LoggingService.info(
        'تم تحويل عرض السعر إلى فاتورة بنجاح',
        category: 'QuotationService',
        data: {
          'quotation_id': quotationId,
          'quotation_number': quotation.quotationNumber,
          'invoice_id': invoiceId,
          'invoice_number': invoiceNumber,
        },
      );

      return invoiceId;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحويل عرض السعر إلى فاتورة',
        category: 'QuotationService',
        data: {'quotation_id': quotationId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على عروض الأسعار المنتهية الصلاحية
  Future<List<Quotation>> getExpiredQuotations() async {
    try {
      final db = await _databaseHelper.database;
      final today = DateTime.now().toIso8601String().split('T')[0];

      final List<Map<String, dynamic>> maps = await db.rawQuery(
        '''
        SELECT q.*,
               c.name as customer_name,
               s.name as supplier_name
        FROM ${AppConstants.quotationsTable} q
        LEFT JOIN ${AppConstants.customersTable} c ON q.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON q.supplier_id = s.id
        WHERE q.valid_until < ? AND q.status != 'converted' AND q.status != 'expired'
        ORDER BY q.valid_until ASC
      ''',
        [today],
      );

      List<Quotation> quotations = [];
      for (final map in maps) {
        final quotation = Quotation.fromMap(map);
        final items = await getQuotationItems(quotation.id!);
        quotations.add(quotation.copyWith(items: items));
      }

      return quotations;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب عروض الأسعار المنتهية الصلاحية',
        category: 'QuotationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث حالة عروض الأسعار المنتهية الصلاحية
  Future<void> updateExpiredQuotations() async {
    try {
      final expiredQuotations = await getExpiredQuotations();

      for (final quotation in expiredQuotations) {
        await updateQuotationStatus(quotation.id!, QuotationStatus.expired);
      }

      if (expiredQuotations.isNotEmpty) {
        LoggingService.info(
          'تم تحديث حالة عروض الأسعار المنتهية الصلاحية',
          category: 'QuotationService',
          data: {'count': expiredQuotations.length},
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث عروض الأسعار المنتهية الصلاحية',
        category: 'QuotationService',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء رقم عرض سعر جديد
  Future<String> generateQuotationNumber() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery('''
        SELECT MAX(CAST(SUBSTR(quotation_number, 3) AS INTEGER)) as max_number
        FROM ${AppConstants.quotationsTable}
        WHERE quotation_number LIKE 'Q-%' AND quotation_number GLOB 'Q-[0-9]*'
      ''');

      final maxNumber = result.first['max_number'] as int? ?? 0;
      return 'Q-${(maxNumber + 1).toString().padLeft(6, '0')}';
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء رقم عرض السعر',
        category: 'QuotationService',
        data: {'error': e.toString()},
      );
      // في حالة الخطأ، استخدم التاريخ والوقت
      final now = DateTime.now();
      return 'Q-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch.toString().substring(8)}';
    }
  }
}
