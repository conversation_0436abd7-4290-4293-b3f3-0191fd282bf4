import 'package:flutter/material.dart';
import '../services/pagination_service.dart';

/// Widget للتحكم في التقسيم (Pagination)
/// يوفر أزرار التنقل بين الصفحات ومعلومات الصفحة الحالية
class PaginationWidget extends StatelessWidget {
  final PaginationResult paginationResult;
  final Function(int page) onPageChanged;
  final bool showPageNumbers;
  final bool showPageInfo;
  final bool showPageSizeSelector;
  final List<int> pageSizeOptions;
  final Function(int pageSize)? onPageSizeChanged;
  final EdgeInsetsGeometry? padding;

  const PaginationWidget({
    super.key,
    required this.paginationResult,
    required this.onPageChanged,
    this.showPageNumbers = true,
    this.showPageInfo = true,
    this.showPageSizeSelector = false,
    this.pageSizeOptions = const [10, 20, 50, 100],
    this.onPageSizeChanged,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (paginationResult.totalItems == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: Column(
        children: [
          if (showPageInfo) _buildPageInfo(context),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (showPageSizeSelector) _buildPageSizeSelector(context),
              if (!showPageSizeSelector) const Spacer(),
              _buildNavigationControls(context),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الصفحة
  Widget _buildPageInfo(BuildContext context) {
    return Text(
      paginationResult.pageInfo,
      style: Theme.of(
        context,
      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
      textAlign: TextAlign.center,
    );
  }

  /// بناء محدد حجم الصفحة
  Widget _buildPageSizeSelector(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('عرض:', style: Theme.of(context).textTheme.bodyMedium),
        const SizedBox(width: 8),
        DropdownButton<int>(
          value: paginationResult.pageSize,
          items: pageSizeOptions.map((size) {
            return DropdownMenuItem<int>(value: size, child: Text('$size'));
          }).toList(),
          onChanged: onPageSizeChanged != null
              ? (int? value) {
                  if (value != null) {
                    onPageSizeChanged!(value);
                  }
                }
              : null,
          underline: Container(),
        ),
        const SizedBox(width: 8),
        Text('عنصر', style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  /// بناء أزرار التنقل
  Widget _buildNavigationControls(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر الصفحة الأولى
        IconButton(
          onPressed: paginationResult.hasPreviousPage
              ? () => onPageChanged(1)
              : null,
          icon: const Icon(Icons.first_page),
          tooltip: 'الصفحة الأولى',
        ),

        // زر الصفحة السابقة
        IconButton(
          onPressed: paginationResult.hasPreviousPage
              ? () => onPageChanged(paginationResult.currentPage - 1)
              : null,
          icon: const Icon(Icons.chevron_right),
          tooltip: 'الصفحة السابقة',
        ),

        // أرقام الصفحات
        if (showPageNumbers) ..._buildPageNumbers(context),

        // زر الصفحة التالية
        IconButton(
          onPressed: paginationResult.hasNextPage
              ? () => onPageChanged(paginationResult.currentPage + 1)
              : null,
          icon: const Icon(Icons.chevron_left),
          tooltip: 'الصفحة التالية',
        ),

        // زر الصفحة الأخيرة
        IconButton(
          onPressed: paginationResult.hasNextPage
              ? () => onPageChanged(paginationResult.totalPages)
              : null,
          icon: const Icon(Icons.last_page),
          tooltip: 'الصفحة الأخيرة',
        ),
      ],
    );
  }

  /// بناء أرقام الصفحات
  List<Widget> _buildPageNumbers(BuildContext context) {
    final pageNumbers = PaginationService.generatePageNumbers(
      paginationResult.currentPage,
      paginationResult.totalPages,
      maxVisible: 5,
    );

    return pageNumbers.map((pageNumber) {
      final isCurrentPage = pageNumber == paginationResult.currentPage;

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: Material(
          color: isCurrentPage
              ? Theme.of(context).primaryColor
              : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          child: InkWell(
            onTap: isCurrentPage ? null : () => onPageChanged(pageNumber),
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                '$pageNumber',
                style: TextStyle(
                  color: isCurrentPage
                      ? Colors.white
                      : Theme.of(context).textTheme.bodyMedium?.color,
                  fontWeight: isCurrentPage
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
        ),
      );
    }).toList();
  }
}

/// Widget مبسط للتقسيم
class SimplePaginationWidget extends StatelessWidget {
  final PaginationResult paginationResult;
  final Function(int page) onPageChanged;

  const SimplePaginationWidget({
    super.key,
    required this.paginationResult,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (paginationResult.totalItems == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // معلومات الصفحة
          Text(
            paginationResult.pageInfo,
            style: Theme.of(context).textTheme.bodySmall,
          ),

          // أزرار التنقل
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: paginationResult.hasPreviousPage
                    ? () => onPageChanged(paginationResult.currentPage - 1)
                    : null,
                icon: const Icon(Icons.chevron_right),
                iconSize: 20,
              ),

              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${paginationResult.currentPage} / ${paginationResult.totalPages}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),

              IconButton(
                onPressed: paginationResult.hasNextPage
                    ? () => onPageChanged(paginationResult.currentPage + 1)
                    : null,
                icon: const Icon(Icons.chevron_left),
                iconSize: 20,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Widget للبحث مع التقسيم
class SearchPaginationWidget extends StatefulWidget {
  final String? initialQuery;
  final Function(String query) onSearchChanged;
  final PaginationResult? paginationResult;
  final Function(int page)? onPageChanged;
  final String hintText;
  final bool showClearButton;

  const SearchPaginationWidget({
    super.key,
    this.initialQuery,
    required this.onSearchChanged,
    this.paginationResult,
    this.onPageChanged,
    this.hintText = 'البحث...',
    this.showClearButton = true,
  });

  @override
  State<SearchPaginationWidget> createState() => _SearchPaginationWidgetState();
}

class _SearchPaginationWidgetState extends State<SearchPaginationWidget> {
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  widget.showClearButton && _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        widget.onSearchChanged('');
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: widget.onSearchChanged,
            textInputAction: TextInputAction.search,
          ),
        ),

        // معلومات التقسيم
        if (widget.paginationResult != null && widget.onPageChanged != null)
          SimplePaginationWidget(
            paginationResult: widget.paginationResult!,
            onPageChanged: widget.onPageChanged!,
          ),
      ],
    );
  }
}

/// Widget لعرض حالة التحميل مع التقسيم
class PaginationLoadingWidget extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? loadingMessage;

  const PaginationLoadingWidget({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      if (loadingMessage != null) ...[
                        const SizedBox(height: 8),
                        Text(loadingMessage!),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
