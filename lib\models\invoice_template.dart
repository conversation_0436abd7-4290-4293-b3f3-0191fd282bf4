/// نماذج قوالب طباعة الفواتير الاحترافية
/// يحتوي على جميع أنواع القوالب وإعدادات التخصيص
library;

import 'package:pdf/pdf.dart';
import 'advanced_print_settings.dart';

/// أنواع قوالب الطباعة المختلفة
enum InvoiceTemplateType {
  /// قالب كلاسيكي - تصميم تقليدي أنيق
  classic('classic', 'كلاسيكي', 'تصميم تقليدي أنيق ومناسب للأعمال الرسمية'),

  /// قالب حديث - تصميم عصري وجذاب
  modern('modern', 'حديث', 'تصميم عصري بألوان جذابة ومناسب للشركات الحديثة'),

  /// قالب مبسط - تصميم بسيط وواضح
  minimal('minimal', 'مبسط', 'تصميم بسيط وواضح يركز على المعلومات الأساسية'),

  /// قالب فاخر - تصميم راقي ومميز
  luxury('luxury', 'فاخر', 'تصميم راقي ومميز للشركات الفاخرة'),

  /// قالب مخصص - يمكن تخصيصه بالكامل
  custom('custom', 'مخصص', 'قالب قابل للتخصيص بالكامل حسب احتياجات المستخدم');

  const InvoiceTemplateType(this.code, this.displayName, this.description);

  final String code;
  final String displayName;
  final String description;

  static InvoiceTemplateType fromCode(String code) {
    return values.firstWhere(
      (type) => type.code == code,
      orElse: () => InvoiceTemplateType.classic,
    );
  }
}

/// إعدادات الألوان للقالب
class TemplateColors {
  final PdfColor primary;
  final PdfColor secondary;
  final PdfColor accent;
  final PdfColor text;
  final PdfColor background;
  final PdfColor border;
  final PdfColor headerBackground;
  final PdfColor tableHeader;

  const TemplateColors({
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.text,
    required this.background,
    required this.border,
    required this.headerBackground,
    required this.tableHeader,
  });

  /// ألوان القالب الكلاسيكي
  static const classic = TemplateColors(
    primary: PdfColors.blue800,
    secondary: PdfColors.blue600,
    accent: PdfColors.blue400,
    text: PdfColors.black,
    background: PdfColors.white,
    border: PdfColors.grey300,
    headerBackground: PdfColors.blue50,
    tableHeader: PdfColors.blue100,
  );

  /// ألوان القالب الحديث
  static const modern = TemplateColors(
    primary: PdfColors.indigo800,
    secondary: PdfColors.purple600,
    accent: PdfColors.pink400,
    text: PdfColors.grey800,
    background: PdfColors.white,
    border: PdfColors.grey200,
    headerBackground: PdfColors.indigo50,
    tableHeader: PdfColors.indigo100,
  );

  /// ألوان القالب المبسط
  static const minimal = TemplateColors(
    primary: PdfColors.grey800,
    secondary: PdfColors.grey600,
    accent: PdfColors.grey400,
    text: PdfColors.black,
    background: PdfColors.white,
    border: PdfColors.grey300,
    headerBackground: PdfColors.grey50,
    tableHeader: PdfColors.grey100,
  );

  /// ألوان القالب الفاخر
  static const luxury = TemplateColors(
    primary: PdfColors.amber800,
    secondary: PdfColors.amber600,
    accent: PdfColors.amber400,
    text: PdfColors.brown800,
    background: PdfColors.white,
    border: PdfColors.amber200,
    headerBackground: PdfColors.amber50,
    tableHeader: PdfColors.amber100,
  );

  Map<String, dynamic> toMap() {
    return {
      'primary': _colorToHex(primary),
      'secondary': _colorToHex(secondary),
      'accent': _colorToHex(accent),
      'text': _colorToHex(text),
      'background': _colorToHex(background),
      'border': _colorToHex(border),
      'headerBackground': _colorToHex(headerBackground),
      'tableHeader': _colorToHex(tableHeader),
    };
  }

  factory TemplateColors.fromMap(Map<String, dynamic> map) {
    return TemplateColors(
      primary: _hexToColor(map['primary'] ?? '#1565C0'),
      secondary: _hexToColor(map['secondary'] ?? '#1976D2'),
      accent: _hexToColor(map['accent'] ?? '#42A5F5'),
      text: _hexToColor(map['text'] ?? '#000000'),
      background: _hexToColor(map['background'] ?? '#FFFFFF'),
      border: _hexToColor(map['border'] ?? '#E0E0E0'),
      headerBackground: _hexToColor(map['headerBackground'] ?? '#E3F2FD'),
      tableHeader: _hexToColor(map['tableHeader'] ?? '#BBDEFB'),
    );
  }

  static String _colorToHex(PdfColor color) {
    final r = (color.red * 255).round();
    final g = (color.green * 255).round();
    final b = (color.blue * 255).round();
    return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
  }

  static PdfColor _hexToColor(String hex) {
    final hexCode = hex.replaceAll('#', '');
    final r = int.parse(hexCode.substring(0, 2), radix: 16) / 255;
    final g = int.parse(hexCode.substring(2, 4), radix: 16) / 255;
    final b = int.parse(hexCode.substring(4, 6), radix: 16) / 255;
    return PdfColor(r, g, b);
  }
}

/// إعدادات الخطوط للقالب
class TemplateFonts {
  final double headerSize;
  final double titleSize;
  final double subtitleSize;
  final double bodySize;
  final double captionSize;
  final bool boldHeaders;
  final bool italicSubtitles;

  const TemplateFonts({
    required this.headerSize,
    required this.titleSize,
    required this.subtitleSize,
    required this.bodySize,
    required this.captionSize,
    this.boldHeaders = true,
    this.italicSubtitles = false,
  });

  /// خطوط القالب الكلاسيكي
  static const classic = TemplateFonts(
    headerSize: 24,
    titleSize: 18,
    subtitleSize: 14,
    bodySize: 12,
    captionSize: 10,
    boldHeaders: true,
    italicSubtitles: false,
  );

  /// خطوط القالب الحديث
  static const modern = TemplateFonts(
    headerSize: 28,
    titleSize: 20,
    subtitleSize: 16,
    bodySize: 12,
    captionSize: 10,
    boldHeaders: true,
    italicSubtitles: true,
  );

  /// خطوط القالب المبسط
  static const minimal = TemplateFonts(
    headerSize: 22,
    titleSize: 16,
    subtitleSize: 14,
    bodySize: 11,
    captionSize: 9,
    boldHeaders: true,
    italicSubtitles: false,
  );

  /// خطوط القالب الفاخر
  static const luxury = TemplateFonts(
    headerSize: 30,
    titleSize: 22,
    subtitleSize: 18,
    bodySize: 14,
    captionSize: 12,
    boldHeaders: true,
    italicSubtitles: true,
  );

  Map<String, dynamic> toMap() {
    return {
      'headerSize': headerSize,
      'titleSize': titleSize,
      'subtitleSize': subtitleSize,
      'bodySize': bodySize,
      'captionSize': captionSize,
      'boldHeaders': boldHeaders,
      'italicSubtitles': italicSubtitles,
    };
  }

  factory TemplateFonts.fromMap(Map<String, dynamic> map) {
    return TemplateFonts(
      headerSize: map['headerSize']?.toDouble() ?? 24.0,
      titleSize: map['titleSize']?.toDouble() ?? 18.0,
      subtitleSize: map['subtitleSize']?.toDouble() ?? 14.0,
      bodySize: map['bodySize']?.toDouble() ?? 12.0,
      captionSize: map['captionSize']?.toDouble() ?? 10.0,
      boldHeaders: map['boldHeaders'] ?? true,
      italicSubtitles: map['italicSubtitles'] ?? false,
    );
  }
}

/// إعدادات التخطيط للقالب
class TemplateLayout {
  final double marginTop;
  final double marginBottom;
  final double marginLeft;
  final double marginRight;
  final double headerHeight;
  final double footerHeight;
  final double sectionSpacing;
  final double tableRowHeight;
  final bool showBorders;
  final bool showBackground;
  final bool showWatermark;

  const TemplateLayout({
    required this.marginTop,
    required this.marginBottom,
    required this.marginLeft,
    required this.marginRight,
    required this.headerHeight,
    required this.footerHeight,
    required this.sectionSpacing,
    required this.tableRowHeight,
    this.showBorders = true,
    this.showBackground = true,
    this.showWatermark = false,
  });

  /// تخطيط القالب الكلاسيكي
  static const classic = TemplateLayout(
    marginTop: 40,
    marginBottom: 40,
    marginLeft: 40,
    marginRight: 40,
    headerHeight: 80,
    footerHeight: 40,
    sectionSpacing: 20,
    tableRowHeight: 25,
    showBorders: true,
    showBackground: true,
    showWatermark: false,
  );

  /// تخطيط القالب الحديث
  static const modern = TemplateLayout(
    marginTop: 32,
    marginBottom: 32,
    marginLeft: 32,
    marginRight: 32,
    headerHeight: 100,
    footerHeight: 50,
    sectionSpacing: 25,
    tableRowHeight: 30,
    showBorders: true,
    showBackground: true,
    showWatermark: true,
  );

  /// تخطيط القالب المبسط
  static const minimal = TemplateLayout(
    marginTop: 50,
    marginBottom: 50,
    marginLeft: 50,
    marginRight: 50,
    headerHeight: 60,
    footerHeight: 30,
    sectionSpacing: 15,
    tableRowHeight: 20,
    showBorders: false,
    showBackground: false,
    showWatermark: false,
  );

  /// تخطيط القالب الفاخر
  static const luxury = TemplateLayout(
    marginTop: 30,
    marginBottom: 30,
    marginLeft: 30,
    marginRight: 30,
    headerHeight: 120,
    footerHeight: 60,
    sectionSpacing: 30,
    tableRowHeight: 35,
    showBorders: true,
    showBackground: true,
    showWatermark: true,
  );

  Map<String, dynamic> toMap() {
    return {
      'marginTop': marginTop,
      'marginBottom': marginBottom,
      'marginLeft': marginLeft,
      'marginRight': marginRight,
      'headerHeight': headerHeight,
      'footerHeight': footerHeight,
      'sectionSpacing': sectionSpacing,
      'tableRowHeight': tableRowHeight,
      'showBorders': showBorders,
      'showBackground': showBackground,
      'showWatermark': showWatermark,
    };
  }

  factory TemplateLayout.fromMap(Map<String, dynamic> map) {
    return TemplateLayout(
      marginTop: map['marginTop']?.toDouble() ?? 40.0,
      marginBottom: map['marginBottom']?.toDouble() ?? 40.0,
      marginLeft: map['marginLeft']?.toDouble() ?? 40.0,
      marginRight: map['marginRight']?.toDouble() ?? 40.0,
      headerHeight: map['headerHeight']?.toDouble() ?? 80.0,
      footerHeight: map['footerHeight']?.toDouble() ?? 40.0,
      sectionSpacing: map['sectionSpacing']?.toDouble() ?? 20.0,
      tableRowHeight: map['tableRowHeight']?.toDouble() ?? 25.0,
      showBorders: map['showBorders'] ?? true,
      showBackground: map['showBackground'] ?? true,
      showWatermark: map['showWatermark'] ?? false,
    );
  }
}

/// إعدادات الشركة للقالب
class CompanySettings {
  final String name;
  final String address;
  final String phone;
  final String email;
  final String? website;
  final String? taxNumber;
  final String? registrationNumber;
  final String? logoPath;
  final bool showLogo;
  final bool showContactInfo;
  final bool showTaxInfo;

  const CompanySettings({
    required this.name,
    required this.address,
    required this.phone,
    required this.email,
    this.website,
    this.taxNumber,
    this.registrationNumber,
    this.logoPath,
    this.showLogo = true,
    this.showContactInfo = true,
    this.showTaxInfo = true,
  });

  /// إعدادات الشركة الافتراضية
  static const defaultSettings = CompanySettings(
    name: 'دفتر الأستاذ الذكي',
    address: 'سوريا - دمشق',
    phone: '+963-11-1234567',
    email: '<EMAIL>',
    website: 'www.smartledger.sy',
    taxNumber: '*********',
    registrationNumber: 'REG-2024-001',
    showLogo: true,
    showContactInfo: true,
    showTaxInfo: true,
  );

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'address': address,
      'phone': phone,
      'email': email,
      'website': website,
      'taxNumber': taxNumber,
      'registrationNumber': registrationNumber,
      'logoPath': logoPath,
      'showLogo': showLogo,
      'showContactInfo': showContactInfo,
      'showTaxInfo': showTaxInfo,
    };
  }

  factory CompanySettings.fromMap(Map<String, dynamic> map) {
    return CompanySettings(
      name: map['name'] ?? 'دفتر الأستاذ الذكي',
      address: map['address'] ?? 'سوريا - دمشق',
      phone: map['phone'] ?? '+963-11-1234567',
      email: map['email'] ?? '<EMAIL>',
      website: map['website'],
      taxNumber: map['taxNumber'],
      registrationNumber: map['registrationNumber'],
      logoPath: map['logoPath'],
      showLogo: map['showLogo'] ?? true,
      showContactInfo: map['showContactInfo'] ?? true,
      showTaxInfo: map['showTaxInfo'] ?? true,
    );
  }
}

/// نموذج قالب الطباعة الرئيسي
class InvoiceTemplate {
  final int? id;
  final String name;
  final InvoiceTemplateType type;
  final TemplateColors colors;
  final TemplateFonts fonts;
  final TemplateLayout layout;
  final CompanySettings companySettings;
  final AdvancedPrintSettings advancedSettings;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  InvoiceTemplate({
    this.id,
    required this.name,
    required this.type,
    required this.colors,
    required this.fonts,
    required this.layout,
    required this.companySettings,
    this.advancedSettings = AdvancedPrintSettings.defaultSettings,
    this.isDefault = false,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء قالب افتراضي حسب النوع
  factory InvoiceTemplate.createDefault(InvoiceTemplateType type) {
    switch (type) {
      case InvoiceTemplateType.classic:
        return InvoiceTemplate(
          name: 'قالب كلاسيكي',
          type: type,
          colors: TemplateColors.classic,
          fonts: TemplateFonts.classic,
          layout: TemplateLayout.classic,
          companySettings: CompanySettings.defaultSettings,
          isDefault: true,
        );
      case InvoiceTemplateType.modern:
        return InvoiceTemplate(
          name: 'قالب حديث',
          type: type,
          colors: TemplateColors.modern,
          fonts: TemplateFonts.modern,
          layout: TemplateLayout.modern,
          companySettings: CompanySettings.defaultSettings,
          isDefault: true,
        );
      case InvoiceTemplateType.minimal:
        return InvoiceTemplate(
          name: 'قالب مبسط',
          type: type,
          colors: TemplateColors.minimal,
          fonts: TemplateFonts.minimal,
          layout: TemplateLayout.minimal,
          companySettings: CompanySettings.defaultSettings,
          isDefault: true,
        );
      case InvoiceTemplateType.luxury:
        return InvoiceTemplate(
          name: 'قالب فاخر',
          type: type,
          colors: TemplateColors.luxury,
          fonts: TemplateFonts.luxury,
          layout: TemplateLayout.luxury,
          companySettings: CompanySettings.defaultSettings,
          isDefault: true,
        );
      case InvoiceTemplateType.custom:
        return InvoiceTemplate(
          name: 'قالب مخصص',
          type: type,
          colors: TemplateColors.classic,
          fonts: TemplateFonts.classic,
          layout: TemplateLayout.classic,
          companySettings: CompanySettings.defaultSettings,
          isDefault: false,
        );
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.code,
      'colors': colors.toMap(),
      'fonts': fonts.toMap(),
      'layout': layout.toMap(),
      'company_settings': companySettings.toMap(),
      'advanced_settings': advancedSettings.toMap(),
      'is_default': isDefault ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory InvoiceTemplate.fromMap(Map<String, dynamic> map) {
    return InvoiceTemplate(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      type: InvoiceTemplateType.fromCode(map['type'] ?? 'classic'),
      colors: TemplateColors.fromMap(
        map['colors'] is String
            ? {} // Handle string case - would need JSON decode in real implementation
            : map['colors'] ?? {},
      ),
      fonts: TemplateFonts.fromMap(
        map['fonts'] is String
            ? {} // Handle string case - would need JSON decode in real implementation
            : map['fonts'] ?? {},
      ),
      layout: TemplateLayout.fromMap(
        map['layout'] is String
            ? {} // Handle string case - would need JSON decode in real implementation
            : map['layout'] ?? {},
      ),
      companySettings: CompanySettings.fromMap(
        map['company_settings'] is String
            ? {} // Handle string case - would need JSON decode in real implementation
            : map['company_settings'] ?? {},
      ),
      advancedSettings: AdvancedPrintSettings.fromMap(
        map['advanced_settings'] is String
            ? {} // Handle string case - would need JSON decode in real implementation
            : map['advanced_settings'] ?? {},
      ),
      isDefault: (map['is_default'] ?? 0) == 1,
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  InvoiceTemplate copyWith({
    int? id,
    String? name,
    InvoiceTemplateType? type,
    TemplateColors? colors,
    TemplateFonts? fonts,
    TemplateLayout? layout,
    CompanySettings? companySettings,
    AdvancedPrintSettings? advancedSettings,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InvoiceTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      colors: colors ?? this.colors,
      fonts: fonts ?? this.fonts,
      layout: layout ?? this.layout,
      companySettings: companySettings ?? this.companySettings,
      advancedSettings: advancedSettings ?? this.advancedSettings,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'InvoiceTemplate(id: $id, name: $name, type: ${type.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceTemplate && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
