/// شاشة تتبع الدفعات الجزئية
/// تعرض جميع الفواتير ذات الدفعات الجزئية مع إمكانية إدارتها
library;

import 'package:flutter/material.dart';
import '../services/payment_tracking_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';
import '../screens/invoice_details_screen.dart';

class PartialPaymentsScreen extends StatefulWidget {
  const PartialPaymentsScreen({super.key});

  @override
  State<PartialPaymentsScreen> createState() => _PartialPaymentsScreenState();
}

class _PartialPaymentsScreenState extends State<PartialPaymentsScreen> {
  final PaymentTrackingService _trackingService = PaymentTrackingService();
  
  List<PartialPaymentTracker> _partialPayments = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _sortBy = 'date'; // date, amount, percentage
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final partialPayments = await _trackingService.getPartiallyPaidInvoices();
      final statistics = await _trackingService.getPartialPaymentStatistics();
      
      setState(() {
        _partialPayments = partialPayments;
        _statistics = statistics;
        _isLoading = false;
      });
      
      _sortData();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _sortData() {
    setState(() {
      _partialPayments.sort((a, b) {
        int comparison = 0;
        
        switch (_sortBy) {
          case 'date':
            comparison = (a.lastPaymentDate ?? DateTime(1900))
                .compareTo(b.lastPaymentDate ?? DateTime(1900));
            break;
          case 'amount':
            comparison = a.remainingAmount.compareTo(b.remainingAmount);
            break;
          case 'percentage':
            comparison = a.completionPercentage.compareTo(b.completionPercentage);
            break;
        }
        
        return _sortAscending ? comparison : -comparison;
      });
    });
  }

  void _changeSorting(String sortBy) {
    setState(() {
      if (_sortBy == sortBy) {
        _sortAscending = !_sortAscending;
      } else {
        _sortBy = sortBy;
        _sortAscending = false;
      }
    });
    _sortData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تتبع الدفعات الجزئية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Column(
              children: [
                _buildStatisticsCard(),
                _buildSortingControls(),
                Expanded(
                  child: _partialPayments.isEmpty
                      ? _buildEmptyState()
                      : _buildPartialPaymentsList(),
                ),
              ],
            ),
    );
  }

  Widget _buildStatisticsCard() {
    if (_statistics.isEmpty) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات الدفعات الجزئية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'عدد الفواتير',
                    '${_statistics['partiallyPaidCount'] ?? 0}',
                    Icons.receipt,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'إجمالي المتبقي',
                    '${(_statistics['totalRemainingAmount'] ?? 0.0).toStringAsFixed(0)} ل.س',
                    Icons.money_off,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'متوسط نسبة الدفع',
                    '${(_statistics['averagePaymentPercentage'] ?? 0.0).toStringAsFixed(1)}%',
                    Icons.pie_chart,
                    AppColors.success,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSortingControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Text(
            'ترتيب حسب:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                _buildSortButton('التاريخ', 'date'),
                _buildSortButton('المبلغ', 'amount'),
                _buildSortButton('النسبة', 'percentage'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortButton(String label, String sortKey) {
    final isSelected = _sortBy == sortKey;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(label),
            if (isSelected) ...[
              const SizedBox(width: 4),
              Icon(
                _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 16,
              ),
            ],
          ],
        ),
        selected: isSelected,
        onSelected: (_) => _changeSorting(sortKey),
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.payment_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فواتير بدفعات جزئية',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جميع الفواتير إما مدفوعة بالكامل أو غير مدفوعة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPartialPaymentsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _partialPayments.length,
      itemBuilder: (context, index) {
        final tracker = _partialPayments[index];
        return _buildPartialPaymentCard(tracker);
      },
    );
  }

  Widget _buildPartialPaymentCard(PartialPaymentTracker tracker) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToInvoiceDetails(tracker.invoiceId),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'فاتورة ${tracker.invoiceNumber}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'عدد الدفعات: ${tracker.paymentCount}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        if (tracker.lastPaymentDate != null)
                          Text(
                            'آخر دفعة: ${tracker.lastPaymentDate!.day}/${tracker.lastPaymentDate!.month}/${tracker.lastPaymentDate!.year}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor(tracker.completionPercentage).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${tracker.completionPercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _getStatusColor(tracker.completionPercentage),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildAmountInfo(
                      'إجمالي الفاتورة',
                      tracker.totalAmount,
                      AppColors.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildAmountInfo(
                      'المبلغ المدفوع',
                      tracker.paidAmount,
                      AppColors.success,
                    ),
                  ),
                  Expanded(
                    child: _buildAmountInfo(
                      'المبلغ المتبقي',
                      tracker.remainingAmount,
                      Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: tracker.completionPercentage / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getStatusColor(tracker.completionPercentage),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (tracker.daysSinceLastPayment != null && tracker.daysSinceLastPayment! > 30)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'متأخر ${tracker.daysSinceLastPayment} يوم',
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: () => _navigateToInvoiceDetails(tracker.invoiceId),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('عرض التفاصيل'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInfo(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(0)} ل.س',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getStatusColor(double percentage) {
    if (percentage >= 80) return AppColors.success;
    if (percentage >= 50) return Colors.orange;
    return Colors.red;
  }

  void _navigateToInvoiceDetails(int invoiceId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoiceDetailsScreen(invoiceId: invoiceId),
      ),
    ).then((_) => _loadData()); // تحديث البيانات عند العودة
  }
}
