import 'package:flutter/material.dart';
import 'responsive_layout.dart';
import 'app_dimensions.dart';
import 'responsive_widgets.dart';

/// تخطيطات محسنة خصيصاً للأجهزة اللوحية
/// تستغل المساحة الإضافية بشكل أفضل وتوفر تجربة مستخدم محسنة

/// تخطيط رئيسي للأجهزة اللوحية مع شريط جانبي قابل للطي
class TabletMasterDetailLayout extends StatefulWidget {
  final Widget sidebar;
  final Widget content;
  final double sidebarWidth;
  final bool initialSidebarVisible;
  final String title;

  const TabletMasterDetailLayout({
    super.key,
    required this.sidebar,
    required this.content,
    this.sidebarWidth = 300,
    this.initialSidebarVisible = true,
    this.title = '',
  });

  @override
  State<TabletMasterDetailLayout> createState() =>
      _TabletMasterDetailLayoutState();
}

class _TabletMasterDetailLayoutState extends State<TabletMasterDetailLayout>
    with SingleTickerProviderStateMixin {
  late bool _sidebarVisible;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _sidebarVisible = widget.initialSidebarVisible;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation =
        Tween<double>(
          begin: _sidebarVisible ? 1.0 : 0.0,
          end: _sidebarVisible ? 1.0 : 0.0,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
          ),
        );

    if (_sidebarVisible) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleSidebar() {
    setState(() {
      _sidebarVisible = !_sidebarVisible;
      if (_sidebarVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final dimensions = context.dimensions;

    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText.h3(widget.title),
        leading: IconButton(
          icon: AnimatedRotation(
            turns: _sidebarVisible ? 0.0 : 0.5,
            duration: const Duration(milliseconds: 300),
            child: const ResponsiveIcon.medium(Icons.menu),
          ),
          onPressed: _toggleSidebar,
          tooltip: _sidebarVisible
              ? 'إخفاء الشريط الجانبي'
              : 'إظهار الشريط الجانبي',
        ),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Row(
        children: [
          // الشريط الجانبي
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return SizedBox(
                width: widget.sidebarWidth * _slideAnimation.value,
                child: _slideAnimation.value > 0.1
                    ? Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(2, 0),
                            ),
                          ],
                        ),
                        child: widget.sidebar,
                      )
                    : null,
              );
            },
          ),

          // المحتوى الرئيسي
          Expanded(
            child: ResponsiveContainer(
              padding: EdgeInsets.all(dimensions.paddingM),
              child: widget.content,
            ),
          ),
        ],
      ),
    );
  }
}

/// تخطيط شبكة محسن للأجهزة اللوحية
class TabletGridLayout extends StatelessWidget {
  final List<Widget> items;
  final int? columns;
  final double? spacing;
  final double? aspectRatio;
  final EdgeInsets? padding;
  final bool showHeaders;
  final String? title;
  final List<Widget>? actions;

  const TabletGridLayout({
    super.key,
    required this.items,
    this.columns,
    this.spacing,
    this.aspectRatio,
    this.padding,
    this.showHeaders = true,
    this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = context.dimensions;
    final effectiveColumns = columns ?? (context.isTablet ? 3 : 2);
    final effectiveSpacing = spacing ?? dimensions.gridSpacing;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingM);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showHeaders) ...[
          Padding(
            padding: effectivePadding,
            child: Row(
              children: [
                if (title != null) ...[
                  Expanded(
                    child: ResponsiveText.h2(
                      title!,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ],
                if (actions != null) ...actions!,
              ],
            ),
          ),
          ResponsiveSpacing.medium(),
        ],

        Expanded(
          child: Padding(
            padding: effectivePadding,
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: effectiveColumns,
                crossAxisSpacing: effectiveSpacing,
                mainAxisSpacing: effectiveSpacing,
                childAspectRatio: aspectRatio ?? dimensions.cardAspectRatio,
              ),
              itemCount: items.length,
              itemBuilder: (context, index) => items[index],
            ),
          ),
        ),
      ],
    );
  }
}

/// تخطيط قائمة محسن للأجهزة اللوحية مع تفاصيل جانبية
class TabletListDetailLayout extends StatefulWidget {
  final List<Widget> listItems;
  final Widget Function(int index)? detailBuilder;
  final Widget? emptyDetail;
  final String? title;
  final double listWidth;

  const TabletListDetailLayout({
    super.key,
    required this.listItems,
    this.detailBuilder,
    this.emptyDetail,
    this.title,
    this.listWidth = 350,
  });

  @override
  State<TabletListDetailLayout> createState() => _TabletListDetailLayoutState();
}

class _TabletListDetailLayoutState extends State<TabletListDetailLayout> {
  int? _selectedIndex;

  @override
  Widget build(BuildContext context) {
    final dimensions = context.dimensions;

    return Row(
      children: [
        // قائمة العناصر
        Container(
          width: widget.listWidth,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.title != null) ...[
                Padding(
                  padding: EdgeInsets.all(dimensions.paddingM),
                  child: ResponsiveText.h3(
                    widget.title!,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const Divider(),
              ],

              Expanded(
                child: ListView.builder(
                  itemCount: widget.listItems.length,
                  itemBuilder: (context, index) {
                    final isSelected = _selectedIndex == index;
                    return Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primaryContainer
                            : null,
                      ),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _selectedIndex = index;
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.all(dimensions.paddingS),
                          child: widget.listItems[index],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // منطقة التفاصيل
        Expanded(
          child: ResponsiveContainer(
            padding: EdgeInsets.all(dimensions.paddingL),
            child: _selectedIndex != null && widget.detailBuilder != null
                ? widget.detailBuilder!(_selectedIndex!)
                : widget.emptyDetail ??
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ResponsiveIcon.extraLarge(
                              Icons.touch_app,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                            ResponsiveSpacing.medium(),
                            ResponsiveText.body(
                              'اختر عنصراً من القائمة لعرض التفاصيل',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.outline,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
          ),
        ),
      ],
    );
  }
}

/// تخطيط نموذج محسن للأجهزة اللوحية
class TabletFormLayout extends StatelessWidget {
  final List<Widget> formFields;
  final List<Widget>? actions;
  final String? title;
  final String? subtitle;
  final int columns;
  final EdgeInsets? padding;

  const TabletFormLayout({
    super.key,
    required this.formFields,
    this.actions,
    this.title,
    this.subtitle,
    this.columns = 2,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = context.dimensions;
    final effectivePadding = padding ?? EdgeInsets.all(dimensions.paddingL);

    return ResponsiveContainer(
      maxWidth: dimensions.maxFormWidth,
      padding: effectivePadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            ResponsiveText.h2(
              title!,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            if (subtitle != null) ...[
              ResponsiveSpacing.small(),
              ResponsiveText.body(
                subtitle!,
                style: TextStyle(color: Theme.of(context).colorScheme.outline),
              ),
            ],
            ResponsiveSpacing.large(),
          ],

          Expanded(child: SingleChildScrollView(child: _buildFormGrid())),

          if (actions != null) ...[
            ResponsiveSpacing.large(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions!
                  .expand(
                    (action) => [
                      action,
                      if (action != actions!.last)
                        ResponsiveSpacing.medium(isVertical: false),
                    ],
                  )
                  .toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFormGrid() {
    if (columns == 1) {
      return Column(
        children: formFields
            .expand((field) => [field, ResponsiveSpacing.medium()])
            .toList(),
      );
    }

    final rows = <Widget>[];
    for (int i = 0; i < formFields.length; i += columns) {
      final rowFields = formFields.skip(i).take(columns).toList();

      rows.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: rowFields
              .expand(
                (field) => [
                  Expanded(child: field),
                  if (field != rowFields.last)
                    ResponsiveSpacing.medium(isVertical: false),
                ],
              )
              .toList(),
        ),
      );

      if (i + columns < formFields.length) {
        rows.add(ResponsiveSpacing.medium());
      }
    }

    return Column(children: rows);
  }
}
