import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import '../constants/accessibility_constants.dart';

/// خدمة إمكانية الوصول
/// تدير جميع وظائف دعم قارئ الشاشة والتنقل بلوحة المفاتيح
class AccessibilityService {
  static final AccessibilityService _instance = AccessibilityService._internal();
  factory AccessibilityService() => _instance;
  AccessibilityService._internal();

  // ===============================
  // إعدادات إمكانية الوصول
  // ===============================

  /// التحقق من تفعيل قارئ الشاشة
  static bool get isScreenReaderEnabled {
    return WidgetsBinding.instance.platformDispatcher.accessibilityFeatures.accessibleNavigation;
  }

  /// التحقق من تفعيل النص الكبير
  static bool get isLargeTextEnabled {
    return WidgetsBinding.instance.platformDispatcher.accessibilityFeatures.boldText;
  }

  /// التحقق من تفعيل التباين العالي
  static bool get isHighContrastEnabled {
    return WidgetsBinding.instance.platformDispatcher.accessibilityFeatures.highContrast;
  }

  /// التحقق من تفعيل تقليل الحركة
  static bool get isReduceMotionEnabled {
    return WidgetsBinding.instance.platformDispatcher.accessibilityFeatures.disableAnimations;
  }

  // ===============================
  // دوال إنشاء Semantic Widgets
  // ===============================

  /// إنشاء زر مع semantic labels
  static Widget createSemanticButton({
    required Widget child,
    required VoidCallback onPressed,
    required String label,
    String? hint,
    String? tooltip,
    bool enabled = true,
  }) {
    return Semantics(
      button: true,
      enabled: enabled,
      label: label,
      hint: hint,
      onTap: enabled ? onPressed : null,
      child: Tooltip(
        message: tooltip ?? label,
        child: child,
      ),
    );
  }

  /// إنشاء حقل نص مع semantic labels
  static Widget createSemanticTextField({
    required Widget child,
    required String label,
    String? hint,
    String? value,
    bool required = false,
  }) {
    return Semantics(
      textField: true,
      label: required ? '$label (مطلوب)' : label,
      hint: hint,
      value: value,
      child: child,
    );
  }

  /// إنشاء عنوان مع semantic labels
  static Widget createSemanticHeader({
    required Widget child,
    required String label,
    int level = 1,
  }) {
    return Semantics(
      header: true,
      label: label,
      child: child,
    );
  }

  /// إنشاء قائمة مع semantic labels
  static Widget createSemanticList({
    required Widget child,
    required String label,
    int? itemCount,
    String? description,
  }) {
    final fullDescription = itemCount != null 
        ? AccessibilityConstants.buildListDescription(label, itemCount)
        : description ?? label;
    
    return Semantics(
      label: label,
      hint: fullDescription,
      child: child,
    );
  }

  /// إنشاء بطاقة مع semantic labels
  static Widget createSemanticCard({
    required Widget child,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return Semantics(
      button: onTap != null,
      label: AccessibilityConstants.buildCardLabel('', title),
      hint: subtitle,
      onTap: onTap,
      child: child,
    );
  }

  /// إنشاء فلتر مع semantic labels
  static Widget createSemanticFilter({
    required Widget child,
    required String filterType,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Semantics(
      button: true,
      selected: isSelected,
      label: AccessibilityConstants.buildFilterLabel(filterType, isSelected),
      hint: isSelected 
          ? 'مُحدد حالياً' 
          : AccessibilityConstants.buildActionHint('للتصفية حسب', filterType),
      onTap: onTap,
      child: child,
    );
  }

  // ===============================
  // دوال الإعلانات الصوتية
  // ===============================

  /// إعلان رسالة للمستخدم
  static void announce(String message, {Assertiveness assertiveness = Assertiveness.polite}) {
    SemanticsService.announce(message, TextDirection.rtl, assertiveness: assertiveness);
  }

  /// إعلان نجاح العملية
  static void announceSuccess(String operation) {
    announce('تم $operation بنجاح', assertiveness: Assertiveness.polite);
  }

  /// إعلان خطأ
  static void announceError(String error) {
    announce('خطأ: $error', assertiveness: Assertiveness.assertive);
  }

  /// إعلان تحميل البيانات
  static void announceLoading(String data) {
    announce('جاري تحميل $data', assertiveness: Assertiveness.polite);
  }

  /// إعلان اكتمال التحميل
  static void announceLoadingComplete(String data, int count) {
    announce('تم تحميل $count من $data', assertiveness: Assertiveness.polite);
  }

  /// إعلان تغيير الشاشة
  static void announceScreenChange(String screenName) {
    announce('تم الانتقال إلى $screenName', assertiveness: Assertiveness.polite);
  }

  // ===============================
  // دوال إدارة التركيز
  // ===============================

  /// تحديد التركيز على عنصر
  static void focusNode(FocusNode focusNode) {
    if (focusNode.canRequestFocus) {
      focusNode.requestFocus();
    }
  }

  /// الانتقال للعنصر التالي
  static void focusNext(BuildContext context) {
    FocusScope.of(context).nextFocus();
  }

  /// الانتقال للعنصر السابق
  static void focusPrevious(BuildContext context) {
    FocusScope.of(context).previousFocus();
  }

  /// إزالة التركيز
  static void unfocus(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  // ===============================
  // دوال مساعدة للتحقق من الحالة
  // ===============================

  /// التحقق من إمكانية الوصول للعنصر
  static bool isAccessible(Widget widget) {
    // فحص أساسي - يمكن تطويره أكثر
    return widget is Semantics || 
           widget is Material || 
           widget is InkWell ||
           widget is GestureDetector;
  }

  /// بناء وصف شامل للعنصر
  static String buildFullDescription({
    required String label,
    String? hint,
    String? value,
    bool? selected,
    bool? enabled,
  }) {
    final parts = <String>[label];
    
    if (value != null && value.isNotEmpty) {
      parts.add('القيمة: $value');
    }
    
    if (selected != null) {
      parts.add(selected ? AccessibilityConstants.selectedState : AccessibilityConstants.unselectedState);
    }
    
    if (enabled != null && !enabled) {
      parts.add(AccessibilityConstants.disabledState);
    }
    
    if (hint != null && hint.isNotEmpty) {
      parts.add(hint);
    }
    
    return parts.join(' - ');
  }

  // ===============================
  // دوال التحقق من صحة إمكانية الوصول
  // ===============================

  /// التحقق من وجود semantic labels مناسبة
  static bool validateSemanticLabels(Widget widget) {
    // هذه دالة أساسية للتحقق - يمكن تطويرها
    if (widget is Semantics) {
      final semantics = widget.properties;
      return semantics.label != null || semantics.hint != null;
    }
    return false;
  }

  /// إنشاء تقرير إمكانية الوصول
  static Map<String, dynamic> generateAccessibilityReport() {
    return {
      'screen_reader_enabled': isScreenReaderEnabled,
      'large_text_enabled': isLargeTextEnabled,
      'high_contrast_enabled': isHighContrastEnabled,
      'reduce_motion_enabled': isReduceMotionEnabled,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  // ===============================
  // دوال التخصيص حسب الإعدادات
  // ===============================

  /// الحصول على مدة الرسوم المتحركة المناسبة
  static Duration getAnimationDuration(Duration defaultDuration) {
    return isReduceMotionEnabled ? Duration.zero : defaultDuration;
  }

  /// الحصول على حجم النص المناسب
  static double getTextSize(double defaultSize) {
    return isLargeTextEnabled ? defaultSize * 1.3 : defaultSize;
  }

  /// الحصول على التباين المناسب للألوان
  static Color getContrastColor(Color defaultColor, Color backgroundColor) {
    if (!isHighContrastEnabled) return defaultColor;
    
    // حساب التباين وإرجاع لون مناسب
    final luminance = defaultColor.computeLuminance();
    final backgroundLuminance = backgroundColor.computeLuminance();
    
    if ((luminance - backgroundLuminance).abs() < 0.5) {
      return luminance > 0.5 ? Colors.black : Colors.white;
    }
    
    return defaultColor;
  }
}
