# دليل اختبار إمكانية الوصول - Smart Ledger

## 📋 نظرة عامة

هذا الدليل يوضح كيفية اختبار ميزات إمكانية الوصول في تطبيق Smart Ledger للتأكد من أنه يمكن استخدامه من قبل الأشخاص ذوي الاحتياجات الخاصة.

## 🎯 أهداف الاختبار

### 1. **التأكد من دعم قارئ الشاشة**
- جميع العناصر لها semantic labels مناسبة
- النصوص تُقرأ بشكل صحيح
- الإعلانات الصوتية تعمل بشكل مناسب

### 2. **التأكد من إمكانية التنقل بلوحة المفاتيح**
- جميع العناصر التفاعلية قابلة للوصول
- ترتيب التنقل منطقي
- اختصارات لوحة المفاتيح تعمل

### 3. **التأكد من التباين والألوان**
- التباين كافي للقراءة
- المعلومات لا تعتمد على اللون فقط
- دعم الوضع عالي التباين

### 4. **التأكد من أحجام العناصر**
- أهداف اللمس كبيرة بما فيه الكفاية
- النصوص قابلة للقراءة
- دعم تكبير النص

## 🧪 أنواع الاختبارات

### 1. اختبارات آلية (Automated Tests)

#### تشغيل الاختبارات:
```bash
flutter test test/accessibility_test.dart
```

#### الاختبارات المتضمنة:
- **اختبار Semantic Labels**: التحقق من وجود تسميات مناسبة
- **اختبار التنقل**: التحقق من إمكانية التنقل بلوحة المفاتيح
- **اختبار التباين**: التحقق من تباين الألوان
- **اختبار أحجام العناصر**: التحقق من أحجام أهداف اللمس
- **اختبار الأداء**: التحقق من الأداء مع تفعيل إمكانية الوصول

### 2. اختبارات يدوية (Manual Tests)

#### أ. اختبار قارئ الشاشة

**على Android (TalkBack):**
1. فتح الإعدادات > إمكانية الوصول > TalkBack
2. تفعيل TalkBack
3. فتح التطبيق والتنقل بين العناصر
4. التحقق من قراءة النصوص بشكل صحيح

**على iOS (VoiceOver):**
1. فتح الإعدادات > إمكانية الوصول > VoiceOver
2. تفعيل VoiceOver
3. فتح التطبيق والتنقل بين العناصر
4. التحقق من قراءة النصوص بشكل صحيح

#### ب. اختبار التنقل بلوحة المفاتيح

**الخطوات:**
1. توصيل لوحة مفاتيح خارجية (للأجهزة المحمولة)
2. فتح التطبيق
3. استخدام Tab للتنقل بين العناصر
4. استخدام Enter/Space لتفعيل العناصر
5. استخدام اختصارات لوحة المفاتيح المخصصة

**الاختصارات المدعومة:**
- `Tab`: الانتقال للعنصر التالي
- `Shift+Tab`: الانتقال للعنصر السابق
- `Enter/Space`: تفعيل العنصر
- `Escape`: إغلاق الحوار أو العودة
- `Ctrl+S`: حفظ (في النماذج)
- `Ctrl+N`: إضافة جديد
- `F2`: تعديل
- `Delete`: حذف

#### ج. اختبار التباين والألوان

**الخطوات:**
1. تفعيل الوضع عالي التباين في النظام
2. فتح التطبيق
3. التحقق من وضوح النصوص والعناصر
4. التحقق من عدم اعتماد المعلومات على اللون فقط

#### د. اختبار تكبير النص

**الخطوات:**
1. زيادة حجم النص في إعدادات النظام
2. فتح التطبيق
3. التحقق من تكيف التطبيق مع النص الكبير
4. التحقق من عدم تداخل العناصر

## 📱 اختبارات خاصة بالشاشات

### 1. الشاشة الرئيسية

**العناصر المطلوب اختبارها:**
- [ ] شعار التطبيق
- [ ] عنوان التطبيق
- [ ] بطاقات لوحة التحكم (6 بطاقات)
- [ ] الإحصائيات السريعة
- [ ] التنقل بين البطاقات

**السيناريوهات:**
1. تفعيل قارئ الشاشة والتنقل بين العناصر
2. استخدام لوحة المفاتيح للتنقل
3. النقر على البطاقات والتحقق من الانتقال

### 2. شاشة دليل الحسابات

**العناصر المطلوب اختبارها:**
- [ ] شريط البحث
- [ ] فلاتر أنواع الحسابات
- [ ] قائمة الحسابات
- [ ] أزرار الإضافة والتحديث

**السيناريوهات:**
1. البحث في الحسابات باستخدام قارئ الشاشة
2. تطبيق الفلاتر والاستماع للإعلانات
3. التنقل في قائمة الحسابات

### 3. شاشة إضافة الحساب

**العناصر المطلوب اختبارها:**
- [ ] حقول النموذج (الكود، الاسم، الوصف)
- [ ] قائمة أنواع الحسابات
- [ ] قائمة الحسابات الأب
- [ ] أزرار الحفظ والإلغاء

**السيناريوهات:**
1. ملء النموذج باستخدام لوحة المفاتيح
2. التنقل بين الحقول باستخدام Tab
3. استخدام اختصارات الحفظ والإلغاء
4. التحقق من رسائل الخطأ

## 🔧 أدوات الاختبار

### 1. أدوات Flutter المدمجة

```dart
// اختبار semantic labels
expect(find.bySemanticsLabel('تسمية العنصر'), findsOneWidget);

// اختبار التركيز
await tester.sendKeyEvent(LogicalKeyboardKey.tab);
expect(tester.binding.focusManager.primaryFocus, isNotNull);

// اختبار الاختصارات
await tester.sendKeyDownEvent(LogicalKeyboardKey.controlLeft);
await tester.sendKeyEvent(LogicalKeyboardKey.keyS);
await tester.sendKeyUpEvent(LogicalKeyboardKey.controlLeft);
```

### 2. أدوات خارجية

**Accessibility Scanner (Android):**
- تطبيق من Google لفحص إمكانية الوصول
- يكتشف مشاكل التباين وأحجام العناصر

**Accessibility Inspector (iOS):**
- أداة في Xcode لفحص إمكانية الوصول
- تظهر معلومات VoiceOver

## 📊 معايير النجاح

### 1. معايير WCAG 2.1

**المستوى A:**
- [ ] جميع الصور لها نص بديل
- [ ] جميع العناصر التفاعلية قابلة للوصول بلوحة المفاتيح
- [ ] التباين 3:1 على الأقل للنصوص الكبيرة

**المستوى AA:**
- [ ] التباين 4.5:1 للنصوص العادية
- [ ] أهداف اللمس 44x44 بكسل على الأقل
- [ ] دعم تكبير النص حتى 200%

### 2. معايير المنصة

**Android:**
- [ ] دعم TalkBack
- [ ] دعم Switch Access
- [ ] دعم Voice Access

**iOS:**
- [ ] دعم VoiceOver
- [ ] دعم Switch Control
- [ ] دعم Voice Control

## 🐛 مشاكل شائعة وحلولها

### 1. عدم قراءة العناصر

**المشكلة:** قارئ الشاشة لا يقرأ عنصر معين
**الحل:** إضافة Semantics widget مع label مناسب

```dart
Semantics(
  label: 'وصف العنصر',
  child: YourWidget(),
)
```

### 2. ترتيب التنقل خاطئ

**المشكلة:** التنقل بـ Tab لا يتبع ترتيب منطقي
**الحل:** استخدام FocusTraversalGroup مع ترتيب مخصص

```dart
FocusTraversalGroup(
  policy: OrderedTraversalPolicy(),
  child: YourWidget(),
)
```

### 3. التباين ضعيف

**المشكلة:** النص غير واضح على الخلفية
**الحل:** تحسين ألوان النص والخلفية

```dart
Text(
  'النص',
  style: TextStyle(
    color: Colors.black87, // تباين أفضل
  ),
)
```

## 📝 تقرير الاختبار

### نموذج تقرير:

```
تاريخ الاختبار: [التاريخ]
المختبر: [الاسم]
الإصدار: [رقم الإصدار]

النتائج:
✅ Semantic Labels: نجح
✅ التنقل بلوحة المفاتيح: نجح
⚠️ التباين: يحتاج تحسين
✅ أحجام العناصر: نجح

المشاكل المكتشفة:
1. تباين ضعيف في بعض النصوص الثانوية
2. عدم وجود semantic label لأيقونة البحث

التوصيات:
1. تحسين تباين النصوص الثانوية
2. إضافة semantic labels للأيقونات
```

## 🚀 التحسينات المستقبلية

1. **دعم المزيد من اللغات** في قارئ الشاشة
2. **تخصيص سرعة القراءة** حسب تفضيل المستخدم
3. **إضافة المزيد من الاختصارات** للوظائف المتقدمة
4. **تحسين دعم الأجهزة المساعدة** الخارجية
5. **إضافة وضع التباين العالي** المخصص
